# Module User - <PERSON><PERSON><PERSON> trúc Chi tiết

## Tổng quan
Module User quản lý người dùng trong hệ thống, bao gồ<PERSON> đăng ký, <PERSON><PERSON><PERSON> thực, <PERSON><PERSON><PERSON> quyền, và quản lý thông tin cá nhân.

## Cấu trúc thư mục

```
modules/core/user/
├── module.go                               # Khai báo User module
│
├── api/                                    # API layer
│   ├── handler.go                          # Main user handlers
│   ├── auth_handler.go                     # Authentication handlers
│   ├── profile_handler.go                  # Profile management handlers
│   ├── session_handler.go                  # Session management handlers
│   ├── activity_handler.go                 # User activity handlers
│   ├── middleware.go                       # User-specific middleware
│   └── router.go                           # User routes
│
├── service/                                 # Business logic layer
│   ├── entity.go                            # Base user entities & interfaces
│   ├── user.go                             # User entity & business logic
│   ├── profile.go                          # User profile entity & logic
│   ├── session.go                          # User session entity & logic
│   ├── activity.go                         # User activity entity & logic
│   ├── verification.go                     # Email/Phone verification entity
│   ├── service.go                          # Service interfaces
│   ├── user_service.go                     # User business service
│   ├── auth_service.go                     # Authentication service
│   ├── profile_service.go                  # Profile service
│   ├── session_service.go                  # Session management service
│   └── activity_service.go                 # Activity tracking service
│
├── repository/                             # Data access layer
│   ├── repository.go                       # User repository interfaces
│   ├── user_repository.go                  # User repository interface
│   ├── profile_repository.go               # Profile repository interface
│   ├── session_repository.go               # Session repository interface
│   ├── activity_repository.go              # Activity repository interface
│   ├── mysql/                              # MySQL implementations
│   │   ├── user_repository.go              # User MySQL repository
│   │   ├── profile_repository.go           # Profile MySQL repository
│   │   ├── session_repository.go           # Session MySQL repository
│   │   └── activity_repository.go          # Activity MySQL repository
│   └── cache/                              # Cache implementations
│       ├── user_cache.go                   # User caching layer
│       └── session_cache.go                # Session caching layer
│
├── dto/                                    # Data Transfer Objects
│   ├── user_dto.go                         # User DTOs
│   ├── auth_dto.go                         # Authentication DTOs
│   ├── profile_dto.go                      # Profile DTOs
│   ├── session_dto.go                      # Session DTOs
│   ├── activity_dto.go                     # Activity DTOs
│   └── filter_dto.go                       # Query/filter DTOs
│
├── migrations/                             # Database migrations
│   ├── 001_create_users_table.sql          # Users table
│   ├── 002_create_user_profiles_table.sql  # User profiles table
│   ├── 003_create_user_sessions_table.sql  # User sessions table
│   ├── 004_create_user_activities_table.sql # User activities table
│   └── 005_create_user_verifications_table.sql # Email/Phone verification
│
├── events/                                 # Event handling
│   ├── publisher.go                        # User event publisher
│   └── subscriber.go                       # User event subscriber
│
├── validators/                             # Input validation
│   ├── user_validator.go                   # User data validation
│   ├── auth_validator.go                   # Authentication validation
│   └── profile_validator.go                # Profile validation
│
└── config/                                 # Configuration
    └── default.yaml                        # Default user configuration
```

## User Service Entities

### 1. User Entity
```
User:
- ID (Primary Key)
- TenantID (Foreign Key)
- WebsiteID (Foreign Key, NULL for tenant-wide users)
- Username (Unique per tenant/website)
- Email (Unique per tenant/website)
- EmailVerified (Boolean)
- Phone (Phone number)
- PhoneVerified (Boolean)
- PasswordHash (Hashed password)
- Status (Active, Inactive, Suspended, Pending, Locked)
- Type (Super Admin, Tenant Admin, Website Admin, User, Guest)
- LastLoginAt (Last login timestamp)
- LoginCount (Total login count)
- FailedLoginAttempts (Failed login attempts)
- LockedUntil (Account lock expiry)
- MustChangePassword (Force password change on next login)
- TwoFactorEnabled (2FA status)
- TwoFactorSecret (2FA secret key)
- CreatedAt
- UpdatedAt
- DeletedAt (Soft delete)
```

### 2. User Profile Model
```
UserProfile:
- ID (Primary Key)
- UserID (Foreign Key, One-to-One)
- FirstName
- LastName
- MiddleName
- DisplayName
- Avatar (Profile picture URL)
- DateOfBirth
- Gender (Male, Female, Other)
- Bio (User biography)
- Language (Preferred language)
- Timezone (User timezone)
- Country (Country code)
- City
- Address
- PostalCode
- CompanyName
- JobTitle
- Website
- SocialLinks (JSON: Facebook, Twitter, LinkedIn, etc.)
- Preferences (JSON: Theme, notifications, etc.)
- Metadata (JSON: Additional custom fields)
- CreatedAt
- UpdatedAt
```

### 3. User Session Model
```
UserSession:
- ID (Primary Key)
- UserID (Foreign Key)
- SessionToken (Unique session identifier)
- RefreshToken (Refresh token)
- DeviceInfo (JSON: Device information)
- IPAddress (Client IP address)
- UserAgent (Client user agent)
- Location (JSON: Geographic location)
- IsActive (Session status)
- ExpiresAt (Session expiry)
- LastAccessedAt (Last accessed timestamp)
- CreatedAt
- UpdatedAt

UserDevice:
- ID (Primary Key)
- UserID (Foreign Key)
- DeviceID (Unique device identifier)
- DeviceName (User-friendly device name)
- DeviceType (Web, Mobile, Desktop)
- Platform (iOS, Android, Windows, Mac, Linux)
- PushToken (For push notifications)
- IsActive
- LastSeenAt
- CreatedAt
- UpdatedAt
```

### 4. User Activity Model
```
UserActivity:
- ID (Primary Key)
- UserID (Foreign Key, NULL for anonymous)
- TenantID (Foreign Key)
- WebsiteID (Foreign Key)
- SessionID (Foreign Key, optional)
- Action (Login, Logout, Create, Update, Delete, View, etc.)
- Resource (User, Post, Product, Order, etc.)
- ResourceID (ID of the resource)
- Description (Human-readable description)
- Details (JSON: Additional details)
- IPAddress
- UserAgent
- Location (JSON: Geographic location)
- CreatedAt

UserLoginHistory:
- ID (Primary Key)
- UserID (Foreign Key)
- LoginType (Password, OAuth, 2FA, etc.)
- Status (Success, Failed, Blocked)
- IPAddress
- UserAgent
- Location (JSON)
- FailureReason (If failed)
- CreatedAt
```

### 5. User Verification Model
```
UserVerification:
- ID (Primary Key)
- UserID (Foreign Key)
- Type (Email, Phone, Identity)
- Code (Verification code)
- Token (Verification token)
- ExpiresAt
- VerifiedAt
- AttemptCount
- MaxAttempts
- IsUsed
- CreatedAt
- UpdatedAt

UserPasswordReset:
- ID (Primary Key)
- UserID (Foreign Key)
- Token (Reset token)
- ExpiresAt
- IsUsed
- IPAddress
- UserAgent
- CreatedAt
- UpdatedAt
```

## Database Schema

### User Tables
```sql
-- =============================================
-- Users Table (Simplified)
-- =============================================
CREATE TABLE users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    tenant_id BIGINT NOT NULL,
    website_id BIGINT NULL,
    username VARCHAR(50) NOT NULL,
    email VARCHAR(255) NOT NULL,
    email_verified BOOLEAN DEFAULT FALSE,
    email_verified_at TIMESTAMP NULL,
    phone VARCHAR(20) NULL,
    phone_verified BOOLEAN DEFAULT FALSE,
    phone_verified_at TIMESTAMP NULL,
    password_hash VARCHAR(255) NOT NULL,
    status ENUM('active', 'inactive', 'suspended', 'pending', 'locked') DEFAULT 'pending',
    type ENUM('super_admin', 'tenant_admin', 'website_admin', 'user', 'guest') DEFAULT 'user',
    last_login_at TIMESTAMP NULL,
    login_count INT DEFAULT 0,
    failed_login_attempts INT DEFAULT 0,
    locked_until TIMESTAMP NULL,
    must_change_password BOOLEAN DEFAULT FALSE,
    two_factor_enabled BOOLEAN DEFAULT FALSE,
    two_factor_secret VARCHAR(32) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    
    UNIQUE KEY unique_username_per_scope (tenant_id, website_id, username, deleted_at),
    UNIQUE KEY unique_email_per_scope (tenant_id, website_id, email, deleted_at),
    INDEX idx_tenant_users (tenant_id),
    INDEX idx_website_users (website_id),
    INDEX idx_user_status (status),
    INDEX idx_user_type (type),
    INDEX idx_user_email (email),
    
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE
);

-- =============================================
-- User Profiles Table
-- =============================================
CREATE TABLE user_profiles (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    first_name VARCHAR(50) NULL,
    middle_name VARCHAR(50) NULL,
    last_name VARCHAR(50) NULL,
    display_name VARCHAR(100) NULL,
    avatar VARCHAR(500) NULL,
    date_of_birth DATE NULL,
    gender ENUM('male', 'female', 'other') NULL,
    bio TEXT NULL,
    language VARCHAR(10) DEFAULT 'en',
    timezone VARCHAR(50) DEFAULT 'UTC',
    country VARCHAR(2) NULL,
    city VARCHAR(100) NULL,
    address TEXT NULL,
    postal_code VARCHAR(20) NULL,
    company_name VARCHAR(100) NULL,
    job_title VARCHAR(100) NULL,
    website VARCHAR(255) NULL,
    social_links JSON NULL,
    preferences JSON NULL,
    metadata JSON NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_user_profile (user_id),
    INDEX idx_profile_name (first_name, last_name),
    INDEX idx_profile_display_name (display_name),
    INDEX idx_profile_country (country),
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

## API Endpoints

### Authentication APIs
```
POST   /api/auth/register                 # User registration
POST   /api/auth/login                    # User login
POST   /api/auth/logout                   # User logout
POST   /api/auth/refresh                  # Refresh token
POST   /api/auth/forgot-password          # Forgot password
POST   /api/auth/reset-password           # Reset password
POST   /api/auth/verify-email             # Verify email
POST   /api/auth/verify-phone             # Verify phone
POST   /api/auth/enable-2fa               # Enable 2FA
POST   /api/auth/disable-2fa              # Disable 2FA
```

### User Management APIs
```
GET    /api/users                         # List users
POST   /api/users                         # Create user
GET    /api/users/{id}                    # Get user details
PUT    /api/users/{id}                    # Update user
DELETE /api/users/{id}                    # Delete user
POST   /api/users/{id}/suspend            # Suspend user
POST   /api/users/{id}/activate           # Activate user
POST   /api/users/{id}/unlock             # Unlock user
```

### Profile Management APIs
```
GET    /api/users/{id}/profile            # Get user profile
PUT    /api/users/{id}/profile            # Update user profile
POST   /api/users/{id}/avatar             # Upload avatar
DELETE /api/users/{id}/avatar             # Delete avatar
```

### Session Management APIs
```
GET    /api/users/{id}/sessions           # Get user sessions
DELETE /api/users/{id}/sessions/{sessionId} # Terminate session
DELETE /api/users/{id}/sessions           # Terminate all sessions
```

### Activity Tracking APIs
```
GET    /api/users/{id}/activities         # Get user activities
GET    /api/users/{id}/login-history      # Get login history
```

## Service Integration Pattern

### User Service with RBAC Integration
```go
type UserService struct {
    userRepo         repository.UserRepository
    rbacService      rbac.AuthorizationService
    activityService  ActivityService
}

// Check if user can perform action before executing
func (s *UserService) UpdateUser(ctx context.Context, userID int64, updates *UpdateUserRequest) error {
    // Check authorization using RBAC service
    canUpdate, err := s.rbacService.HasPermission(ctx, userID, "user.update")
    if err != nil {
        return err
    }
    if !canUpdate {
        return errors.New("insufficient permissions")
    }
    
    // Perform the update
    err = s.userRepo.Update(ctx, userID, updates)
    if err != nil {
        return err
    }
    
    // Log activity
    s.activityService.LogActivity(ctx, userID, "user.update", "user", userID, updates)
    
    return nil
}
```

## Best Practices

1. **Security**: Always hash passwords, implement rate limiting for authentication attempts
2. **Validation**: Validate all user input thoroughly before processing
3. **Sessions**: Use token-based authentication with short expiry and refresh tokens
4. **Auditing**: Log all important user activities for security and compliance
5. **Multi-tenancy**: Design user management with multi-tenancy in mind from the start
6. **Integration**: Integrate with RBAC module for permission checks on all operations