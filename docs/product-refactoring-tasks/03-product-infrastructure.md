# Task 03: Create Proper bootstrap.go Following Auth Pattern

**Category**: Module Infrastructure  
**Estimated Time**: 1 hour  
**Dependencies**: Task 02  
**Priority**: High (Foundation)

## Objective
Create a proper `bootstrap.go` file following the exact pattern used in `modules/auth/bootstrap.go` to handle route registration and module bootstrapping.

## What to Implement

### 1. Bootstrap File Creation
- Create `modules/product/bootstrap.go` following auth module pattern
- Implement `registerRoutes` function with proper signature
- Keep the bootstrap logic simple and focused

### 2. Route Registration Function
- Implement `registerRoutes(server *core.Server, handler *api.Handler) error`
- Follow the exact pattern from auth module
- Delegate actual route registration to handler

### 3. Integration with Module
- Update `module.go` to use bootstrap function for route registration
- Ensure proper error handling and propagation
- Maintain separation of concerns between module and bootstrap

### 4. Pattern Consistency
- Follow exact same structure as `modules/auth/bootstrap.go`
- Use same function signatures and error handling
- Maintain same level of abstraction

## Files to Create/Update
- Create `modules/product/bootstrap.go`
- Update `modules/product/module.go` to use bootstrap function

## Reference Pattern
```go
// modules/auth/bootstrap.go
package auth

import (
    "wnapi/internal/core"
    "wnapi/modules/auth/api"
)

// registerRoutes đăng ký các route của module auth
func registerRoutes(server *core.Server, handler *api.Handler) error {
    return handler.RegisterRoutes(server)
}
```

## Acceptance Criteria
- [ ] `bootstrap.go` file created with exact same pattern as auth module
- [ ] `registerRoutes` function implemented with correct signature
- [ ] Module.go updated to use bootstrap function
- [ ] Route registration works correctly through bootstrap
- [ ] Error handling matches auth module pattern
- [ ] No additional complexity beyond auth module pattern

## Notes
- Keep this file minimal and focused on route registration only
- Follow the auth module pattern exactly to maintain consistency
- This establishes the foundation for proper API layer integration
