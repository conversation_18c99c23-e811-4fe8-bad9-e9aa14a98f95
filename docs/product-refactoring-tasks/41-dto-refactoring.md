# Task 41: Reorganize DTOs from Request/Response Directories to Combined Files

**Category**: DTO Refactoring  
**Estimated Time**: 1-2 hours  
**Dependencies**: Task 25 (API Layer Stable)  
**Priority**: Medium (Code Organization)

## Objective
Reorganize the DTO structure from separate `request/` and `response/` directories to combined files following the auth module pattern, where each operation has a single file containing both request and response DTOs.

## What to Implement

### 1. New DTO File Structure
- Reorganize from current structure:
  ```
  dto/
  ├── request/
  │   ├── create_product.go
  │   ├── update_product.go
  │   └── list_product.go
  └── response/
      ├── product.go
      └── product_list.go
  ```
- To auth module pattern:
  ```
  dto/
  ├── create_product.go      # Contains both CreateProductRequest and CreateProductResponse
  ├── update_product.go      # Contains both UpdateProductRequest and UpdateProductResponse
  ├── list_product.go        # Contains both ListProductRequest and ListProductResponse
  ├── create_category.go     # Contains both CreateCategoryRequest and CreateCategoryResponse
  └── ...
  ```

### 2. Combined DTO Files
- Create combined files following auth module pattern:
  - `create_product.go` - CreateProductRequest + CreateProductResponse
  - `update_product.go` - UpdateProductRequest + UpdateProductResponse
  - `list_product.go` - ListProductRequest + ListProductResponse
  - `search_product.go` - SearchProductRequest + SearchProductResponse
  - `create_category.go` - CreateCategoryRequest + CreateCategoryResponse
  - `update_category.go` - UpdateCategoryRequest + UpdateCategoryResponse
  - `list_category.go` - ListCategoryRequest + ListCategoryResponse

### 3. DTO Content Organization
```go
// Example: create_product.go
package dto

// CreateProductRequest represents the request to create a new product
type CreateProductRequest struct {
    Name        string  `json:"name" binding:"required,max=255"`
    Description *string `json:"description" binding:"omitempty"`
    CategoryID  *uint   `json:"category_id" binding:"omitempty"`
    Price       float64 `json:"price" binding:"required,min=0"`
    // ... other fields
}

// CreateProductResponse represents the response after creating a product
type CreateProductResponse struct {
    ProductID   uint    `json:"product_id"`
    Name        string  `json:"name"`
    Description *string `json:"description"`
    CategoryID  *uint   `json:"category_id"`
    Price       float64 `json:"price"`
    CreatedAt   string  `json:"created_at"`
    // ... other fields
}
```

### 4. Attribute and Variant DTOs
- Reorganize attribute-related DTOs:
  - `create_product_attribute.go`
  - `create_product_attribute_group.go`
  - `create_product_attribute_option.go`
- Reorganize variant-related DTOs:
  - `create_product_variant.go`
  - `update_product_variant.go`

### 5. Common DTOs
- Keep common DTOs in separate files:
  - `common.go` - Shared types and enums
  - `pagination.go` - Pagination-related DTOs
  - `error.go` - Error response DTOs

### 6. Update Import Statements
- Update all import statements throughout the codebase
- Update API handlers to use new DTO locations
- Update service implementations to use new DTO structure
- Update test files to use new DTO imports

### 7. Validation and Tags
- Ensure all validation tags are preserved
- Maintain JSON serialization tags
- Keep binding validation rules
- Preserve any custom validation logic

## Files to Create
- New combined DTO files in `modules/product/dto/`
- Update all files that import DTOs

## Files to Remove
- `modules/product/dto/request/` directory and all contents
- `modules/product/dto/response/` directory and all contents
- Update any references to old DTO locations

## Reference Pattern
- `modules/auth/dto/` - Primary pattern to follow
- `modules/auth/dto/create_user.go` - Example of combined request/response
- `modules/auth/dto/login.go` - Example of operation-specific DTOs

## Migration Strategy
1. Create new combined DTO files
2. Update imports in handlers and services
3. Test that all functionality still works
4. Remove old request/response directories
5. Clean up any remaining references

## Acceptance Criteria
- [ ] All DTOs reorganized into combined files
- [ ] Request and response types in same files
- [ ] All import statements updated throughout codebase
- [ ] Old request/response directories removed
- [ ] All validation tags and rules preserved
- [ ] API functionality unchanged after reorganization
- [ ] Tests pass with new DTO structure
- [ ] Code follows auth module DTO patterns exactly

## Notes
- This is primarily a code organization improvement
- Functionality should remain exactly the same
- Focus on maintaining all existing validation and serialization
- Use this opportunity to standardize DTO naming conventions
- Ensure consistency with auth module patterns
