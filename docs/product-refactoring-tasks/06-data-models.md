# Task 06: Update All Model ID Fields from uint to INT UNSIGNED

**Category**: Data Models  
**Estimated Time**: 1-2 hours  
**Dependencies**: Task 05  
**Priority**: High (Data Foundation)

## Objective
Update all model ID fields throughout the product module from `uint` to `INT UNSIGNED` to maintain consistency with the user's preferred data modeling patterns and ensure compatibility with the auth/rbac modules.

## What to Implement

### 1. Primary Key Field Updates
- Update all primary key fields in models to use `INT UNSIGNED`
- Ensure GORM tags specify correct database column types
- Update struct field types from `uint` to match database schema

### 2. Foreign Key Field Updates
- Update all foreign key fields to use `INT UNSIGNED`
- Ensure referential integrity is maintained
- Update relationship definitions in GORM

### 3. Model Files to Update
- `models/product.go` - ProductID, CategoryID, TenantID
- `models/category.go` - CategoryID, ParentID, TenantID
- `models/product_attribute.go` - AttributeID, GroupID, TenantID
- `models/product_attribute_group.go` - GroupID, TenantID
- `models/product_attribute_option.go` - OptionID, AttributeID, TenantID
- `models/product_attribute_value.go` - ValueID, ProductID, AttributeID, TenantID
- `models/product_variant.go` - VariantID, ProductID, TenantID
- `models/product_variant_attribute_value.go` - ValueID, VariantID, AttributeID, TenantID
- All other model files with ID fields

### 4. GORM Tag Updates
- Update GORM column type specifications
- Ensure primary key tags are correct
- Update foreign key constraint definitions
- Maintain auto-increment settings where appropriate

### 5. Relationship Updates
- Update all GORM relationship definitions
- Ensure foreign key references use correct field types
- Update association foreign key specifications
- Maintain proper relationship cardinality

## Files to Update
- All files in `modules/product/models/`
- Any DTO files that reference model IDs
- Repository implementations that handle ID fields
- Service implementations that work with IDs

## Example Changes
```go
// Before
type Product struct {
    ProductID uint `gorm:"primaryKey" json:"product_id"`
    TenantID  uint `json:"tenant_id"`
}

// After  
type Product struct {
    ProductID uint `gorm:"primaryKey;type:INT UNSIGNED" json:"product_id"`
    TenantID  uint `gorm:"type:INT UNSIGNED" json:"tenant_id"`
}
```

## Acceptance Criteria
- [ ] All primary key fields use INT UNSIGNED type specification
- [ ] All foreign key fields use INT UNSIGNED type specification
- [ ] GORM tags updated with correct column types
- [ ] All model relationships maintain referential integrity
- [ ] Models compile without errors
- [ ] Database migrations will create correct column types
- [ ] Consistency with auth/rbac module patterns maintained

## Notes
- This change affects the database schema and must be coordinated with migration updates
- Ensure all ID fields are consistently typed throughout the module
- Test model relationships after changes to ensure they still work correctly
