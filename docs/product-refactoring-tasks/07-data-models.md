# Task 07: Add tenant_id Fields to All Models with Proper Constraints

**Category**: Data Models  
**Estimated Time**: 1-2 hours  
**Dependencies**: Task 06  
**Priority**: High (Multi-tenant Foundation)

## Objective
Add `tenant_id` fields to all product models that don't already have them, and ensure all models have proper tenant isolation support with correct constraints and indexing.

## What to Implement

### 1. Add Missing tenant_id Fields
- Review all models and add `tenant_id` field where missing
- Ensure consistent field definition across all models
- Use `INT UNSIGNED` type for tenant_id fields
- Add proper GORM tags and JSON serialization

### 2. Tenant Field Standardization
- Standardize tenant_id field definition across all models:
  ```go
  TenantID uint `gorm:"type:INT UNSIGNED;not null;index" json:"tenant_id"`
  ```
- Ensure consistent naming and tagging
- Add database constraints for tenant isolation

### 3. Models to Update
- Verify and update tenant_id in:
  - `models/product.go`
  - `models/category.go`
  - `models/product_attribute.go`
  - `models/product_attribute_group.go`
  - `models/product_attribute_option.go`
  - `models/product_attribute_value.go`
  - `models/product_configurable_attribute.go`
  - `models/product_variant.go`
  - `models/product_variant_attribute_value.go`
  - `models/product_option_group.go`
  - `models/product_option_value.go`
  - `models/product_options_link.go`

### 4. Composite Indexes for Multi-tenancy
- Add composite indexes combining tenant_id with other frequently queried fields
- Ensure efficient queries for tenant-specific data
- Add indexes like `(tenant_id, product_id)`, `(tenant_id, category_id)`, etc.

### 5. Model Validation
- Add validation to ensure tenant_id is always provided
- Consider adding model hooks to validate tenant context
- Ensure tenant_id cannot be null or zero

### 6. Relationship Updates
- Update all model relationships to consider tenant isolation
- Ensure foreign key relationships include tenant context
- Update association definitions to maintain tenant boundaries

## Files to Update
- All model files in `modules/product/models/`
- Any existing validation logic
- Model relationship definitions

## Example Implementation
```go
type Product struct {
    ProductID   uint   `gorm:"primaryKey;type:INT UNSIGNED" json:"product_id"`
    TenantID    uint   `gorm:"type:INT UNSIGNED;not null;index" json:"tenant_id"`
    CategoryID  *uint  `gorm:"type:INT UNSIGNED;index:idx_tenant_category" json:"category_id"`
    Name        string `gorm:"type:VARCHAR(255);not null" json:"name"`
    // ... other fields
}

// Add composite index
func (Product) TableName() string {
    return "ecom_products"
}
```

## Acceptance Criteria
- [ ] All models have tenant_id field with consistent definition
- [ ] tenant_id uses INT UNSIGNED type with proper constraints
- [ ] Composite indexes created for efficient tenant-specific queries
- [ ] All model relationships respect tenant boundaries
- [ ] Validation ensures tenant_id is always provided
- [ ] Models compile and work correctly with GORM
- [ ] Database schema supports proper tenant isolation

## Notes
- This is critical for multi-tenant data isolation
- Ensure all queries will be tenant-scoped in repository layer
- Consider the impact on existing data when adding tenant_id fields
- Coordinate with migration updates to handle schema changes
