# Task 02: Refactor module.go to Follow Auth/RBAC Patterns

**Category**: Module Infrastructure  
**Estimated Time**: 1-2 hours  
**Dependencies**: Task 01  
**Priority**: High (Foundation)

## Objective
Refactor `modules/product/module.go` to follow the same patterns as `modules/auth/module.go` and `modules/rbac/module.go`, including proper core framework integration, dependency injection, and module lifecycle management.

## What to Implement

### 1. Module Structure Pattern
- Follow the auth module pattern for module struct definition
- Include proper fields for logger, config, app reference, and handler
- Remove direct database connections in favor of core framework integration

### 2. Module Factory Function
- Implement `NewModule` function following auth pattern
- Use `core.RegisterModuleFactory("product", NewModule)` in init function
- Proper dependency injection through core.App parameter

### 3. Core Framework Integration
- Use `app.GetDBManager()` instead of direct database connections
- Use `app.GetLogger()` for logging
- Remove custom HTTP server setup in favor of core framework

### 4. Module Interface Implementation
- Implement all required core.Module interface methods:
  - `Name() string`
  - `Init(ctx context.Context) error`
  - `RegisterRoutes(server *core.Server) error`
  - `Cleanup(ctx context.Context) error`
  - `GetMigrationPath() string`

### 5. Configuration Management
- Create internal configuration structure similar to auth module
- Load configuration from environment variables
- Provide sensible defaults for missing configuration

### 6. Service and Repository Initialization
- Initialize repositories using core database manager
- Initialize services with proper dependency injection
- Create handler with service dependencies

## Files to Update
- `modules/product/module.go`
- Create `modules/product/internal/config.go` (if needed)
- Create `modules/product/internal/types.go` (if needed)

## Reference Patterns
- `modules/auth/module.go` - Main pattern to follow
- `modules/rbac/module.go` - Additional reference
- `modules/auth/internal/config.go` - Configuration pattern

## Acceptance Criteria
- [ ] Module follows exact same structure as auth module
- [ ] Proper core framework integration implemented
- [ ] All core.Module interface methods implemented
- [ ] Configuration loading works correctly
- [ ] Module can be registered and initialized by core framework
- [ ] No direct database or HTTP server management in module
- [ ] Proper dependency injection for all components

## Notes
- This change establishes the foundation for all other refactoring tasks
- Remove any custom server setup and rely on core framework
- Ensure proper error handling and logging throughout
