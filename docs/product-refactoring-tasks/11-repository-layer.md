# Task 11: Create Repository Interface Definitions with Tenant ID Parameters

**Category**: Repository Layer  
**Estimated Time**: 1-2 hours  
**Dependencies**: Task 10 (Data Models Complete)  
**Priority**: High (Repository Foundation)

## Objective
Create comprehensive repository interface definitions following the patterns from auth/rbac modules, ensuring all methods include tenant ID parameters for proper multi-tenant data isolation.

## What to Implement

### 1. Main Repository Interface File
- Create `modules/product/repository/repository.go` following auth pattern
- Define all repository interfaces in a single file
- Include comprehensive method signatures with tenant isolation

### 2. Product Repository Interface
```go
type ProductRepository interface {
    // Basic CRUD operations
    Create(ctx context.Context, tenantID uint, product *models.Product) error
    GetByID(ctx context.Context, tenantID uint, productID uint) (*models.Product, error)
    Update(ctx context.Context, tenantID uint, product *models.Product) error
    Delete(ctx context.Context, tenantID uint, productID uint) error
    
    // List operations with cursor-based pagination
    List(ctx context.Context, tenantID uint, req request.ListProductRequest) ([]*models.Product, string, bool, error)
    Search(ctx context.Context, tenantID uint, req request.SearchProductRequest) ([]*models.Product, string, bool, error)
    
    // Product-specific operations
    GetByCode(ctx context.Context, tenantID uint, code string) (*models.Product, error)
    GetByCategory(ctx context.Context, tenantID uint, categoryID uint) ([]*models.Product, error)
}
```

### 3. Category Repository Interface
```go
type CategoryRepository interface {
    // Basic CRUD operations
    Create(ctx context.Context, tenantID uint, category *models.Category) error
    GetByID(ctx context.Context, tenantID uint, categoryID uint) (*models.Category, error)
    Update(ctx context.Context, tenantID uint, category *models.Category) error
    Delete(ctx context.Context, tenantID uint, categoryID uint) error
    
    // List operations
    List(ctx context.Context, tenantID uint, req request.ListCategoryRequest) ([]*models.Category, string, bool, error)
    
    // Category tree operations
    GetChildren(ctx context.Context, tenantID uint, parentID uint) ([]*models.Category, error)
    GetTree(ctx context.Context, tenantID uint) ([]*models.Category, error)
    MoveNode(ctx context.Context, tenantID uint, nodeID uint, newParentID uint) error
}
```

### 4. Attribute Repository Interfaces
- ProductAttributeRepository with tenant isolation
- ProductAttributeGroupRepository with tenant isolation
- ProductAttributeOptionRepository with tenant isolation
- ProductAttributeValueRepository with tenant isolation

### 5. Variant Repository Interfaces
- ProductVariantRepository with tenant isolation
- ProductVariantAttributeValueRepository with tenant isolation

### 6. Common Patterns
- All methods include `ctx context.Context` as first parameter
- All methods include `tenantID uint` as second parameter
- List methods use cursor-based pagination pattern from rbac module
- Error handling follows auth/rbac patterns
- Include proper documentation for each interface

## Files to Create
- `modules/product/repository/repository.go` - Main interface definitions

## Reference Patterns
- `modules/rbac/repository/repository.go` - Primary pattern for tenant isolation
- `modules/auth/repository/auth_repository.go` - Interface definition pattern

## Acceptance Criteria
- [ ] All repository interfaces defined with tenant ID parameters
- [ ] CRUD operations include proper tenant isolation
- [ ] List operations use cursor-based pagination
- [ ] Search operations include tenant filtering
- [ ] Category tree operations respect tenant boundaries
- [ ] All methods include proper context handling
- [ ] Interface documentation is comprehensive
- [ ] Patterns match auth/rbac module standards

## Notes
- These interfaces will guide the implementation in subsequent tasks
- Ensure all operations are tenant-scoped for data isolation
- Use cursor-based pagination for all list operations as per user preference
- Include specialized operations for product domain (search, categories, variants)
