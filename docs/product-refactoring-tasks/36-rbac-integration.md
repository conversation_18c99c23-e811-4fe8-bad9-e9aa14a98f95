# Task 36: Replace Mock Permission Service with Real RBAC Integration

**Category**: RBAC Integration  
**Estimated Time**: 1-2 hours  
**Dependencies**: Task 35 (Auth Integration Complete)  
**Priority**: High (Authorization Foundation)

## Objective
Replace the mock permission service currently used in the product module with real RBAC integration, implementing proper permission checks for all product operations following the patterns from the RBAC module.

## What to Implement

### 1. Remove Mock Permission Service
- Remove `mockPermissionService` from module.go
- Remove any mock implementations and temporary permission logic
- Clean up development-only permission bypasses

### 2. RBAC Service Integration
- Integrate with real RBAC module services
- Use proper permission checking from RBAC module
- Follow patterns established in RBAC module for permission validation

### 3. Permission Service Injection
```go
// In module.go
func NewModule(app *core.App, config map[string]interface{}) (core.Module, error) {
    // Get RBAC services from core app
    rbacModule := app.GetModule("rbac")
    permissionService := rbacModule.GetPermissionService()
    
    // Use real permission service in handlers
    handler := api.NewHandler(productService, permissionService)
    
    return &Module{
        // ...
        handler: handler,
    }, nil
}
```

### 4. Product-Specific Permissions
- Define comprehensive permission set for product operations:
  - `products.create` - Create new products
  - `products.read` - View product details
  - `products.update` - Modify existing products
  - `products.delete` - Remove products
  - `products.list` - List products
  - `categories.create` - Create categories
  - `categories.read` - View categories
  - `categories.update` - Modify categories
  - `categories.delete` - Remove categories
  - `categories.manage_tree` - Reorganize category tree

### 5. Permission Middleware Integration
- Use RBAC permission middleware for all protected routes
- Replace mock permission checks with real RBAC checks
- Follow middleware patterns from RBAC module

### 6. Handler Permission Checks
```go
func (h *ProductHandler) CreateProduct(c *gin.Context) {
    // Tenant and user context already available from previous middleware
    tenantID := getTenantIDFromContext(c)
    userID := getUserIDFromContext(c)
    
    // Permission check happens in middleware, but can also check in handler
    if !h.permissionService.CheckPermission(c, tenantID, userID, "products.create") {
        c.JSON(http.StatusForbidden, gin.H{"error": "Insufficient permissions"})
        return
    }
    
    // Continue with product creation
}
```

### 7. Route Protection Updates
- Update all route registrations to use real permission middleware
- Apply appropriate permissions to each endpoint
- Ensure proper middleware ordering: tenant -> auth -> rbac

### 8. Error Handling
- Implement proper error responses for permission failures
- Use consistent error codes and messages
- Log permission violations for audit purposes

## Files to Update
- `modules/product/module.go` - Remove mock service, integrate real RBAC
- `modules/product/api/routes.go` - Update permission middleware usage
- All handler files - Update permission checking logic
- `modules/product/api/handler.go` - Update constructor to accept real permission service

## Reference Patterns
- `modules/rbac/service/permission_service.go` - Permission checking patterns
- `modules/rbac/api/middleware/` - Permission middleware patterns
- `.cursor/rules/jwt.mdc` - Permission control examples

## Permission Definitions
Create or update permission definitions for product module:
```sql
INSERT INTO rbac_permissions (permission_code, permission_name, permission_description) VALUES
('products.create', 'Create Products', 'Permission to create new products'),
('products.read', 'Read Products', 'Permission to view product details'),
('products.update', 'Update Products', 'Permission to modify existing products'),
('products.delete', 'Delete Products', 'Permission to remove products'),
('products.list', 'List Products', 'Permission to list products'),
('categories.create', 'Create Categories', 'Permission to create new categories'),
('categories.read', 'Read Categories', 'Permission to view categories'),
('categories.update', 'Update Categories', 'Permission to modify categories'),
('categories.delete', 'Delete Categories', 'Permission to remove categories'),
('categories.manage_tree', 'Manage Category Tree', 'Permission to reorganize category hierarchy');
```

## Acceptance Criteria
- [ ] Mock permission service completely removed
- [ ] Real RBAC permission service integrated
- [ ] All product operations protected with appropriate permissions
- [ ] Permission middleware properly applied to all routes
- [ ] Error handling for permission failures implemented
- [ ] Permission definitions created for product operations
- [ ] Audit logging for permission checks working
- [ ] Integration tests pass with real permission checking

## Notes
- This completes the security integration for the product module
- Ensure all operations are properly protected
- Test with different user roles to verify permission enforcement
- Coordinate with RBAC module team for any new permission requirements
