# Task 26: Implement Tenant Resolution Middleware Following Auth Patterns

**Category**: Multi-tenant Integration  
**Estimated Time**: 1-2 hours  
**Dependencies**: Task 25 (API Layer Complete)  
**Priority**: High (Multi-tenant Foundation)

## Objective
Implement tenant resolution middleware following the patterns established in auth/rbac modules, ensuring proper domain-based tenant resolution and context propagation throughout the request lifecycle.

## What to Implement

### 1. Tenant Resolution Middleware
- Create middleware to extract tenant information from request
- Support multiple tenant resolution methods:
  - Domain-based resolution (primary method)
  - Header-based resolution (X-Tenant-ID)
  - Subdomain-based resolution
- Follow auth module patterns for middleware implementation

### 2. Tenant Context Propagation
- Add tenant information to request context
- Ensure tenant ID is available throughout request lifecycle
- Create helper functions to extract tenant ID from context
- Follow patterns from auth module for context handling

### 3. Domain-Based Tenant Resolution
```go
func ResolveTenantFromDomain(domain string) (uint, error) {
    // Implement domain-to-tenant mapping
    // Support custom domains and subdomains
    // Include caching for performance
}
```

### 4. Tenant Validation Middleware
- Validate tenant exists and is active
- Check tenant permissions and status
- Handle tenant suspension or deactivation
- Proper error responses for invalid tenants

### 5. Middleware Integration
- Integrate with existing API handlers
- Ensure middleware runs before auth middleware
- Follow middleware ordering: tenant -> auth -> rbac
- Add to all protected routes in product module

### 6. Context Helper Functions
```go
func GetTenantIDFromContext(c *gin.Context) (uint, error) {
    // Extract tenant ID from context
    // Handle missing or invalid tenant context
    // Return appropriate errors
}

func SetTenantContext(c *gin.Context, tenantID uint) {
    // Set tenant information in context
    // Include additional tenant metadata if needed
}
```

### 7. Error Handling
- Standardized error responses for tenant issues
- Proper HTTP status codes for tenant-related errors
- Logging for tenant resolution failures
- Security considerations for tenant information exposure

## Files to Create/Update
- Create `modules/product/api/middleware/tenant.go`
- Update API handlers to use tenant middleware
- Update route registration to include tenant middleware
- Create tenant context helper functions

## Reference Patterns
- Auth module middleware patterns
- RBAC module tenant handling
- Core framework middleware integration
- `.cursor/rules/jwt.mdc` for tenant context patterns

## Example Implementation
```go
func TenantResolutionMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        // Extract tenant from domain/header
        tenantID, err := resolveTenant(c)
        if err != nil {
            c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid tenant"})
            c.Abort()
            return
        }
        
        // Validate tenant
        if !isValidTenant(tenantID) {
            c.JSON(http.StatusForbidden, gin.H{"error": "Tenant not found or inactive"})
            c.Abort()
            return
        }
        
        // Set tenant context
        SetTenantContext(c, tenantID)
        c.Next()
    }
}
```

## Acceptance Criteria
- [ ] Tenant resolution middleware implemented
- [ ] Domain-based tenant resolution working
- [ ] Tenant context properly propagated
- [ ] Middleware ordering follows tenant -> auth -> rbac pattern
- [ ] Error handling for invalid tenants implemented
- [ ] Helper functions for tenant context extraction
- [ ] Integration with all product API routes
- [ ] Performance optimized with caching where appropriate

## Notes
- This is critical for multi-tenant data isolation
- Ensure tenant resolution happens before any data access
- Consider caching tenant information for performance
- Security: Don't expose tenant information unnecessarily
- Coordinate with auth integration in subsequent tasks
