# Task 04: Update internal/config.go and internal/types.go

**Category**: Module Infrastructure  
**Estimated Time**: 1-2 hours  
**Dependencies**: Task 03  
**Priority**: Medium

## Objective
Create or update `internal/config.go` and `internal/types.go` files to follow the patterns established in the auth module, providing proper configuration management and type definitions for the product module.

## What to Implement

### 1. Configuration Structure (internal/config.go)
- Create product-specific configuration structure
- Include relevant settings for product module:
  - Default pagination limits
  - Product image settings
  - Category tree depth limits
  - Search configuration
  - Cache settings
- Follow auth module pattern for environment variable loading
- Provide sensible defaults for all configuration values

### 2. Type Definitions (internal/types.go)
- Define core interfaces for product module:
  - ProductService interface
  - ProductRepository interface
  - CategoryService interface
  - CategoryRepository interface
- Define common types and enums used throughout module
- Include proper context handling patterns
- Define error types specific to product domain

### 3. Configuration Loading
- Implement configuration loading function similar to auth module
- Use environment variables with fallback defaults
- Include validation for configuration values
- Proper error handling for configuration issues

### 4. Interface Definitions
- Define comprehensive service interfaces with tenant ID parameters
- Define repository interfaces with tenant isolation
- Include all CRUD operations with proper signatures
- Define list operations with cursor-based pagination

## Files to Create/Update
- Create `modules/product/internal/config.go`
- Create `modules/product/internal/types.go`
- Update `modules/product/module.go` to use new configuration

## Reference Patterns
- `modules/auth/internal/config.go` - Configuration pattern
- `modules/auth/internal/types.go` - Type definition pattern
- `modules/rbac/internal/types.go` - Additional interface patterns

## Acceptance Criteria
- [ ] Configuration structure defined with product-specific settings
- [ ] Environment variable loading implemented with defaults
- [ ] Service interfaces defined with tenant ID parameters
- [ ] Repository interfaces defined with tenant isolation
- [ ] All interfaces include proper context handling
- [ ] Configuration loading integrated into module.go
- [ ] Error types defined for product domain
- [ ] Cursor-based pagination included in list operations

## Notes
- Focus on product-specific configuration needs
- Ensure all interfaces include tenant ID parameters for multi-tenant support
- Keep configuration simple but comprehensive
- Define interfaces that will guide implementation in later tasks
