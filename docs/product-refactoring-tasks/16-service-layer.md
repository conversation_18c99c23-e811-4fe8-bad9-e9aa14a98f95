# Task 16: Create Service Interface Definitions with Tenant ID Parameters

**Category**: Service Layer  
**Estimated Time**: 1-2 hours  
**Dependencies**: Task 15 (Repository Layer Complete)  
**Priority**: High (Service Foundation)

## Objective
Create comprehensive service interface definitions following auth/rbac patterns, ensuring all business logic operations include tenant ID parameters and proper authorization context.

## What to Implement

### 1. Service Interface Definitions
- Create service interfaces in `modules/product/internal/types.go`
- Follow auth module pattern for service interface organization
- Include tenant ID parameters in all operations
- Add user context for authorization checks

### 2. Product Service Interface
```go
type ProductService interface {
    // Basic CRUD operations
    CreateProduct(ctx context.Context, tenantID uint, userID uint, req dto.CreateProductRequest) (*dto.CreateProductResponse, error)
    GetProduct(ctx context.Context, tenantID uint, userID uint, productID uint) (*dto.ProductResponse, error)
    UpdateProduct(ctx context.Context, tenantID uint, userID uint, productID uint, req dto.UpdateProductRequest) (*dto.UpdateProductResponse, error)
    DeleteProduct(ctx context.Context, tenantID uint, userID uint, productID uint) error
    
    // List and search operations
    ListProducts(ctx context.Context, tenantID uint, userID uint, req dto.ListProductRequest) (*dto.ListProductResponse, error)
    SearchProducts(ctx context.Context, tenantID uint, userID uint, req dto.SearchProductRequest) (*dto.SearchProductResponse, error)
    
    // Product-specific operations
    GetProductByCode(ctx context.Context, tenantID uint, userID uint, code string) (*dto.ProductResponse, error)
    GetProductsByCategory(ctx context.Context, tenantID uint, userID uint, categoryID uint) (*dto.ListProductResponse, error)
}
```

### 3. Category Service Interface
```go
type CategoryService interface {
    // Basic CRUD operations
    CreateCategory(ctx context.Context, tenantID uint, userID uint, req dto.CreateCategoryRequest) (*dto.CreateCategoryResponse, error)
    GetCategory(ctx context.Context, tenantID uint, userID uint, categoryID uint) (*dto.CategoryResponse, error)
    UpdateCategory(ctx context.Context, tenantID uint, userID uint, categoryID uint, req dto.UpdateCategoryRequest) (*dto.UpdateCategoryResponse, error)
    DeleteCategory(ctx context.Context, tenantID uint, userID uint, categoryID uint) error
    
    // List operations
    ListCategories(ctx context.Context, tenantID uint, userID uint, req dto.ListCategoryRequest) (*dto.ListCategoryResponse, error)
    
    // Category tree operations
    GetCategoryTree(ctx context.Context, tenantID uint, userID uint) (*dto.CategoryTreeResponse, error)
    MoveCategoryNode(ctx context.Context, tenantID uint, userID uint, req dto.MoveCategoryRequest) error
}
```

### 4. Attribute Service Interfaces
- ProductAttributeService with tenant and user context
- ProductAttributeGroupService with tenant and user context
- ProductAttributeOptionService with tenant and user context

### 5. Variant Service Interfaces
- ProductVariantService with tenant and user context
- ProductVariantAttributeValueService with tenant and user context

### 6. Service Implementation Patterns
- All methods include context, tenantID, and userID parameters
- Business logic validation includes tenant boundary checks
- Authorization checks integrated into service layer
- Proper error handling and response formatting
- DTO transformation between service and repository layers

### 7. Common Service Patterns
- Input validation and sanitization
- Business rule enforcement
- Authorization checks before repository calls
- Audit logging for sensitive operations
- Transaction management for complex operations

## Files to Update
- `modules/product/internal/types.go` - Add service interface definitions
- Update existing service files to implement new interfaces

## Reference Patterns
- `modules/auth/internal/types.go` - Service interface pattern
- `modules/rbac/service/` - Service implementation patterns with tenant isolation

## Acceptance Criteria
- [ ] All service interfaces defined with tenant and user ID parameters
- [ ] Business operations include proper authorization context
- [ ] DTO-based request/response patterns implemented
- [ ] Service interfaces support all required product operations
- [ ] Category tree operations include tenant isolation
- [ ] Search and list operations use proper pagination
- [ ] Error handling patterns match auth/rbac modules
- [ ] Interface documentation is comprehensive

## Notes
- Service layer enforces business rules and authorization
- All operations must validate tenant boundaries
- Include user context for audit trails and authorization
- DTO transformation happens at service layer boundary
- Prepare for RBAC integration in later tasks
