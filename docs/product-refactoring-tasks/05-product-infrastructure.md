# Task 05: Reorganize Directory Structure to Match Auth Module

**Category**: Module Infrastructure  
**Estimated Time**: 1-2 hours  
**Dependencies**: Task 04  
**Priority**: Medium

## Objective
Reorganize the directory structure of modules/product to exactly match the layout and organization of modules/auth, ensuring consistency across modules and proper separation of concerns.

## What to Implement

### 1. Directory Structure Alignment
- Reorganize directories to match auth module structure:
  ```
  modules/product/
  ├── api/
  │   ├── handler.go
  │   └── handlers/
  ├── bootstrap.go
  ├── domain/
  ├── dto/
  ├── internal/
  ├── migrations/
  ├── models/
  ├── module.go
  ├── repository/
  │   ├── repository.go
  │   └── mysql/
  └── service/
  ```

### 2. Remove Unnecessary Directories
- Remove or consolidate directories that don't exist in auth module:
  - `cmd/` directory (move to appropriate location or remove)
  - `configs/` directory (integrate into internal/)
  - `docs/` directory (move to module-level docs)
  - `tests/` directory (reorganize into appropriate locations)
  - `tracing/` directory (integrate into core framework)

### 3. File Organization
- Move files to appropriate directories following auth pattern
- Ensure API handlers are in `api/handlers/` subdirectory
- Place repository implementations in `repository/mysql/`
- Organize models in `models/` directory
- Place DTOs in `dto/` directory

### 4. Clean Up Unused Files
- Remove files that don't align with auth module pattern
- Consolidate duplicate functionality
- Remove custom configuration files in favor of internal/ pattern
- Remove custom tracing in favor of core framework

### 5. Update Import Paths
- Update all import statements to reflect new directory structure
- Ensure all files can find their dependencies
- Update any relative imports to use full module paths

## Files to Move/Update
- Move API files to proper `api/` structure
- Reorganize repository files into `repository/mysql/`
- Move configuration into `internal/`
- Update all import statements throughout module

## Reference Structure
- `modules/auth/` - Primary reference for directory structure
- `modules/rbac/` - Secondary reference for complex modules

## Acceptance Criteria
- [ ] Directory structure exactly matches auth module layout
- [ ] All files moved to appropriate directories
- [ ] Unnecessary directories removed or consolidated
- [ ] All import paths updated and working
- [ ] Module builds successfully with new structure
- [ ] No orphaned files or directories
- [ ] Clean separation of concerns maintained

## Notes
- This task establishes the final foundation for all subsequent refactoring
- Be careful to update all import statements when moving files
- Remove custom solutions in favor of core framework integration
- Maintain backward compatibility where possible during transition
