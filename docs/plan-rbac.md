## Module RBAC - <PERSON><PERSON><PERSON> tr<PERSON>c Chi tiết

## Tổng quan
Module RBAC (Role-Based Access Control) quản lý phân quyền và ủy quyền trong hệ thống, cho phép kiểm soát truy cập chi tiết đến các tài nguyên dựa trên vai trò và chính sách.

## C<PERSON>u trúc thư mục

```
modules/core/rbac/
├── module.go                               # Khai báo RBAC module
│
├── api/                                    # API layer
│   ├── handler.go                          # RBAC handlers chính
│   ├── role_handler.go                     # Role management handlers
│   ├── permission_handler.go               # Permission management handlers
│   ├── policy_handler.go                   # Policy management handlers
│   ├── middleware.go                       # Authorization middleware
│   └── router.go                           # RBAC routes
│
├── service/                                 # Business logic layer
│   ├── entity.go                            # Base RBAC entities & interfaces
│   ├── role.go                             # Role entity & business logic
│   ├── permission.go                       # Permission entity & logic
│   ├── policy.go                           # Authorization policy entity
│   ├── user_role.go                        # User-Role relationship entity
│   ├── service.go                          # Service interfaces
│   ├── role_service.go                     # Role business service
│   ├── permission_service.go               # Permission service
│   ├── policy_service.go                   # Policy service
│   └── authorization_service.go            # Main authorization service
│
├── repository/                             # Data access layer
│   ├── repository.go                       # RBAC repository interfaces
│   ├── role_repository.go                  # Role repository interface
│   ├── permission_repository.go            # Permission repository interface
│   ├── policy_repository.go                # Policy repository interface
│   ├── user_role_repository.go             # User-Role repository interface
│   ├── mysql/                              # MySQL implementations
│   │   ├── role_repository.go              # Role MySQL repository
│   │   ├── permission_repository.go        # Permission MySQL repository
│   │   ├── policy_repository.go            # Policy MySQL repository
│   │   └── user_role_repository.go         # User-Role MySQL repository
│   └── cache/                              # Cache implementations
│       ├── role_cache.go                   # Role caching layer
│       ├── permission_cache.go             # Permission caching layer
│       └── user_permission_cache.go        # User permission caching
│
├── dto/                                    # Data Transfer Objects
│   ├── role_dto.go                         # Role DTOs
│   ├── permission_dto.go                   # Permission DTOs
│   ├── policy_dto.go                       # Policy DTOs
│   ├── user_role_dto.go                    # User-Role DTOs
│   └── authorization_dto.go                # Authorization check DTOs
│
├── migrations/                             # Database migrations
│   ├── 001_create_roles_table.sql          # Roles table
│   ├── 002_create_permissions_table.sql    # Permissions table
│   ├── 003_create_role_permissions_table.sql # Role-Permission mapping
│   ├── 004_create_user_roles_table.sql     # User-Role mapping
│   ├── 005_create_policies_table.sql       # Authorization policies
│   └── 006_insert_default_permissions.sql  # Default system permissions
│
├── policies/                               # Authorization policy implementations
│   ├── base_policy.go                      # Base policy interface
│   ├── user_policy.go                      # User management policies
│   ├── content_policy.go                   # Content management policies
│   ├── ecommerce_policy.go                 # E-commerce policies
│   ├── system_policy.go                    # System administration policies
│   └── tenant_policy.go                    # Tenant-specific policies
│
├── events/                                 # Event handling
│   ├── publisher.go                        # RBAC event publisher
│   └── subscriber.go                       # RBAC event subscriber
│
└── config/                                 # Configuration
    └── default.yaml                        # Default RBAC configuration
```

## Các Model chính

### 1. Role Model
```
Role:
- ID (Primary Key)
- TenantID (Foreign Key, NULL for system roles)
- Name (Role display name)
- Code (Unique identifier)
- Description (Role description)
- IsSystem (Cannot be deleted if true)
- IsDefault (Default role for new users)
- Level (Hierarchy level for role comparison)
- CreatedAt
- UpdatedAt

RoleHierarchy:
- ID (Primary Key)
- ParentRoleID (Foreign Key to roles)
- ChildRoleID (Foreign Key to roles)
- CreatedAt
```

### 2. Permission Model
```
Permission:
- ID (Primary Key)
- Name (Permission display name)
- Code (Unique identifier like 'user.create')
- Module (Module name: core, content, ecommerce)
- Resource (Resource name: user, post, product)
- Action (Action name: create, read, update, delete, manage)
- Description (Permission description)
- IsSystem (System permissions cannot be deleted)
- CreatedAt
- UpdatedAt

PermissionGroup:
- ID (Primary Key)
- Name (Group name)
- Description (Group description)
- Module (Module name)
- CreatedAt
- UpdatedAt
```

### 3. Policy Model
```
Policy:
- ID (Primary Key)
- TenantID (Foreign Key, NULL for system policies)
- Name (Policy name)
- Code (Unique identifier)
- Type (RBAC, ABAC, Custom)
- Description (Policy description)
- IsSystem (System policies cannot be deleted)
- CreatedAt
- UpdatedAt

PolicyRule:
- ID (Primary Key)
- PolicyID (Foreign Key to policies)
- Resource (Resource pattern)
- Actions (Comma-separated actions)
- Effect (Allow/Deny)
- Priority (Rule priority)
- Conditions (JSON conditions for ABAC)
- CreatedAt
- UpdatedAt
```

## Các API Endpoints chính

### Quản lý Role
- `GET    /api/roles` - Danh sách roles
- `POST   /api/roles` - Tạo mới role
- `GET    /api/roles/{id}` - Chi tiết role
- `PUT    /api/roles/{id}` - Cập nhật role
- `DELETE /api/roles/{id}` - Xóa role
- `GET    /api/roles/{id}/permissions` - Danh sách permissions của role
- `POST   /api/roles/{id}/permissions` - Gán permissions cho role

### Quản lý Permission
- `GET    /api/permissions` - Danh sách permissions
- `GET    /api/permissions/groups` - Danh sách permission groups
- `GET    /api/permissions/check` - Kiểm tra quyền

### Quản lý Policy
- `GET    /api/policies` - Danh sách policies
- `POST   /api/policies` - Tạo mới policy
- `GET    /api/policies/{id}` - Chi tiết policy
- `PUT    /api/policies/{id}` - Cập nhật policy
- `DELETE /api/policies/{id}` - Xóa policy
- `GET    /api/policies/{id}/rules` - Danh sách rules của policy
- `POST   /api/policies/{id}/rules` - Thêm rule vào policy

## Database Migrations

### Tạo bảng roles
```sql
CREATE TABLE roles (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    tenant_id BIGINT NULL,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(50) NOT NULL UNIQUE,
    description TEXT NULL,
    is_system BOOLEAN DEFAULT FALSE,
    is_default BOOLEAN DEFAULT FALSE,
    level INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NULL ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE
);

-- Thêm các role hệ thống mặc định
INSERT INTO roles (name, code, description, is_system, is_default, level) VALUES 
('Super Admin', 'super_admin', 'Quản trị hệ thống cao cấp', TRUE, FALSE, 1000),
('Admin', 'admin', 'Quản trị viên', TRUE, TRUE, 100),
('User', 'user', 'Người dùng thông thường', TRUE, TRUE, 10);
```

### Tạo bảng permissions
```sql
CREATE TABLE permissions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(100) NOT NULL UNIQUE,
    module VARCHAR(50) NOT NULL,
    resource VARCHAR(50) NOT NULL,
    action VARCHAR(50) NOT NULL,
    description TEXT NULL,
    is_system BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NULL ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_module_resource (module, resource)
);

-- Thêm các permission hệ thống mặc định
INSERT INTO permissions (name, code, module, resource, action, description, is_system) VALUES 
('Xem danh sách người dùng', 'user.list', 'core', 'user', 'list', 'Xem danh sách người dùng', TRUE),
('Tạo người dùng', 'user.create', 'core', 'user', 'create', 'Tạo người dùng mới', TRUE),
('Xem chi tiết người dùng', 'user.read', 'core', 'user', 'read', 'Xem thông tin chi tiết người dùng', TRUE),
('Cập nhật người dùng', 'user.update', 'core', 'user', 'update', 'Cập nhật thông tin người dùng', TRUE),
('Xóa người dùng', 'user.delete', 'core', 'user', 'delete', 'Xóa người dùng', TRUE);
```

### Tạo bảng role_permissions
```sql
CREATE TABLE role_permissions (
    role_id BIGINT NOT NULL,
    permission_id BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (role_id, permission_id),
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE
);
```

### Tạo bảng user_roles
```sql
CREATE TABLE user_roles (
    user_id BIGINT NOT NULL,
    role_id BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (user_id, role_id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE
);
```

## Middleware Authorization

### Kiểm tra quyền trong middleware
```go
func Authorization(permission string) gin.HandlerFunc {
    return func(c *gin.Context) {
        // Lấy thông tin user từ context
        user, exists := c.Get("user")
        if !exists {
            c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
            return
        }
        
        // Kiểm tra quyền
        hasPermission, err := authService.CheckPermission(c.Request.Context(), user.(*service.User).ID, permission)
        if err != nil || !hasPermission {
            c.AbortWithStatusJSON(http.StatusForbidden, gin.H{"error": "Forbidden"})
            return
        }
        
        c.Next()
    }
}
```

## Cách sử dụng

### 1. Khởi tạo RBAC module
```go
rbacModule := rbac.NewRBACModule(db, cache, eventBus)
rbacModule.Init()
```

### 2. Sử dụng middleware kiểm tra quyền
```go
// Áp dụng middleware cho route
router.GET("/api/users", rbac.Authorization("user.list"), userHandler.ListUsers)

// Hoặc sử dụng middleware cho nhóm route
authorized := router.Group("/api")
authorized.Use(rbac.Authorization("")) // Yêu cầu đăng nhập
{
    authorized.GET("/users", userHandler.ListUsers)
    authorized.POST("/users", userHandler.CreateUser)
}
```

### 3. Kiểm tra quyền trong service
```go
// Kiểm tra quyền trước khi thực hiện hành động
hasPermission, err := rbacService.CheckPermission(ctx, userID, "user.delete")
if err != nil || !hasPermission {
    return fmt.Errorf("permission denied")
}

// Thực hiện hành động sau khi đã xác nhận có quyền
return userRepository.Delete(ctx, userID)
```

## Tích hợp với hệ thống

1. **Tích hợp với module User**: Module RBAC sử dụng bảng users từ module User để quản lý người dùng và phân quyền.
2. **Tích hợp với module Tenant**: Hỗ trợ đa tenant thông qua trường tenant_id trong các bảng.
3. **Tích hợp với module Audit**: Ghi log các thay đổi về phân quyền.

## Mở rộng

### Thêm permission mới
1. Tạo migration thêm permission mới vào bảng permissions
2. Thêm permission vào danh sách permission mặc định trong file config
3. Cập nhật tài liệu API

### Tạo policy tùy chỉnh
1. Tạo file mới trong thư mục `policies/`
2. Implement interface `Policy`
3. Đăng ký policy trong `policy_service.go`

## Best Practices

1. Luôn sử dụng constants cho permission codes
2. Sử dụng cache cho các truy vấn phân quyền thường xuyên
3. Kiểm tra quyền ở cả middleware và service layer
4. Ghi log đầy đủ các hành động phân quyền quan trọng
5. Sử dụng transactions cho các thao tác cập nhật nhiều bảng RBAC