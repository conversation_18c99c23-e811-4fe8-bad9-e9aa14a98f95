# Tài Liệu Kiến Trúc WNAPI

## Tổng Quan Kiến Trúc

WNAPI được thiết kế theo kiến trúc mô-đun hóa cao, mỗi thành phần chức năng được tách thành các module riêng biệt, cho phép tùy chỉnh và mở rộng một cách linh hoạt.

![Architecture Diagram](/docs/images/architecture.jpg)

## Các Thành Phần Chính

### Core Framework (internal/core)

Core Framework là trung tâm của ứng dụng, cung cấp các chức năng cơ bản:

- **App**: Quản lý vòng đời ứng dụng, khởi tạo và hủy các module
- **Config**: Đọc và quản lý cấu hình từ file YAML
- **Server**: Quản lý HTTP server và định tuyến
- **Module Registry**: <PERSON>u<PERSON><PERSON> lý việc đăng ký và khởi tạo các module

### Module System

Mỗi module trong hệ thống đều tuân theo interface chung:

```go
type Module interface {
    // Name trả về tên của module
    Name() string

    // Init khởi tạo module
    Init(ctx context.Context) error

    // RegisterRoutes đăng ký các routes của module
    RegisterRoutes(router *Server) error

    // Cleanup dọn dẹp tài nguyên của module
    Cleanup(ctx context.Context) error

    // GetMigrationPath trả về đường dẫn chứa migrations của module
    GetMigrationPath() string
}
```

Các module được tạo ra và đăng ký với hệ thống vào thời điểm biên dịch thông qua cơ chế `init()` function và factory pattern:

```go
func init() {
    // Đăng ký module với global registry
    core.RegisterModuleFactory("auth", NewModule)
}
```

### Plugin System

Hệ thống plugin cho phép mở rộng chức năng mà không cần sửa đổi code core, tuân theo interface:

```go
type Plugin interface {
    // Name trả về tên của plugin
    Name() string

    // Init khởi tạo plugin
    Init(ctx context.Context) error

    // Shutdown dọn dẹp tài nguyên của plugin
    Shutdown(ctx context.Context) error
}
```

### Database Layer

Cung cấp truy cập đến database với:

- **Connection Management**: Quản lý pool connection
- **Migration System**: Hỗ trợ database migration
- **Repository Pattern**: Cung cấp interface truy cập dữ liệu

### Project System

Cho phép tạo nhiều dự án khác nhau từ cùng một codebase với:

- **Project-specific Config**: Cấu hình riêng cho từng dự án
- **Module Activation**: Kích hoạt/vô hiệu hóa module theo dự án
- **Environment Settings**: Cấu hình môi trường

## Luồng Dữ Liệu & Xử Lý Request

1. HTTP Request đến Server
2. Middleware xử lý (logging, authentication, etc.)
3. Router định tuyến request đến module thích hợp
4. Handler xử lý request:
   - Chuyển đổi dữ liệu (DTOs)
   - Gọi service layer
   - Service thực hiện business logic
   - Repository truy xuất/cập nhật dữ liệu
5. Response được trả về

## Các Pattern Sử Dụng

- **Dependency Injection**: Thông qua module factory và constructor
- **Repository Pattern**: Truy cập dữ liệu
- **Factory Pattern**: Tạo module và plugin
- **Strategy Pattern**: Xử lý business logic
- **Middleware Pattern**: Xử lý request/response

## Mở Rộng Hệ Thống

### Tạo Module Mới

1. Tạo thư mục module mới trong `modules/`
2. Implement Module interface
3. Đăng ký module với registry trong `init()`
4. Tạo các thành phần cần thiết (api, service, models, repository)

### Tạo Plugin Mới

1. Tạo thư mục plugin mới trong `plugins/`
2. Implement Plugin interface
3. Đăng ký plugin với registry trong `init()`

### Tạo Project Mới

1. Tạo thư mục project mới trong `projects/`
2. Tạo file config.yaml xác định module và plugin cần sử dụng
3. Cấu hình database và các thiết lập khác

## Hiệu Năng & Scale

- **Goroutines**: Xử lý bất đồng bộ
- **Connection Pooling**: Tối ưu kết nối database
- **Caching Mechanisms**: Giảm tải database
- **Horizontal Scaling**: Khả năng scale thông qua nhiều instance 