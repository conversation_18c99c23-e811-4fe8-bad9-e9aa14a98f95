# Product Module Refactoring Plan

## Overview
Refactor the modules/product directory to follow the same architectural structure and patterns as modules/auth and modules/rbac, implementing multi-tenant architecture, proper auth integration, and consistent patterns.

## Task Categories

### 1. Module Infrastructure (Tasks 01-05)
Foundation setup and core module structure

### 2. Data Models (Tasks 06-10)
Database models, migrations, and ID field updates

### 3. Repository Layer (Tasks 11-15)
Repository interfaces and implementations with tenant support

### 4. Service Layer (Tasks 16-20)
Service interfaces and implementations with tenant support

### 5. API Layer (Tasks 21-25)
Handlers, routes, and middleware integration

### 6. Multi-tenant Integration (Tasks 26-30)
Tenant resolution and data isolation

### 7. Auth Integration (Tasks 31-35)
JWT middleware and authentication patterns

### 8. RBAC Integration (Tasks 36-40)
Permission checks and role-based access

### 9. DTO Refactoring (Tasks 41-45)
Reorganize DTOs to combined pattern

### 10. Testing (Tasks 46-50)
Update tests for new patterns

## Dependencies
- Tasks 01-05 must be completed first (foundation)
- Tasks 06-10 should follow (data layer)
- Tasks 11-15 depend on 06-10 (repository layer)
- Tasks 16-20 depend on 11-15 (service layer)
- Tasks 21-25 depend on 16-20 (API layer)
- Tasks 26-30 can run parallel with 21-25
- Tasks 31-35 depend on 26-30 (auth integration)
- Tasks 36-40 depend on 31-35 (RBAC integration)
- Tasks 41-45 can run after 21-25 (DTO refactoring)
- Tasks 46-50 should be done throughout but finalized at end

## Key Patterns to Implement
- Multi-tenant architecture with strict data isolation
- Domain-based tenant resolution
- Middleware ordering: tenant -> auth -> rbac
- Repository/service interfaces with tenantID parameters
- INT UNSIGNED for all ID fields
- Combined DTO files (request + response)
- Cursor-based pagination for list operations
- Consistent error handling patterns
- Core framework integration
