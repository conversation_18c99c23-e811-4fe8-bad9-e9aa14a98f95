# Plan Tích Hợ<PERSON> golang-migrate/migrate

## 1. <PERSON>ài Đặt và C<PERSON>u <PERSON> Đầu

### 1.1 Cài đặt Dependencies
```bash
# Thêm vào go.mod
go get -u github.com/golang-migrate/migrate/v4
go get -u github.com/golang-migrate/migrate/v4/database/mysql
go get -u github.com/golang-migrate/migrate/v4/source/file
```

### 1.2 Cập nhật cấu trúc thư mục
```

├── internal/
│   ├── database/
│   │   ├── connection.go
│   │   ├── migration.go                 # Migration utilities (CẬP NHẬT)
│   │   └── migrator.go                  # Migrator manager (MỚI)
│
├── cmd/
│   ├── server/
│   │   └── main.go
│   └── migrate/                         # CLI tool cho migration (MỚI)
│       └── main.go
│
├── scripts/
│   ├── migrate.sh                       # Migration scripts (CẬP NHẬT)
│   ├── create-migration.sh              # Tạo migration file (MỚI)
│   └── rollback.sh                      # Rollback migrations (MỚI)
```

## 2. <PERSON><PERSON><PERSON><PERSON> <PERSON> Migration Manager

### 2.1 Migration Interface và Utilities

```go
// internal/database/migrator.go
package database

import (
    "database/sql"
    "fmt"
    "path/filepath"
    "sort"
    
    "github.com/golang-migrate/migrate/v4"
    "github.com/golang-migrate/migrate/v4/database/mysql"
    _ "github.com/golang-migrate/migrate/v4/source/file"
)

type MigrationConfig struct {
    DatabaseURL     string
    MigrationsPath  string
    TablePrefix     string
}

type Migrator struct {
    db           *sql.DB
    migrate      *migrate.Migrate
    config       *MigrationConfig
    moduleName   string
}

type ModuleMigrator struct {
    systemMigrator *Migrator
    moduleMigrators map[string]*Migrator
    config         *MigrationConfig
}

func NewModuleMigrator(db *sql.DB, config *MigrationConfig) (*ModuleMigrator, error) {
    // Khởi tạo system migrator
    systemMigrator, err := NewMigrator(db, &MigrationConfig{
        DatabaseURL:    config.DatabaseURL,
        MigrationsPath: filepath.Join(config.MigrationsPath, "system"),
        TablePrefix:    "system",
    })
    if err != nil {
        return nil, fmt.Errorf("failed to create system migrator: %w", err)
    }

    return &ModuleMigrator{
        systemMigrator:  systemMigrator,
        moduleMigrators: make(map[string]*Migrator),
        config:          config,
    }, nil
}

func NewMigrator(db *sql.DB, config *MigrationConfig) (*Migrator, error) {
    driver, err := mysql.WithInstance(db, &mysql.Config{
        MigrationsTable: fmt.Sprintf("%s_schema_migrations", config.TablePrefix),
    })
    if err != nil {
        return nil, fmt.Errorf("failed to create database driver: %w", err)
    }

    sourceURL := fmt.Sprintf("file://%s", config.MigrationsPath)
    m, err := migrate.NewWithDatabaseInstance(sourceURL, "mysql", driver)
    if err != nil {
        return nil, fmt.Errorf("failed to create migrate instance: %w", err)
    }

    return &Migrator{
        db:      db,
        migrate: m,
        config:  config,
    }, nil
}

// Thêm module migrator
func (mm *ModuleMigrator) AddModule(moduleName string) error {
    migrator, err := NewMigrator(mm.systemMigrator.db, &MigrationConfig{
        DatabaseURL:    mm.config.DatabaseURL,
        MigrationsPath: filepath.Join(mm.config.MigrationsPath, "modules", moduleName),
        TablePrefix:    fmt.Sprintf("module_%s", moduleName),
    })
    if err != nil {
        return fmt.Errorf("failed to create migrator for module %s: %w", moduleName, err)
    }

    mm.moduleMigrators[moduleName] = migrator
    return nil
}

// Migration theo thứ tự: system -> modules
func (mm *ModuleMigrator) MigrateUp() error {
    // 1. Migrate system tables trước
    if err := mm.systemMigrator.migrate.Up(); err != nil && err != migrate.ErrNoChange {
        return fmt.Errorf("system migration failed: %w", err)
    }

    // 2. Migrate từng module theo thứ tự
    moduleNames := make([]string, 0, len(mm.moduleMigrators))
    for name := range mm.moduleMigrators {
        moduleNames = append(moduleNames, name)
    }
    sort.Strings(moduleNames) // Ensure consistent order

    for _, moduleName := range moduleNames {
        migrator := mm.moduleMigrators[moduleName]
        if err := migrator.migrate.Up(); err != nil && err != migrate.ErrNoChange {
            return fmt.Errorf("module %s migration failed: %w", moduleName, err)
        }
    }

    return nil
}

// Rollback ngược lại: modules -> system
func (mm *ModuleMigrator) MigrateDown() error {
    // 1. Rollback từng module trước
    for moduleName, migrator := range mm.moduleMigrators {
        if err := migrator.migrate.Down(); err != nil && err != migrate.ErrNoChange {
            return fmt.Errorf("module %s rollback failed: %w", moduleName, err)
        }
    }

    // 2. Rollback system tables
    if err := mm.systemMigrator.migrate.Down(); err != nil && err != migrate.ErrNoChange {
        return fmt.Errorf("system rollback failed: %w", err)
    }

    return nil
}

func (mm *ModuleMigrator) Close() error {
    if err := mm.systemMigrator.migrate.Close(); err != nil {
        return err
    }

    for _, migrator := range mm.moduleMigrators {
        if err := migrator.migrate.Close(); err != nil {
            return err
        }
    }

    return nil
}
```

### 2.2 Tích Hợp vào Core App

```go
// internal/core/app.go (cập nhật)
package core

import (
    "myapp/internal/database"
    // ... other imports
)

type App struct {
    config     *AppConfig
    db         *sql.DB
    migrator   *database.ModuleMigrator  // THÊM MỚI
    modules    map[string]Module
    plugins    map[string]Plugin
    server     *Server
}

func NewApp(config *AppConfig) (*App, error) {
    // ... existing code ...

    // Khởi tạo migrator
    migrator, err := database.NewModuleMigrator(db, &database.MigrationConfig{
        DatabaseURL:    config.DatabaseURL,
        MigrationsPath: "migrations",
    })
    if err != nil {
        return nil, fmt.Errorf("failed to create migrator: %w", err)
    }

    return &App{
        config:   config,
        db:       db,
        migrator: migrator,  // THÊM MỚI
        modules:  make(map[string]Module),
        plugins:  make(map[string]Plugin),
    }, nil
}

// Thêm method migrate
func (a *App) RunMigrations() error {
    return a.migrator.MigrateUp()
}

func (a *App) RollbackMigrations() error {
    return a.migrator.MigrateDown()
}

// Cập nhật InitModules để register migrations
func (a *App) InitModules() error {
    moduleConfigs := a.config.Modules

    for _, moduleConfig := range moduleConfigs {
        if !moduleConfig.Enabled {
            continue
        }

        // Đăng ký module migration
        if err := a.migrator.AddModule(moduleConfig.Name); err != nil {
            return fmt.Errorf("failed to add module %s migration: %w", moduleConfig.Name, err)
        }

        // ... existing module initialization code ...
    }

    return nil
}
```

## 3. Module Migration Integration

### 3.1 Cập nhật Module Interface

```go
// internal/core/module.go (cập nhật)
type Module interface {
    Name() string
    RegisterRoutes(router Router)
    GetMigrationPath() string  // THÊM MỚI
    GetMigrationOrder() int    // THÊM MỚI - thứ tự migration
}

// Ví dụ implementation trong auth module
// modules/auth/module.go
func (m *AuthModule) GetMigrationPath() string {
    return "migrations/modules/auth"
}

func (m *AuthModule) GetMigrationOrder() int {
    return 1  // auth module cần migrate đầu tiên
}
```

### 3.2 Migration Files Organization

```sql
-- migrations/system/000001_init_schema.up.sql
CREATE DATABASE IF NOT EXISTS myapp;
USE myapp;

-- Tạo các bảng system chung
CREATE TABLE IF NOT EXISTS system_config (
    id INT PRIMARY KEY AUTO_INCREMENT,
    config_key VARCHAR(255) NOT NULL UNIQUE,
    config_value TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- migrations/modules/auth/000001_create_users_table.up.sql
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    email VARCHAR(255) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_email (email),
    INDEX idx_status (status)
);

-- migrations/modules/auth/000002_add_refresh_tokens.up.sql
CREATE TABLE refresh_tokens (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    token_hash VARCHAR(255) NOT NULL UNIQUE,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_expires_at (expires_at)
);
```

## 4. CLI Tools cho Migration

### 4.1 Migration CLI Tool

```go
// cmd/migrate/main.go
package main

import (
    "flag"
    "fmt"
    "log"
    "os"
    
    "myapp/internal/core"
    "myapp/internal/database"
)

func main() {
    var (
        projectName = flag.String("project", "", "Project name")
        action      = flag.String("action", "up", "Migration action: up, down, version, create")
        moduleName  = flag.String("module", "", "Module name for create action")
        migrationName = flag.String("name", "", "Migration name for create action")
    )
    flag.Parse()

    if *projectName == "" {
        log.Fatal("Project name is required")
    }

    config := &core.AppConfig{
        ConfigPath:        fmt.Sprintf("projects/%s/config.yaml", *projectName),
        ModulesConfigPath: fmt.Sprintf("projects/%s/modules.yaml", *projectName),
    }

    switch *action {
    case "up":
        runMigrationUp(config)
    case "down":
        runMigrationDown(config)
    case "version":
        showMigrationVersion(config)
    case "create":
        createMigration(*moduleName, *migrationName)
    default:
        log.Fatalf("Unknown action: %s", *action)
    }
}

func runMigrationUp(config *core.AppConfig) {
    app, err := core.NewApp(config)
    if err != nil {
        log.Fatalf("Failed to create app: %v", err)
    }
    defer app.Close()

    if err := app.InitModules(); err != nil {
        log.Fatalf("Failed to init modules: %v", err)
    }

    if err := app.RunMigrations(); err != nil {
        log.Fatalf("Migration failed: %v", err)
    }

    fmt.Println("Migration completed successfully")
}

func createMigration(moduleName, migrationName string) {
    if moduleName == "" || migrationName == "" {
        log.Fatal("Module name and migration name are required for create action")
    }

    generator := &database.MigrationGenerator{
        ModuleName:    moduleName,
        MigrationName: migrationName,
        BasePath:      "migrations",
    }

    if err := generator.Generate(); err != nil {
        log.Fatalf("Failed to create migration: %v", err)
    }

    fmt.Printf("Migration created for module %s: %s\n", moduleName, migrationName)
}
```

### 4.2 Migration Generator

```go
// internal/database/generator.go
package database

import (
    "fmt"
    "os"
    "path/filepath"
    "time"
)

type MigrationGenerator struct {
    ModuleName    string
    MigrationName string
    BasePath      string
}

func (g *MigrationGenerator) Generate() error {
    timestamp := time.Now().Unix()
    
    var migrationDir string
    if g.ModuleName == "system" {
        migrationDir = filepath.Join(g.BasePath, "system")
    } else {
        migrationDir = filepath.Join(g.BasePath, "modules", g.ModuleName)
    }

    // Tạo thư mục nếu chưa tồn tại
    if err := os.MkdirAll(migrationDir, 0755); err != nil {
        return fmt.Errorf("failed to create migration directory: %w", err)
    }

    // Tạo files up và down
    baseName := fmt.Sprintf("%d_%s", timestamp, g.MigrationName)
    upFile := filepath.Join(migrationDir, baseName+".up.sql")
    downFile := filepath.Join(migrationDir, baseName+".down.sql")

    // Tạo file up
    upContent := fmt.Sprintf(`-- Migration: %s
-- Module: %s
-- Created: %s

-- Add your SQL statements here
`, g.MigrationName, g.ModuleName, time.Now().Format("2006-01-02 15:04:05"))

    if err := os.WriteFile(upFile, []byte(upContent), 0644); err != nil {
        return fmt.Errorf("failed to create up file: %w", err)
    }

    // Tạo file down
    downContent := fmt.Sprintf(`-- Rollback: %s
-- Module: %s
-- Created: %s

-- Add your rollback SQL statements here
`, g.MigrationName, g.ModuleName, time.Now().Format("2006-01-02 15:04:05"))

    if err := os.WriteFile(downFile, []byte(downContent), 0644); err != nil {
        return fmt.Errorf("failed to create down file: %w", err)
    }

    return nil
}
```

## 5. Scripts và Automation

### 5.1 Cập nhật Migration Scripts

```bash
#!/bin/bash
# scripts/migrate.sh

PROJECT_NAME=$1
ACTION=${2:-"up"}

if [ -z "$PROJECT_NAME" ]; then
    echo "Usage: $0 <project-name> [action]"
    echo "Actions: up, down, version"
    exit 1
fi

echo "Running migration for project: $PROJECT_NAME"

case $ACTION in
    "up")
        go run cmd/migrate/main.go -project="$PROJECT_NAME" -action=up
        ;;
    "down")
        echo "Warning: This will rollback ALL migrations. Are you sure? (y/N)"
        read -r response
        if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
            go run cmd/migrate/main.go -project="$PROJECT_NAME" -action=down
        else
            echo "Migration rollback cancelled"
        fi
        ;;
    "version")
        go run cmd/migrate/main.go -project="$PROJECT_NAME" -action=version
        ;;
    *)
        echo "Unknown action: $ACTION"
        echo "Available actions: up, down, version"
        exit 1
        ;;
esac
```

```bash
#!/bin/bash
# scripts/create-migration.sh

MODULE_NAME=$1
MIGRATION_NAME=$2

if [ -z "$MODULE_NAME" ] || [ -z "$MIGRATION_NAME" ]; then
    echo "Usage: $0 <module-name> <migration-name>"
    echo "Example: $0 auth add_user_roles"
    exit 1
fi

echo "Creating migration for module: $MODULE_NAME"
echo "Migration name: $MIGRATION_NAME"

go run cmd/migrate/main.go -action=create -module="$MODULE_NAME" -name="$MIGRATION_NAME"

echo "Migration files created successfully!"
echo "Remember to:"
echo "1. Edit the .up.sql file with your schema changes"
echo "2. Edit the .down.sql file with rollback statements"
echo "3. Test the migration before deploying"
```

### 5.2 Makefile Integration

```makefile
# Makefile (cập nhật)

# Migration commands
.PHONY: migrate migrate-up migrate-down migrate-version create-migration

migrate: migrate-up

migrate-up:
	@echo "Running migrations for project: $(PROJECT)"
	@./scripts/migrate.sh $(PROJECT) up

migrate-down:
	@echo "Rolling back migrations for project: $(PROJECT)"
	@./scripts/migrate.sh $(PROJECT) down

migrate-version:
	@echo "Checking migration version for project: $(PROJECT)"
	@./scripts/migrate.sh $(PROJECT) version

create-migration:
	@echo "Creating migration for module: $(MODULE)"
	@./scripts/create-migration.sh $(MODULE) $(NAME)

# Ví dụ sử dụng:
# make migrate PROJECT=blog-site
# make create-migration MODULE=auth NAME=add_user_roles
# make migrate-down PROJECT=blog-site
```

## 6. Testing Strategy

### 6.1 Migration Testing

```go
// tests/integration/migration_test.go
package integration

import (
    "database/sql"
    "testing"
    
    "myapp/internal/database"
    _ "github.com/go-sql-driver/mysql"
)

func TestMigrations(t *testing.T) {
    // Setup test database
    db := setupTestDB(t)
    defer db.Close()

    migrator, err := database.NewModuleMigrator(db, &database.MigrationConfig{
        DatabaseURL:    getTestDatabaseURL(),
        MigrationsPath: "../../migrations",
    })
    if err != nil {
        t.Fatalf("Failed to create migrator: %v", err)
    }
    defer migrator.Close()

    // Test migration up
    if err := migrator.MigrateUp(); err != nil {
        t.Fatalf("Migration up failed: %v", err)
    }

    // Verify tables exist
    tables := []string{"users", "refresh_tokens"}
    for _, table := range tables {
        if !tableExists(t, db, table) {
            t.Errorf("Table %s does not exist after migration", table)
        }
    }

    // Test migration down
    if err := migrator.MigrateDown(); err != nil {
        t.Fatalf("Migration down failed: %v", err)
    }

    // Verify tables are dropped
    for _, table := range tables {
        if tableExists(t, db, table) {
            t.Errorf("Table %s still exists after rollback", table)
        }
    }
}
```

## 7. Deployment Integration

### 7.1 Docker Integration

```dockerfile
# Dockerfile (cập nhật)
FROM golang:1.21-alpine AS builder

WORKDIR /app
COPY go.mod go.sum ./
RUN go mod download

COPY . .
RUN go build -o server cmd/server/main.go
RUN go build -o migrate cmd/migrate/main.go

FROM alpine:latest
RUN apk --no-cache add ca-certificates tzdata
WORKDIR /root/

COPY --from=builder /app/server .
COPY --from=builder /app/migrate .
COPY --from=builder /app/migrations ./migrations
COPY --from=builder /app/projects ./projects
COPY --from=builder /app/scripts ./scripts

# Make scripts executable
RUN chmod +x ./scripts/*.sh

CMD ["./server", "--project=blog-site"]
```

### 7.2 Docker Compose với Migration

```yaml
# docker-compose.yml (cập nhật)
version: '3.8'

services:
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: myapp
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql

  migrate:
    build: .
    command: ["./migrate", "-project=blog-site", "-action=up"]
    depends_on:
      - mysql
    environment:
      DATABASE_URL: "mysql://root:rootpassword@mysql:3306/myapp"

  app:
    build: .
    command: ["./server", "--project=blog-site"]
    ports:
      - "8080:8080"
    depends_on:
      - migrate
    environment:
      DATABASE_URL: "mysql://root:rootpassword@mysql:3306/myapp"

volumes:
  mysql_data:
```

## 8. Monitoring và Logging

### 8.1 Migration Logging

```go
// internal/database/migrator.go (cập nhật)
import (
    "log/slog"
)

func (mm *ModuleMigrator) MigrateUp() error {
    slog.Info("Starting database migrations")

    // System migration
    slog.Info("Running system migrations")
    if err := mm.systemMigrator.migrate.Up(); err != nil && err != migrate.ErrNoChange {
        slog.Error("System migration failed", "error", err)
        return fmt.Errorf("system migration failed: %w", err)
    }
    slog.Info("System migrations completed")

    // Module migrations
    for _, moduleName := range moduleNames {
        slog.Info("Running module migration", "module", moduleName)
        migrator := mm.moduleMigrators[moduleName]
        if err := migrator.migrate.Up(); err != nil && err != migrate.ErrNoChange {
            slog.Error("Module migration failed", "module", moduleName, "error", err)
            return fmt.Errorf("module %s migration failed: %w", moduleName, err)
        }
        slog.Info("Module migration completed", "module", moduleName)
    }

    slog.Info("All migrations completed successfully")
    return nil
}
```

## Kế Hoạch Triển Khai

### Phase 1: Core Implementation (Week 1)
- [ ] Cài đặt golang-migrate dependencies
- [ ] Implement `ModuleMigrator` và `Migrator` classes
- [ ] Tạo migration CLI tool
- [ ] Viết migration scripts cơ bản

### Phase 2: Module Integration (Week 2)
- [ ] Tích hợp migration vào core App
- [ ] Cập nhật module interfaces
- [ ] Tạo migration files cho các module hiện tại
- [ ] Testing migration flow

### Phase 3: Tooling & Automation (Week 3)
- [ ] Hoàn thiện migration generator
- [ ] Cập nhật build scripts và Makefile
- [ ] Docker integration
- [ ] Documentation

### Phase 4: Testing & Deployment (Week 4)
- [ ] Comprehensive testing
- [ ] Production deployment testing
- [ ] Performance optimization
- [ ] Final documentation

Cấu trúc này sẽ cho phép:
- Migration tự động theo modules
- Rollback an toàn
- Tách biệt migration system và modules
- Tool CLI mạnh mẽ cho development
- Integration hoàn chỉnh với deployment workflow