# Kế hoạch chuyển đổi từ YAML sang .env

## 1. Tổng quan

Hiện tại hệ thống đang sử dụng file cấu hình YAML để quản lý các thiết lập ứng dụng. <PERSON><PERSON> tăng tính bảo mật và tuân thủ best practices trong việc quản lý configuration, chúng ta sẽ chuyển đổi sang sử dụng file `.env` cho các biến môi trường.

## 2. Phân tích hiện trạng

### 2.1. Các file YAML hiện tại:
- `config/app.yaml` - Cấu hình chính của ứng dụng
- `config/db.yaml` - Cấu hình database  
- `config/modules.yaml` - Cấu hình modules

### 2.2. Cấu trúc hiện tại:
```yaml
# app.yaml
app:
  name: wnapi
  version: 0.1.0
  env: development

server:
  host: 0.0.0.0
  port: 8080
  timeout: 30s
  # ... c<PERSON>c cấu hình khác

# db.yaml  
database:
  type: mysql
  host: localhost
  port: 3307
  username: root
  password: root
  # ... các cấu hình khác

# modules.yaml
modules:
  - name: hello
    enabled: true
  - name: auth
    enabled: true
```

## 3. Kế hoạch thực hiện

### 3.1. Giai đoạn 1: Chuẩn bị và thiết kế

#### 3.1.1. Thiết kế cấu trúc .env
Tạo mapping từ YAML sang environment variables:

**File `.env`:**
```env
# Application Configuration
APP_NAME=wnapi
APP_VERSION=0.1.0
APP_ENV=development
LOG_LEVEL=info

# Server Configuration
SERVER_HOST=0.0.0.0
SERVER_PORT=8080
SERVER_TIMEOUT=30s
SERVER_READ_TIMEOUT=15s
SERVER_WRITE_TIMEOUT=15s
SERVER_MAX_HEADER_BYTES=1048576

# Database Configuration
DB_TYPE=mysql
DB_HOST=localhost
DB_PORT=3307
DB_USERNAME=root
DB_PASSWORD=root
DB_DATABASE=wnapi
DB_MAX_OPEN_CONNS=20
DB_MAX_IDLE_CONNS=10
DB_CONN_MAX_LIFETIME=3600s
DB_MIGRATION_PATH=./migrations

# Module Configuration
MODULES_ENABLED=hello,auth

# Auth Module Configuration
AUTH_JWT_SECRET=your_very_secure_jwt_secret_key_here
AUTH_ACCESS_TOKEN_EXPIRY=15m
AUTH_REFRESH_TOKEN_EXPIRY=168h

# Hello Module Configuration
HELLO_MESSAGE=Xin chào từ module Hello!

# Auth Module Message
AUTH_MESSAGE=Xin chào từ module Auth!
```

#### 3.1.2. Thiết kế file template
Tạo file `.env.example` làm template:
```env
# Application Configuration
APP_NAME=wnapi
APP_VERSION=0.1.0
APP_ENV=development
LOG_LEVEL=info

# Server Configuration
SERVER_HOST=0.0.0.0
SERVER_PORT=8080
SERVER_TIMEOUT=30s
SERVER_READ_TIMEOUT=15s
SERVER_WRITE_TIMEOUT=15s
SERVER_MAX_HEADER_BYTES=1048576

# Database Configuration
DB_TYPE=mysql
DB_HOST=localhost
DB_PORT=3307
DB_USERNAME=your_db_username
DB_PASSWORD=your_db_password
DB_DATABASE=wnapi
DB_MAX_OPEN_CONNS=20
DB_MAX_IDLE_CONNS=10
DB_CONN_MAX_LIFETIME=3600s
DB_MIGRATION_PATH=./migrations

# Module Configuration  
MODULES_ENABLED=hello,auth

# Auth Module Configuration
AUTH_JWT_SECRET=your_very_secure_jwt_secret_key_here
AUTH_ACCESS_TOKEN_EXPIRY=15m
AUTH_REFRESH_TOKEN_EXPIRY=168h

# Hello Module Configuration
HELLO_MESSAGE=Xin chào từ module Hello!

# Auth Module Message  
AUTH_MESSAGE=Xin chào từ module Auth!
```

### 3.2. Giai đoạn 2: Cập nhật code

#### 3.2.1. Cập nhật config loader
- Tạo package để đọc environment variables
- Sử dụng thư viện `godotenv` để load file .env
- Cập nhật struct config để map với environment variables

```go
// internal/core/config.go
type Config struct {
    App      AppConfig      `env:"APP"`
    Server   ServerConfig   `env:"SERVER"`
    Database DatabaseConfig `env:"DB"`
    Modules  ModulesConfig  `env:"MODULES"`
    Auth     AuthConfig     `env:"AUTH"`
}

type AppConfig struct {
    Name     string `env:"NAME" envDefault:"wnapi"`
    Version  string `env:"VERSION" envDefault:"0.1.0"`
    Env      string `env:"ENV" envDefault:"development"`
    LogLevel string `env:"LOG_LEVEL" envDefault:"info"`
}

type ServerConfig struct {
    Host           string        `env:"HOST" envDefault:"0.0.0.0"`
    Port           int           `env:"PORT" envDefault:"8080"`
    Timeout        time.Duration `env:"TIMEOUT" envDefault:"30s"`
    ReadTimeout    time.Duration `env:"READ_TIMEOUT" envDefault:"15s"`
    WriteTimeout   time.Duration `env:"WRITE_TIMEOUT" envDefault:"15s"`
    MaxHeaderBytes int           `env:"MAX_HEADER_BYTES" envDefault:"1048576"`
}

type DatabaseConfig struct {
    Type            string        `env:"TYPE" envDefault:"mysql"`
    Host            string        `env:"HOST" envDefault:"localhost"`
    Port            int           `env:"PORT" envDefault:"3307"`
    Username        string        `env:"USERNAME"`
    Password        string        `env:"PASSWORD"`
    Database        string        `env:"DATABASE" envDefault:"wnapi"`
    MaxOpenConns    int           `env:"MAX_OPEN_CONNS" envDefault:"20"`
    MaxIdleConns    int           `env:"MAX_IDLE_CONNS" envDefault:"10"`
    ConnMaxLifetime time.Duration `env:"CONN_MAX_LIFETIME" envDefault:"3600s"`
    MigrationPath   string        `env:"MIGRATION_PATH" envDefault:"./migrations"`
}
```

#### 3.2.2. Tạo function load configuration
```go
func LoadConfig() (*Config, error) {
    // Load .env file
    if err := godotenv.Load(); err != nil {
        log.Printf("Warning: .env file not found or could not be loaded: %v", err)
    }
    
    cfg := &Config{}
    if err := env.Parse(cfg); err != nil {
        return nil, fmt.Errorf("failed to parse environment variables: %w", err)
    }
    
    return cfg, nil
}
```

#### 3.2.3. Cập nhật module configuration
- Tạo cơ chế để modules đọc config từ environment variables
- Cập nhật các modules hiện tại (hello, auth) để sử dụng env vars

### 3.3. Giai đoạn 3: Migration và testing

#### 3.3.1. Tạo script migration
Tạo script `scripts/migrate-to-env.sh` để:
- Đọc các file YAML hiện tại
- Chuyển đổi sang format .env
- Backup các file YAML cũ
- Tạo file .env mới

#### 3.3.2. Testing
- Test với các environment khác nhau (development, production)
- Verify rằng tất cả config được load đúng
- Test override environment variables

### 3.4. Giai đoạn 4: Deployment và cleanup

#### 3.4.1. Cập nhật Docker
- Cập nhật Dockerfile để copy .env.example
- Cập nhật docker-compose.yml để sử dụng environment variables
- Cập nhật docker-compose.prod.yml

#### 3.4.2. Cập nhật documentation
- Cập nhật README.md với hướng dẫn setup .env
- Cập nhật docs/development.md
- Tạo migration guide

#### 3.4.3. Cleanup
- Xóa các file YAML cũ (sau khi verify)
- Cập nhật .gitignore để exclude .env nhưng include .env.example

## 4. Dependencies cần thêm

```go
// go.mod
require (
    github.com/caarlos0/env/v11
    github.com/joho/godotenv v1.4.0
)
```

## 5. File structure sau khi migration

```
config/
    .env.example          # Template file
    .env                 # Actual config (gitignored)
    legacy/              # Backup YAML files
        app.yaml
        db.yaml  
        modules.yaml
```

## 6. Lợi ích của việc chuyển đổi

### 6.1. Bảo mật
- Sensitive data (passwords, secrets) không bị commit vào git
- Dễ dàng quản lý các môi trường khác nhau

### 6.2. DevOps friendly
- Tương thích với container environments
- Dễ dàng override trong CI/CD pipelines
- Tuân thủ 12-factor app principles

### 6.3. Đơn giản hóa
- Ít file configuration hơn
- Format đơn giản, dễ đọc
- Dễ dàng automation

## 7. Timeline thực hiện

| Giai đoạn | Thời gian | Mô tả |
|-----------|-----------|-------|
| Giai đoạn 1 | 1 ngày | Thiết kế và chuẩn bị |
| Giai đoạn 2 | 2-3 ngày | Cập nhật code và testing |
| Giai đoạn 3 | 1 ngày | Migration và verification |
| Giai đoạn 4 | 1 ngày | Deployment và cleanup |
| **Tổng** | **5-6 ngày** | |

## 8. Risk và mitigation

### 8.1. Risks
- Mất mát configuration data trong quá trình migration
- Breaking changes cho development environment
- Tương thích với existing deployment

### 8.2. Mitigation
- Backup tất cả file YAML trước khi migration
- Phát triển song song với YAML (backward compatibility)
- Detailed testing trước khi deploy production
- Rollback plan sẵn sàng

## 9. Checklist thực hiện

- [ ] Thiết kế cấu trúc .env
- [ ] Tạo .env.example template
- [ ] Cập nhật config loader
- [ ] Implement env parsing
- [ ] Update modules configuration
- [ ] Tạo migration script
- [ ] Testing với development environment  
- [ ] Cập nhật Docker configuration
- [ ] Cập nhật documentation
- [ ] Deploy to staging
- [ ] Deploy to production
- [ ] Cleanup YAML files
- [ ] Update .gitignore

## 10. Post-migration

### 10.1. Monitoring
- Monitor application startup cho errors
- Verify tất cả modules load correctly
- Check performance impact (nếu có)

### 10.2. Documentation
- Cập nhật onboarding docs cho new developers
- Tạo troubleshooting guide
- Best practices cho environment management

---

**Người thực hiện:** Development Team  
**Reviewer:** Tech Lead  
**Ngày tạo:** 24/05/2025  
**Version:** 1.0