version: '3.8'

services:
  # API service
  api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: wnapi
    restart: unless-stopped
    ports:
      - "8080:8080"
    volumes:
      - ./logs:/app/logs
      - ./config:/app/config
      - ./projects:/app/projects
    depends_on:
      - mysql
    networks:
      - wnapi-network
    command: ["--project=sample"]

  # MySQL database
  mysql:
    image: mysql:8.0
    container_name: wnapi-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: wnapi
      MYSQL_USER: wnapi
      MYSQL_PASSWORD: password
    ports:
      - "3307:3306"
    volumes:
      - mysql-data:/var/lib/mysql
    networks:
      - wnapi-network
    command: --default-authentication-plugin=mysql_native_password

  # Adminer để quản lý database
  adminer:
    image: adminer
    container_name: wnapi-adminer
    restart: unless-stopped
    ports:
      - "8081:8080"
    depends_on:
      - mysql
    networks:
      - wnapi-network

volumes:
  mysql-data:
    driver: local

networks:
  wnapi-network:
    driver: bridge 