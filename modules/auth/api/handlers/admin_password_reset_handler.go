package handlers

import (
	"net/http"

	"wnapi/internal/pkg/response"
	"wnapi/modules/auth/dto"
	"wnapi/modules/auth/service"

	"github.com/gin-gonic/gin"
)

type AdminPasswordResetHandler struct {
	passwordResetService *service.PasswordResetService
}

func NewAdminPasswordResetHandler(passwordResetService *service.PasswordResetService) *AdminPasswordResetHandler {
	return &AdminPasswordResetHandler{
		passwordResetService: passwordResetService,
	}
}

// ForgotPassword xử lý API quên mật khẩu
func (h *AdminPasswordResetHandler) ForgotPassword(c *gin.Context) {
	var req dto.ForgotPasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Định dạng yêu cầu không hợp lệ", "INVALID_REQUEST", details)
		return
	}

	err := h.passwordResetService.ForgotPassword(c.Request.Context(), &req)
	if err != nil {
		if err == service.ErrUserNotFound {
			response.Error(c, http.StatusNotFound, "Email không tồn tại trong hệ thống", "USER_NOT_FOUND")
			return
		}
		response.Error(c, http.StatusInternalServerError, "Không thể xử lý yêu cầu quên mật khẩu", "PASSWORD_RESET_FAILED")
		return
	}

	response.Success(c, map[string]string{"message": "Email hướng dẫn đặt lại mật khẩu đã được gửi"}, nil)
}

// VerifyResetToken xử lý API xác thực token
func (h *AdminPasswordResetHandler) VerifyResetToken(c *gin.Context) {
	var req dto.VerifyResetTokenRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Định dạng yêu cầu không hợp lệ", "INVALID_REQUEST", details)
		return
	}

	resp, err := h.passwordResetService.VerifyResetToken(c.Request.Context(), &req)
	if err != nil {
		if err == service.ErrInvalidToken {
			response.Error(c, http.StatusBadRequest, "Token không hợp lệ hoặc đã hết hạn", "INVALID_TOKEN")
			return
		}
		response.Error(c, http.StatusInternalServerError, "Không thể xác thực token", "TOKEN_VERIFICATION_FAILED")
		return
	}

	response.Success(c, resp, nil)
}

// ResetPassword xử lý API đặt lại mật khẩu
func (h *AdminPasswordResetHandler) ResetPassword(c *gin.Context) {
	var req dto.ResetPasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Định dạng yêu cầu không hợp lệ", "INVALID_REQUEST", details)
		return
	}

	err := h.passwordResetService.ResetPassword(c.Request.Context(), &req)
	if err != nil {
		switch err {
		case service.ErrInvalidToken:
			response.Error(c, http.StatusBadRequest, "Token không hợp lệ hoặc đã hết hạn", "INVALID_TOKEN")
		case service.ErrPasswordsNotMatch:
			details := []interface{}{map[string]string{"field": "confirm_password", "message": "Mật khẩu và xác nhận mật khẩu không khớp"}}
			response.ErrorWithDetails(c, http.StatusBadRequest, "Mật khẩu không khớp", "PASSWORDS_NOT_MATCH", details)
		case service.ErrWeakPassword:
			details := []interface{}{map[string]string{"field": "password", "message": "Mật khẩu phải có ít nhất 8 ký tự, bao gồm ít nhất 1 chữ hoa, 1 chữ thường, 1 số và 1 ký tự đặc biệt"}}
			response.ErrorWithDetails(c, http.StatusBadRequest, "Mật khẩu không đáp ứng yêu cầu bảo mật", "WEAK_PASSWORD", details)
		default:
			response.Error(c, http.StatusInternalServerError, "Không thể đặt lại mật khẩu", "PASSWORD_RESET_FAILED")
		}
		return
	}

	response.Success(c, map[string]string{"message": "Đặt lại mật khẩu thành công"}, nil)
}
