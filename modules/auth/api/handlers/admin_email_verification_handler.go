package handlers

import (
	"net/http"

	"wnapi/internal/pkg/response"
	"wnapi/modules/auth/dto"
	"wnapi/modules/auth/internal"

	"github.com/gin-gonic/gin"
)

// AdminEmailVerificationHandler xử lý các yêu cầu xác thực email cho admin
type AdminEmailVerificationHandler struct {
	authService internal.AuthService
}

// NewAdminEmailVerificationHandler tạo một instance mới của AdminEmailVerificationHandler
func NewAdminEmailVerificationHandler(authService internal.AuthService) *AdminEmailVerificationHandler {
	return &AdminEmailVerificationHandler{
		authService: authService,
	}
}

// VerifyEmail xử lý việc xác thực email thông qua token
func (h *AdminEmailVerificationHandler) VerifyEmail(c *gin.Context) {
	var req dto.VerifyEmailRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Định dạng yêu cầu không hợp lệ", "INVALID_REQUEST", details)
		return
	}

	resp, err := h.authService.VerifyEmail(c.Request.Context(), req.Token)
	if err != nil {
		if err.Error() == "token không hợp lệ" || err.Error() == "token đã hết hạn" {
			response.Error(c, http.StatusNotFound, "Mã xác thực không hợp lệ hoặc đã hết hạn", "INVALID_TOKEN")
		} else {
			response.Error(c, http.StatusInternalServerError, "Không thể xác thực email", "EMAIL_VERIFICATION_FAILED")
		}
		return
	}

	response.Success(c, resp, nil)
}

// ResendVerification xử lý việc gửi lại email xác thực
func (h *AdminEmailVerificationHandler) ResendVerification(c *gin.Context) {
	var req dto.ResendVerificationEmailRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Định dạng yêu cầu không hợp lệ", "INVALID_REQUEST", details)
		return
	}

	err := h.authService.ResendVerificationEmail(c.Request.Context(), req.Email)
	if err != nil {
		if err.Error() == "không tìm thấy người dùng với email này" {
			response.Error(c, http.StatusNotFound, "Không tìm thấy người dùng với email này", "USER_NOT_FOUND")
		} else {
			response.Error(c, http.StatusInternalServerError, "Không thể gửi lại email xác thực", "EMAIL_VERIFICATION_FAILED")
		}
		return
	}

	response.Success(c, map[string]string{"message": "Email xác thực đã được gửi lại"}, nil)
}
