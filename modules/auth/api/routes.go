package api

import (
	"fmt"
	"os"
	"wnapi/internal/core"
	"wnapi/internal/pkg/auth"
	"wnapi/internal/pkg/tracing"
	"wnapi/modules/auth/api/handlers"
	"wnapi/modules/auth/internal"
	"wnapi/modules/auth/service"

	"github.com/gin-gonic/gin"
)

// Handler là đối tượng chính xử lý API cho module Auth
type Handler struct {
	adminAuthHandler              *handlers.AdminAuthHandler
	adminPasswordResetHandler     *handlers.AdminPasswordResetHandler
	adminEmailVerificationHandler *handlers.AdminEmailVerificationHandler
	routes                        []string
	jwtService                    *auth.JWTService
}

// NewHandler tạo một handler mới
func NewHandler(authService internal.AuthService, passwordResetService *service.PasswordResetService, jwtConfig auth.JWTConfig) *Handler {
	// Thiết lập mode cho Gin dựa vào biến môi trường
	appEnv := os.Getenv("APP_ENV")
	if appEnv != "production" {
		gin.SetMode(gin.DebugMode)
	}

	jwtService := auth.NewJWTService(jwtConfig)
	return &Handler{
		adminAuthHandler:              handlers.NewAdminAuthHandler(authService),
		adminPasswordResetHandler:     handlers.NewAdminPasswordResetHandler(passwordResetService),
		adminEmailVerificationHandler: handlers.NewAdminEmailVerificationHandler(authService),
		routes:                        make([]string, 0),
		jwtService:                    jwtService,
	}
}

// RegisterRoutes đăng ký tất cả routes cho module Auth
func (h *Handler) RegisterRoutes(server *core.Server) error {

	// API Group
	apiGroup := server.Group("/api/v1/auth")

	// Thêm middleware tracing cho tất cả các route auth
	apiGroup.Use(tracing.GinMiddleware("auth"))

	// Lưu lại danh sách các route để hiển thị
	basePath := "/api/v1/auth"

	// Health check endpoint
	apiGroup.GET("/healthy", h.healthCheck)
	h.routes = append(h.routes, fmt.Sprintf("GET %s/healthy", basePath))

	// ===== BASIC AUTH ROUTES =====
	// Không yêu cầu xác thực
	apiGroup.POST("/login", h.adminAuthHandler.Login)
	h.routes = append(h.routes, fmt.Sprintf("POST %s/login", basePath))

	apiGroup.POST("/signin", h.adminAuthHandler.Login) // Alias for login
	h.routes = append(h.routes, fmt.Sprintf("POST %s/signin", basePath))

	apiGroup.POST("/register", h.adminAuthHandler.Register)
	h.routes = append(h.routes, fmt.Sprintf("POST %s/register", basePath))

	apiGroup.POST("/signup", h.adminAuthHandler.Register) // Alias for register
	h.routes = append(h.routes, fmt.Sprintf("POST %s/signup", basePath))

	apiGroup.POST("/refresh-token", h.adminAuthHandler.RefreshToken)
	h.routes = append(h.routes, fmt.Sprintf("POST %s/refresh-token", basePath))

	// ===== PASSWORD RESET ROUTES =====
	apiGroup.POST("/forgot-password", h.adminPasswordResetHandler.ForgotPassword)
	h.routes = append(h.routes, fmt.Sprintf("POST %s/forgot-password", basePath))

	apiGroup.GET("/verify-reset-token", h.adminPasswordResetHandler.VerifyResetToken)
	h.routes = append(h.routes, fmt.Sprintf("GET %s/verify-reset-token", basePath))

	apiGroup.POST("/reset-password", h.adminPasswordResetHandler.ResetPassword)
	h.routes = append(h.routes, fmt.Sprintf("POST %s/reset-password", basePath))

	// ===== EMAIL VERIFICATION ROUTES =====
	apiGroup.GET("/verify-email", h.adminEmailVerificationHandler.VerifyEmail)
	h.routes = append(h.routes, fmt.Sprintf("GET %s/verify-email", basePath))

	apiGroup.POST("/resend-verification", h.adminEmailVerificationHandler.ResendVerification)
	h.routes = append(h.routes, fmt.Sprintf("POST %s/resend-verification", basePath))

	// Yêu cầu xác thực - sử dụng JWT middleware
	authenticated := apiGroup.Group("/")
	authenticated.Use(h.jwtService.JWTAuthMiddleware())
	{
		authenticated.POST("/logout", h.adminAuthHandler.Logout)
		h.routes = append(h.routes, fmt.Sprintf("POST %s/logout", basePath))

		authenticated.PUT("/change-password", h.adminAuthHandler.ChangePassword)
		h.routes = append(h.routes, fmt.Sprintf("PUT %s/change-password", basePath))

		authenticated.GET("/profile", h.adminAuthHandler.GetProfile)
		h.routes = append(h.routes, fmt.Sprintf("GET %s/profile", basePath))
	}

	// In danh sách routes nếu không ở môi trường production
	if gin.Mode() == gin.DebugMode {
		fmt.Println("=== AUTH MODULE ROUTES ===")
		for _, route := range h.routes {
			fmt.Println(route)
		}
		fmt.Println("=========================")
	}

	return nil
}

// ListRoutes trả về danh sách tất cả các routes đã đăng ký
func (h *Handler) ListRoutes() []string {
	return h.routes
}

// healthCheck là endpoint kiểm tra trạng thái hoạt động của module
func (h *Handler) healthCheck(c *gin.Context) {
	c.JSON(200, gin.H{
		"status":  "ok",
		"module":  "auth",
		"message": "Auth module is running",
	})
}

// AuthHandler is an alias for Handler to maintain backward compatibility
type AuthHandler = Handler

// NewAuthHandler creates a new AuthHandler (alias for NewHandler)
func NewAuthHandler(authService internal.AuthService, passwordResetService *service.PasswordResetService, jwtConfig auth.JWTConfig) *AuthHandler {
	return NewHandler(authService, passwordResetService, jwtConfig)
}
