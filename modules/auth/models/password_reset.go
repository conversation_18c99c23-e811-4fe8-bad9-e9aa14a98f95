package models

import (
	"time"
)

// PasswordReset là model cho bảng auth_password_resets
type PasswordReset struct {
	ID        int       `db:"id" json:"id" gorm:"primaryKey"`
	Email     string    `db:"email" json:"email"`
	Token     string    `db:"token" json:"token"`
	Used      bool      `db:"used" json:"used"`
	ExpiresAt time.Time `db:"expires_at" json:"expires_at"`
	CreatedAt time.Time `db:"created_at" json:"created_at"`
	UpdatedAt time.Time `db:"updated_at" json:"updated_at"`
}

// TableName xác định tên bảng trong cơ sở dữ liệu
func (PasswordReset) TableName() string {
	return "auth_password_resets"
}
