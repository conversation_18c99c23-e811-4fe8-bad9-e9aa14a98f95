package models

import (
	"time"
)

// EmailVerification đại diện cho một token xác thực email
type EmailVerification struct {
	ID        int64     `db:"id" json:"id" gorm:"primaryKey"`
	UserID    int64     `db:"user_id" json:"user_id"`
	Email     string    `db:"email" json:"email"`
	Token     string    `db:"token" json:"token"`
	Verified  bool      `db:"verified" json:"verified"`
	ExpiresAt time.Time `db:"expires_at" json:"expires_at"`
	CreatedAt time.Time `db:"created_at" json:"created_at"`
	UpdatedAt time.Time `db:"updated_at" json:"updated_at"`
}

// TableName xác định tên bảng trong cơ sở dữ liệu
func (EmailVerification) TableName() string {
	return "auth_email_verifications"
}
