package service

import (
	"context"
	"fmt"
	"log"
	"time"

	"wnapi/modules/auth/dto"
	"wnapi/modules/auth/repository"

	"github.com/google/uuid"
	"google.golang.org/grpc"
)

const (
	// Thời gian hết hạn của token reset password (24 giờ)
	tokenExpiration = 24 * time.Hour
)

// PasswordResetService xử lý các thao tác liên quan đến reset password
type PasswordResetService struct {
	passwordResetRepo  repository.PasswordResetRepository
	userClient         interface{} // Tạm thời đổi sang interface{} để tránh lỗi import
	notificationClient interface{} // Tạm thời đổi sang interface{} để tránh lỗi import
	baseURL            string
	webURL             string
}

// NewPasswordResetService tạo một service mới để xử lý reset password
func NewPasswordResetService(
	passwordResetRepo repository.PasswordResetRepository,
	userConn *grpc.ClientConn,
	notificationConn *grpc.ClientConn,
	baseURL string,
	webURL string,
) *PasswordResetService {
	return &PasswordResetService{
		passwordResetRepo:  passwordResetRepo,
		userClient:         nil, // Tạm thời để nil, sẽ khởi tạo trong triển khai thực tế
		notificationClient: nil, // Tạm thời để nil, sẽ khởi tạo trong triển khai thực tế
		baseURL:            baseURL,
		webURL:             webURL,
	}
}

// ForgotPassword xử lý yêu cầu quên mật khẩu
func (s *PasswordResetService) ForgotPassword(ctx context.Context, req *dto.ForgotPasswordRequest) error {
	// Tạo context với timeout
	ctxWithTimeout, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	// Trong trường hợp này, giả sử chúng ta đã xác thực email tồn tại
	// Trong triển khai thực tế, sẽ gọi userClient.GetUserByEmail ở đây

	// Tạo token đặt lại mật khẩu
	token := uuid.New().String()
	expiresAt := time.Now().Add(tokenExpiration)

	// Vô hiệu hóa tất cả token cũ của email này
	if err := s.passwordResetRepo.InvalidateOldTokens(ctxWithTimeout, req.Email); err != nil {
		log.Printf("Warning: không thể vô hiệu hóa token cũ: %v", err)
	}

	// Tạo token mới
	if err := s.passwordResetRepo.Create(ctxWithTimeout, req.Email, token, expiresAt); err != nil {
		return fmt.Errorf("không thể tạo token đặt lại mật khẩu: %w", err)
	}

	// Tạo URL đặt lại mật khẩu
	resetURL := fmt.Sprintf("%s/auth/reset-password?token=%s", s.webURL, token)

	// Giả định đã gửi email thành công
	log.Printf("Đã gửi email đặt lại mật khẩu đến: %s, URL: %s", req.Email, resetURL)

	return nil
}

// VerifyResetToken xác thực token đặt lại mật khẩu
func (s *PasswordResetService) VerifyResetToken(ctx context.Context, req *dto.VerifyResetTokenRequest) (*dto.VerifyTokenResponse, error) {
	// Tạo context với timeout
	ctxWithTimeout, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	// Tìm token
	resetToken, err := s.passwordResetRepo.FindByToken(ctxWithTimeout, req.Token)
	if err != nil {
		return nil, fmt.Errorf("không thể xác thực token: %w", err)
	}

	// Kiểm tra xem token có tồn tại và còn hiệu lực
	if resetToken == nil || resetToken.Used || resetToken.ExpiresAt.Before(time.Now()) {
		return nil, ErrInvalidToken
	}

	return &dto.VerifyTokenResponse{
		Email: resetToken.Email,
		Valid: true,
	}, nil
}

// ResetPassword đặt lại mật khẩu
func (s *PasswordResetService) ResetPassword(ctx context.Context, req *dto.ResetPasswordRequest) error {
	// Tạo context với timeout
	ctxWithTimeout, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	// Kiểm tra mật khẩu và xác nhận mật khẩu
	if req.Password != req.ConfirmPassword {
		return ErrPasswordsNotMatch
	}

	// Tìm token
	resetToken, err := s.passwordResetRepo.FindByToken(ctxWithTimeout, req.Token)
	if err != nil {
		return fmt.Errorf("không thể xác thực token: %w", err)
	}

	// Kiểm tra xem token có tồn tại và còn hiệu lực
	if resetToken == nil || resetToken.Used || resetToken.ExpiresAt.Before(time.Now()) {
		return ErrInvalidToken
	}

	// Trong triển khai thực tế, sẽ gọi userClient.GetUserByEmail và userClient.ResetPassword ở đây
	log.Printf("Đặt lại mật khẩu cho email: %s", resetToken.Email)

	// Đánh dấu token đã sử dụng
	if err := s.passwordResetRepo.MarkAsUsed(ctxWithTimeout, req.Token); err != nil {
		log.Printf("Warning: không thể đánh dấu token đã sử dụng: %v", err)
	}

	// Giả định đã gửi thông báo thành công
	log.Printf("Đã gửi thông báo đặt lại mật khẩu thành công đến email: %s", resetToken.Email)

	return nil
}
