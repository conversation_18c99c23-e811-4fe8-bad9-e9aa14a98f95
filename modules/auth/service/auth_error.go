package service

import (
	"errors"
)

// Định nghĩa các lỗi cho auth service
var (
	// Các lỗi chung
	ErrUserNotFound       = errors.New("không tìm thấy người dùng")
	ErrInvalidCredentials = errors.New("thông tin đăng nhập không hợp lệ")

	// Lỗi liên quan đến đặt lại mật khẩu
	ErrInvalidToken        = errors.New("token không hợp lệ hoặc đã hết hạn")
	ErrPasswordsNotMatch   = errors.New("mật khẩu và xác nhận mật khẩu không khớp")
	ErrWeakPassword        = errors.New("mật khẩu không đáp ứng yêu cầu bảo mật")
	ErrPasswordResetFailed = errors.New("không thể đặt lại mật khẩu")

	// Lỗi liên quan đến xác thực email
	ErrEmailVerificationFailed = errors.New("không thể xác thực email")
	ErrEmailAlreadyVerified    = errors.New("email đã được xác thực")
	ErrEmailNotFound           = errors.New("không tìm thấy email")
)
