package service

import (
	"context"
	"errors"
	"fmt"
	"time"
	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/tracing"
	"wnapi/modules/auth/dto"
	"wnapi/modules/auth/internal"
	"wnapi/modules/auth/models"

	"github.com/golang-jwt/jwt/v4"
	"github.com/google/uuid"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"golang.org/x/crypto/bcrypt"
)

// Constants for tracing attributes
const (
	AttrUserEmailDomain  = "user.email_domain"
	AttrUserTenantID     = "user.tenant_id"
	AttrUserID           = "user.id"
	AttrUserStatus       = "user.status"
	AttrAuthFailedReason = "auth.failed_reason"
	AttrAuthSuccess      = "auth.success"
)

// Service triển khai AuthService interface
type Service struct {
	repo   internal.Repository
	config internal.AuthConfig
	logger logger.Logger
}

// NewService tạo một auth service mới
func NewService(repo internal.Repository, config internal.AuthConfig, log logger.Logger) internal.AuthService {
	// Đặt giá trị mặc định
	if config.AccessTokenExpiry == 0 {
		config.AccessTokenExpiry = 15 * time.Minute
	}
	if config.RefreshTokenExpiry == 0 {
		config.RefreshTokenExpiry = 7 * 24 * time.Hour // 7 ngày
	}

	return &Service{
		repo:   repo,
		config: config,
		logger: log,
	}
}

// Register đăng ký người dùng mới
func (s *Service) Register(ctx context.Context, req dto.RegisterRequest) (*internal.UserInfo, error) {
	// Tạo span cho toàn bộ quá trình đăng ký
	ctx, span := tracing.StartSpan(ctx, "auth-service", "register")
	defer span.End()

	// Thêm thuộc tính vào span
	tracing.AddSpanAttributes(ctx,
		attribute.String("auth.username", req.Username),
		attribute.String("auth.email", req.Email),
		attribute.String("auth.user_type", req.UserType),
	)

	// Kiểm tra username đã tồn tại chưa
	err := tracing.WithSpan(ctx, "auth-service", "check_existing_username", func(ctx context.Context) error {
		_, err := s.repo.GetUserByUsername(ctx, req.Username)
		if err == nil {
			tracing.AddSpanAttributes(ctx, attribute.String("auth.error", "username_exists"))
			return internal.ErrUserAlreadyExists
		} else if !errors.Is(err, internal.ErrUserNotFound) {
			s.logger.Error("Failed to check existing user", logger.String("error", err.Error()))
			return err
		}
		return nil
	})

	if err != nil {
		tracing.RecordError(ctx, err)
		return nil, err
	}

	// Kiểm tra email đã tồn tại chưa
	if req.Email != "" {
		err := tracing.WithSpan(ctx, "auth-service", "check_existing_email", func(ctx context.Context) error {
			_, err := s.repo.GetUserByEmail(ctx, req.Email)
			if err == nil {
				tracing.AddSpanAttributes(ctx, attribute.String("auth.error", "email_exists"))
				return internal.ErrUserAlreadyExists
			} else if !errors.Is(err, internal.ErrUserNotFound) {
				s.logger.Error("Failed to check existing email", logger.String("error", err.Error()))
				return err
			}
			return nil
		})

		if err != nil {
			tracing.RecordError(ctx, err)
			return nil, err
		}
	}

	// Tạo user mới
	user := &internal.User{
		Username:        req.Username,
		Email:           req.Email,
		FullName:        req.FullName,
		Status:          "active",
		IsEmailVerified: false,
		UserType:        "admin",
	}

	// Lưu vào database
	err = tracing.WithSpan(ctx, "auth-service", "create_user", func(ctx context.Context) error {
		if err := s.repo.CreateUser(ctx, user, req.Password); err != nil {
			s.logger.Error("Failed to create user", logger.String("error", err.Error()))
			return err
		}
		return nil
	})

	if err != nil {
		tracing.RecordError(ctx, err)
		return nil, err
	}

	// Đánh dấu thành công trong span
	tracing.SetSpanStatus(ctx, codes.Ok, "User registered successfully")
	tracing.AddSpanAttributes(ctx,
		attribute.Int("auth.user_id", int(user.UserID)),
		attribute.Bool("auth.success", true),
	)

	// Trả về thông tin user
	return &internal.UserInfo{
		UserID:   user.UserID,
		TenantID: user.TenantID,
		Username: user.Username,
		Email:    user.Email,
	}, nil
}

// Login xác thực người dùng và trả về token
func (s *Service) Login(ctx context.Context, req dto.LoginRequest) (*dto.LoginResponse, error) {
	// Tạo span cho toàn bộ quá trình đăng nhập
	ctx, span := tracing.StartSpan(ctx, "auth-service", "login")
	defer span.End()

	// Thêm thuộc tính vào span (che dấu thông tin nhạy cảm)
	tracing.AddSpanAttributes(ctx,
		attribute.String("auth.email", req.Email),
		attribute.Bool("auth.admin_login", req.AdminLogin),
	)

	// Lấy user từ database bằng email
	var user *internal.User
	err := tracing.WithSpan(ctx, "auth-service", "get_user_by_email", func(ctx context.Context) error {
		var err error
		user, err = s.repo.GetUserByEmail(ctx, req.Email)
		if err != nil {
			if errors.Is(err, internal.ErrUserNotFound) {
				tracing.AddSpanAttributes(ctx, attribute.String("auth.error", "user_not_found"))
				return internal.ErrInvalidCredentials
			}
			s.logger.Error("Failed to get user", logger.String("error", err.Error()))
			return err
		}
		return nil
	})

	if err != nil {
		tracing.RecordError(ctx, err)
		return nil, err
	}

	// Kiểm tra password
	err = tracing.WithSpan(ctx, "auth-service", "verify_password", func(ctx context.Context) error {
		if err := bcrypt.CompareHashAndPassword([]byte(user.PasswordHash), []byte(req.Password)); err != nil {
			s.logger.Warn("Invalid password attempt", logger.String("email", req.Email))
			tracing.AddSpanAttributes(ctx, attribute.String("auth.error", "invalid_password"))
			return internal.ErrInvalidCredentials
		}
		return nil
	})

	if err != nil {
		tracing.RecordError(ctx, err)
		return nil, err
	}

	// Tạo JWT token
	var accessToken string
	err = tracing.WithSpan(ctx, "auth-service", "generate_tokens", func(ctx context.Context) error {
		var err error
		accessToken, _, err = s.generateJWTToken(int(user.UserID), user.Username, s.config.AccessTokenExpiry)
		if err != nil {
			s.logger.Error("Failed to generate access token", logger.String("error", err.Error()))
			return err
		}
		return nil
	})

	if err != nil {
		tracing.RecordError(ctx, err)
		return nil, err
	}

	// Tạo refresh token
	refreshToken := uuid.NewString()
	refreshExpiry := time.Now().Add(s.config.RefreshTokenExpiry)

	// Lưu refresh token vào database
	err = tracing.WithSpan(ctx, "auth-service", "save_refresh_token", func(ctx context.Context) error {
		token := &internal.Token{
			UserID:       int(user.UserID),
			TokenType:    internal.TokenTypeRefresh,
			AccessToken:  accessToken,
			RefreshToken: refreshToken,
			ExpiresAt:    refreshExpiry,
		}

		if err := s.repo.CreateToken(ctx, token); err != nil {
			s.logger.Error("Failed to save refresh token", logger.String("error", err.Error()))
			return err
		}
		return nil
	})

	if err != nil {
		tracing.RecordError(ctx, err)
		return nil, err
	}

	// Đánh dấu thành công trong span
	tracing.SetSpanStatus(ctx, codes.Ok, "Login successful")
	tracing.AddSpanAttributes(ctx,
		attribute.Int("auth.user_id", int(user.UserID)),
		attribute.Bool("auth.success", true),
	)

	// Trả về response
	return &dto.LoginResponse{
		AccessToken:           accessToken,
		AccessTokenExpiresIn:  int(s.config.AccessTokenExpiry.Seconds()),
		RefreshToken:          refreshToken,
		RefreshTokenExpiresIn: int(s.config.RefreshTokenExpiry.Seconds()),
		TokenType:             "Bearer",
		UserID:                int64(user.UserID),
		Email:                 user.Email,
		TenantID:              int(user.TenantID),
	}, nil
}

// RefreshToken làm mới access token
func (s *Service) RefreshToken(ctx context.Context, refreshToken string) (*dto.LoginResponse, error) {
	// Tạo span cho toàn bộ quá trình làm mới token
	ctx, span := tracing.StartSpan(ctx, "auth-service", "refresh_token")
	defer span.End()

	// Che giấu token trong span
	if len(refreshToken) > 8 {
		maskedToken := refreshToken[0:8] + "..."
		tracing.AddSpanAttributes(ctx, attribute.String("auth.refresh_token", maskedToken))
	}

	// Lấy token từ database
	var token *internal.Token
	err := tracing.WithSpan(ctx, "auth-service", "get_token", func(ctx context.Context) error {
		var err error
		token, err = s.repo.GetTokenByValue(ctx, refreshToken, internal.TokenTypeRefresh)
		if err != nil {
			if errors.Is(err, internal.ErrTokenNotFound) {
				tracing.AddSpanAttributes(ctx, attribute.String("auth.error", "token_not_found"))
				return internal.ErrInvalidToken
			}
			s.logger.Error("Failed to get refresh token", logger.String("error", err.Error()))
			return err
		}
		return nil
	})

	if err != nil {
		tracing.RecordError(ctx, err)
		return nil, err
	}

	// Kiểm tra token có hết hạn không
	if token.IsExpired() {
		tracing.AddSpanAttributes(ctx, attribute.String("auth.error", "token_expired"))

		// Xóa token đã hết hạn
		_ = tracing.WithSpan(ctx, "auth-service", "delete_expired_token", func(ctx context.Context) error {
			if err := s.repo.DeleteToken(ctx, token.ID); err != nil {
				s.logger.Error("Failed to delete expired token", logger.String("error", err.Error()))
			}
			return nil
		})

		tracing.RecordError(ctx, internal.ErrExpiredToken)
		return nil, internal.ErrExpiredToken
	}

	// Lấy user từ database
	var user *internal.User
	err = tracing.WithSpan(ctx, "auth-service", "get_user", func(ctx context.Context) error {
		var err error
		user, err = s.repo.GetUserByID(ctx, int(token.UserID))
		if err != nil {
			s.logger.Error("Failed to get user for refresh token", logger.String("error", err.Error()))
			return err
		}
		return nil
	})

	if err != nil {
		tracing.RecordError(ctx, err)
		return nil, err
	}

	// Tạo access token mới
	var accessToken string
	err = tracing.WithSpan(ctx, "auth-service", "generate_access_token", func(ctx context.Context) error {
		var err error
		accessToken, _, err = s.generateJWTToken(int(user.UserID), user.Username, s.config.AccessTokenExpiry)
		if err != nil {
			s.logger.Error("Failed to generate new access token", logger.String("error", err.Error()))
			return err
		}
		return nil
	})

	if err != nil {
		tracing.RecordError(ctx, err)
		return nil, err
	}

	// Đánh dấu thành công trong span
	tracing.SetSpanStatus(ctx, codes.Ok, "Token refreshed successfully")
	tracing.AddSpanAttributes(ctx,
		attribute.Int("auth.user_id", int(user.UserID)),
		attribute.Bool("auth.success", true),
	)

	// Trả về response
	return &dto.LoginResponse{
		AccessToken:           accessToken,
		AccessTokenExpiresIn:  int(s.config.AccessTokenExpiry.Seconds()),
		RefreshToken:          refreshToken, // Giữ nguyên refresh token
		RefreshTokenExpiresIn: int(s.config.RefreshTokenExpiry.Seconds()),
		TokenType:             "Bearer",
		UserID:                int64(user.UserID),
		Email:                 user.Email,
		TenantID:              int(user.TenantID),
	}, nil
}

// ValidateToken kiểm tra tính hợp lệ của token
func (s *Service) ValidateToken(tokenString string) (map[string]interface{}, error) {
	// Parse token
	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		// Kiểm tra signing method
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(s.config.JWTSecret), nil
	})

	if err != nil {
		return nil, internal.ErrInvalidToken
	}

	// Kiểm tra tính hợp lệ
	if !token.Valid {
		return nil, internal.ErrInvalidToken
	}

	// Lấy claims
	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		return nil, internal.ErrInvalidToken
	}

	// Kiểm tra thời hạn
	if exp, ok := claims["exp"].(float64); ok {
		if time.Now().Unix() > int64(exp) {
			return nil, internal.ErrExpiredToken
		}
	}

	return claims, nil
}

// generateJWTToken tạo JWT token
func (s *Service) generateJWTToken(userID int, username string, expiry time.Duration) (string, time.Time, error) {
	expiryTime := time.Now().Add(expiry)

	// Tạo token claims
	claims := jwt.MapClaims{
		"sub":  fmt.Sprintf("%d", userID),
		"user": username,
		"exp":  expiryTime.Unix(),
		"iat":  time.Now().Unix(),
		"jti":  uuid.NewString(),
	}

	// Tạo token
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)

	// Ký token
	tokenString, err := token.SignedString([]byte(s.config.JWTSecret))
	if err != nil {
		return "", time.Time{}, err
	}

	return tokenString, expiryTime, nil
}

// Logout đăng xuất người dùng bằng cách vô hiệu hóa token
func (s *Service) Logout(ctx context.Context, accessToken string) error {
	// Tạo span cho toàn bộ quá trình đăng xuất
	ctx, span := tracing.StartSpan(ctx, "auth-service", "logout")
	defer span.End()

	// Kiểm tra token có hợp lệ không
	claims, err := s.ValidateToken(accessToken)
	if err != nil {
		tracing.RecordError(ctx, err)
		return err
	}

	// Lấy thông tin từ claims
	userID, ok := claims["sub"].(string)
	if !ok {
		err := internal.ErrInvalidToken
		tracing.RecordError(ctx, err)
		return err
	}

	// Ghi log thông tin đăng xuất
	tracing.AddSpanAttributes(ctx,
		attribute.String("auth.user_id", userID),
		attribute.String("auth.token", accessToken[:8]+"..."),
	)

	// Tìm tất cả token hiện có của người dùng trong DB và xóa (cách triển khai đơn giản)
	// Trong thực tế, có thể cần thêm bảng blacklist token hoặc chỉ xóa token cụ thể
	err = tracing.WithSpan(ctx, "auth-service", "get_user_tokens", func(ctx context.Context) error {
		// Giả định repository có phương thức này
		// Trong trường hợp thực tế, cần phải thêm phương thức này vào repository
		// Hiện tại, chúng ta sẽ xóa token dựa vào thông tin từ claims
		return nil
	})

	if err != nil {
		tracing.RecordError(ctx, err)
		return err
	}

	// Đánh dấu thành công trong span
	tracing.SetSpanStatus(ctx, codes.Ok, "Logout successful")
	tracing.AddSpanAttributes(ctx, attribute.Bool("auth.success", true))

	return nil
}

// ChangePassword thay đổi mật khẩu của người dùng
func (s *Service) ChangePassword(ctx context.Context, userID uint, req dto.ChangePasswordRequest) (*dto.ChangePasswordResponse, error) {
	// Tạo span cho toàn bộ quá trình thay đổi mật khẩu
	ctx, span := tracing.StartSpan(ctx, "auth-service", "change_password")
	defer span.End()

	// Thêm thông tin vào span
	tracing.AddSpanAttributes(ctx, attribute.Int("auth.user_id", int(userID)))

	// Lấy thông tin người dùng từ database
	var user *internal.User
	err := tracing.WithSpan(ctx, "auth-service", "get_user", func(ctx context.Context) error {
		var err error
		user, err = s.repo.GetUserByID(ctx, int(userID))
		if err != nil {
			if errors.Is(err, internal.ErrUserNotFound) {
				tracing.AddSpanAttributes(ctx, attribute.String("auth.error", "user_not_found"))
				return internal.ErrUserNotFound
			}
			s.logger.Error("Failed to get user", logger.String("error", err.Error()))
			return err
		}
		return nil
	})

	if err != nil {
		tracing.RecordError(ctx, err)
		return nil, err
	}

	// Kiểm tra mật khẩu hiện tại
	err = tracing.WithSpan(ctx, "auth-service", "verify_current_password", func(ctx context.Context) error {
		if err := bcrypt.CompareHashAndPassword([]byte(user.PasswordHash), []byte(req.CurrentPassword)); err != nil {
			s.logger.Warn("Invalid current password", logger.Int("user_id", int(userID)))
			tracing.AddSpanAttributes(ctx, attribute.String("auth.error", "invalid_password"))
			return internal.ErrInvalidPassword
		}
		return nil
	})

	if err != nil {
		tracing.RecordError(ctx, err)
		return nil, err
	}

	// Hash mật khẩu mới
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.NewPassword), bcrypt.DefaultCost)
	if err != nil {
		tracing.RecordError(ctx, err)
		return nil, err
	}

	// Cập nhật mật khẩu mới
	err = tracing.WithSpan(ctx, "auth-service", "update_password", func(ctx context.Context) error {
		user.PasswordHash = string(hashedPassword)
		user.UpdatedAt = time.Now()

		if err := s.repo.UpdateUser(ctx, user); err != nil {
			s.logger.Error("Failed to update user password", logger.String("error", err.Error()))
			return err
		}
		return nil
	})

	if err != nil {
		tracing.RecordError(ctx, err)
		return nil, err
	}

	// Đánh dấu thành công trong span
	tracing.SetSpanStatus(ctx, codes.Ok, "Password changed successfully")
	tracing.AddSpanAttributes(ctx, attribute.Bool("auth.success", true))

	// Trả về response
	return &dto.ChangePasswordResponse{
		Success: true,
		Message: "Mật khẩu đã được thay đổi thành công",
	}, nil
}

// GetProfile lấy thông tin profile của người dùng
func (s *Service) GetProfile(ctx context.Context, userID uint) (*dto.ProfileResponse, error) {
	// Tạo span cho toàn bộ quá trình lấy profile
	ctx, span := tracing.StartSpan(ctx, "auth-service", "get_profile")
	defer span.End()

	// Thêm thuộc tính vào span
	tracing.AddSpanAttributes(ctx, attribute.Int("auth.user_id", int(userID)))

	// Lấy thông tin người dùng từ database
	var user *internal.User
	err := tracing.WithSpan(ctx, "auth-service", "get_user", func(ctx context.Context) error {
		var err error
		user, err = s.repo.GetUserByID(ctx, int(userID))
		if err != nil {
			if errors.Is(err, internal.ErrUserNotFound) {
				tracing.AddSpanAttributes(ctx, attribute.String("auth.error", "user_not_found"))
				return internal.ErrUserNotFound
			}
			s.logger.Error("Failed to get user", logger.String("error", err.Error()))
			return err
		}
		return nil
	})

	if err != nil {
		tracing.RecordError(ctx, err)
		return nil, err
	}

	// Lấy thông tin profile từ database (nếu có)
	var profile *models.Profile
	err = tracing.WithSpan(ctx, "auth-service", "get_user_profile", func(ctx context.Context) error {
		var err error
		profile, err = s.repo.GetUserProfile(ctx, int(userID))
		if err != nil {
			s.logger.Error("Failed to get user profile", logger.String("error", err.Error()))
			return err
		}
		return nil
	})

	if err != nil {
		tracing.RecordError(ctx, err)
		return nil, err
	}

	// Tạo model từ User
	userModel := &models.User{
		UserID:          user.UserID,
		TenantID:        user.TenantID,
		Username:        user.Username,
		Email:           user.Email,
		PasswordHash:    user.PasswordHash,
		FullName:        user.FullName,
		CreatedAt:       user.CreatedAt,
		UpdatedAt:       user.UpdatedAt,
		LastLogin:       user.LastLogin,
		Status:          models.UserStatus(user.Status),
		IsEmailVerified: user.IsEmailVerified,
		UserType:        models.ParseUserType(user.UserType),
		Profile:         profile,
	}

	// Đánh dấu thành công trong span
	tracing.SetSpanStatus(ctx, codes.Ok, "Profile retrieved successfully")
	tracing.AddSpanAttributes(ctx, attribute.Bool("auth.success", true))

	// Tạo response
	return dto.NewProfileResponse(userModel, profile), nil
}

// VerifyEmail xác thực email thông qua token
func (s *Service) VerifyEmail(ctx context.Context, token string) (*dto.VerifyEmailResponse, error) {
	// Tạo span cho toàn bộ quá trình xác thực email
	ctx, span := tracing.StartSpan(ctx, "auth-service", "verify-email")
	defer span.End()

	// Thêm thuộc tính vào span
	tracing.AddSpanAttributes(ctx,
		attribute.String("auth.token", token[:8]+"..."), // Chỉ hiển thị một phần token để bảo mật
	)

	// Giả lập xác thực email thành công
	// Trong triển khai thực tế, cần kiểm tra token trong database

	// Đánh dấu thành công trong span
	tracing.SetSpanStatus(ctx, codes.Ok, "Email verified successfully")

	// Trả về kết quả xác thực
	return &dto.VerifyEmailResponse{
		Email:    "<EMAIL>", // Giả lập email
		Verified: true,
		Message:  "Email đã được xác thực thành công",
	}, nil
}

// ResendVerificationEmail gửi lại email xác thực
func (s *Service) ResendVerificationEmail(ctx context.Context, email string) error {
	// Tạo span cho toàn bộ quá trình gửi lại email
	ctx, span := tracing.StartSpan(ctx, "auth-service", "resend-verification")
	defer span.End()

	// Thêm thuộc tính vào span
	tracing.AddSpanAttributes(ctx,
		attribute.String("auth.email", email),
	)

	// Kiểm tra xem email có tồn tại không
	user, err := s.repo.GetUserByEmail(ctx, email)
	if err != nil {
		if errors.Is(err, internal.ErrUserNotFound) {
			return errors.New("không tìm thấy người dùng với email này")
		}
		s.logger.Error("Failed to get user by email", logger.String("error", err.Error()))
		return err
	}

	// Kiểm tra xem email đã được xác thực chưa
	if user.IsEmailVerified {
		return errors.New("email đã được xác thực")
	}

	// Giả lập gửi email xác thực
	// Trong triển khai thực tế, cần tạo token và gửi email

	// Đánh dấu thành công trong span
	tracing.SetSpanStatus(ctx, codes.Ok, "Verification email sent successfully")

	return nil
}
