package mysql

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"wnapi/modules/auth/models"
	"wnapi/modules/auth/repository"
)

type passwordResetRepository struct {
	db *sql.DB
}

// NewPasswordResetRepository tạo một instance mới của passwordResetRepository
func NewPasswordResetRepository(db *sql.DB) repository.PasswordResetRepository {
	return &passwordResetRepository{
		db: db,
	}
}

// Create tạo một bản ghi đặt lại mật khẩu mới
func (r *passwordResetRepository) Create(ctx context.Context, email string, token string, expiresAt time.Time) error {
	query := `
		INSERT INTO auth_password_resets (email, token, expires_at)
		VALUES (?, ?, ?)
	`
	_, err := r.db.ExecContext(ctx, query, email, token, expiresAt)
	if err != nil {
		return fmt.Errorf("failed to create password reset: %w", err)
	}
	return nil
}

// FindByToken tìm bản ghi đặt lại mật khẩu theo token
func (r *passwordResetRepository) FindByToken(ctx context.Context, token string) (*models.PasswordReset, error) {
	query := `
		SELECT id, email, token, used, expires_at, created_at, updated_at 
		FROM auth_password_resets 
		WHERE token = ? AND used = false AND expires_at > ?
	`
	var reset models.PasswordReset
	err := r.db.QueryRowContext(ctx, query, token, time.Now()).Scan(
		&reset.ID,
		&reset.Email,
		&reset.Token,
		&reset.Used,
		&reset.ExpiresAt,
		&reset.CreatedAt,
		&reset.UpdatedAt,
	)

	if err == sql.ErrNoRows {
		return nil, nil
	}
	if err != nil {
		return nil, fmt.Errorf("failed to find password reset token: %w", err)
	}
	return &reset, nil
}

// MarkAsUsed đánh dấu token đã được sử dụng
func (r *passwordResetRepository) MarkAsUsed(ctx context.Context, token string) error {
	query := `UPDATE auth_password_resets SET used = true WHERE token = ?`
	_, err := r.db.ExecContext(ctx, query, token)
	if err != nil {
		return fmt.Errorf("failed to mark token as used: %w", err)
	}
	return nil
}

// InvalidateOldTokens vô hiệu hóa tất cả token cũ cho một email
func (r *passwordResetRepository) InvalidateOldTokens(ctx context.Context, email string) error {
	query := `UPDATE auth_password_resets SET used = true WHERE email = ? AND used = false`
	_, err := r.db.ExecContext(ctx, query, email)
	if err != nil {
		return fmt.Errorf("failed to invalidate old tokens: %w", err)
	}
	return nil
}

// DeleteExpiredTokens xóa các token đã hết hạn
func (r *passwordResetRepository) DeleteExpiredTokens(ctx context.Context) error {
	query := `
		DELETE FROM auth_password_resets 
		WHERE expires_at < ? OR (used = true AND created_at < DATE_SUB(?, INTERVAL 1 DAY))
	`
	now := time.Now()
	_, err := r.db.ExecContext(ctx, query, now, now)
	if err != nil {
		return fmt.Errorf("failed to delete expired tokens: %w", err)
	}
	return nil
}
