package mysql

import (
	"context"
	"fmt"
	"time"

	"wnapi/modules/auth/models"
	"wnapi/modules/auth/repository"

	"github.com/jmoiron/sqlx"
)

// SQLxEmailVerificationRepository là cài đặt MySQL cho EmailVerificationRepository
type SQLxEmailVerificationRepository struct {
	db *sqlx.DB
}

// NewEmailVerificationRepository tạo instance mới của SQLxEmailVerificationRepository
func NewEmailVerificationRepository(db *sqlx.DB) repository.EmailVerificationRepository {
	return &SQLxEmailVerificationRepository{db: db}
}

// Create lưu token xác thực email vào database
func (r *SQLxEmailVerificationRepository) Create(ctx context.Context, verification *models.EmailVerification) error {
	now := time.Now()
	verification.CreatedAt = now
	verification.UpdatedAt = now

	query := `
		INSERT INTO auth_email_verifications (
			user_id, email, token, verified, expires_at, created_at, updated_at
		) VALUES (
			:user_id, :email, :token, :verified, :expires_at, :created_at, :updated_at
		)
	`

	_, err := r.db.NamedExecContext(ctx, query, verification)
	if err != nil {
		return fmt.Errorf("failed to create email verification: %w", err)
	}

	return nil
}

// GetByToken lấy token xác thực email bằng token
func (r *SQLxEmailVerificationRepository) GetByToken(ctx context.Context, token string) (*models.EmailVerification, error) {
	query := `
		SELECT id, user_id, email, token, verified, expires_at, created_at, updated_at
		FROM auth_email_verifications
		WHERE token = ? AND verified = false
		LIMIT 1
	`

	var verification models.EmailVerification
	err := r.db.GetContext(ctx, &verification, query, token)
	if err != nil {
		return nil, fmt.Errorf("failed to get email verification by token: %w", err)
	}

	return &verification, nil
}

// MarkAsVerified đánh dấu token đã được sử dụng để xác thực
func (r *SQLxEmailVerificationRepository) MarkAsVerified(ctx context.Context, token string) error {
	query := `
		UPDATE auth_email_verifications
		SET verified = true, updated_at = ?
		WHERE token = ?
	`

	_, err := r.db.ExecContext(ctx, query, time.Now(), token)
	if err != nil {
		return fmt.Errorf("failed to mark email verification as verified: %w", err)
	}

	return nil
}

// GetByUserID lấy tất cả các token xác thực email của một người dùng
func (r *SQLxEmailVerificationRepository) GetByUserID(ctx context.Context, userID int64) ([]*models.EmailVerification, error) {
	query := `
		SELECT id, user_id, email, token, verified, expires_at, created_at, updated_at
		FROM auth_email_verifications
		WHERE user_id = ?
		ORDER BY created_at DESC
	`

	var verifications []*models.EmailVerification
	err := r.db.SelectContext(ctx, &verifications, query, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get email verifications by user ID: %w", err)
	}

	return verifications, nil
}

// DeleteExpired xóa tất cả các token đã hết hạn
func (r *SQLxEmailVerificationRepository) DeleteExpired(ctx context.Context) error {
	query := `
		DELETE FROM auth_email_verifications
		WHERE expires_at < ? AND verified = false
	`

	_, err := r.db.ExecContext(ctx, query, time.Now())
	if err != nil {
		return fmt.Errorf("failed to delete expired email verifications: %w", err)
	}

	return nil
}
