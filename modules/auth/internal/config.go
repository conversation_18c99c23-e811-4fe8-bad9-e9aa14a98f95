package internal

import (
	"fmt"
	"log"
	"os"

	"github.com/caarlos0/env/v11"
	"github.com/joho/godotenv"
)

// LoadAuthConfig đọc cấu hình auth từ biến môi trường
func LoadAuthConfig() (*AuthConfig, error) {
	// Tải file .env nếu có
	if _, err := os.Stat(".env"); err == nil {
		if err := godotenv.Load(); err != nil {
			log.Printf("Cảnh báo: không thể tải file .env: %v", err)
		}
	}

	// Khởi tạo config mặc định
	cfg := &AuthConfig{
		JWTSecret:          "default_jwt_secret_change_me_in_production",
		AccessTokenExpiry:  15 * 60 * 1000000000,       // 15 phút theo nanosecond
		RefreshTokenExpiry: 168 * 60 * 60 * 1000000000, // 168 giờ theo nanosecond
		Message:            "Xin chào từ module Auth!",
	}

	// Đ<PERSON><PERSON> cấu hình từ biến môi trường với prefix AUTH_
	opts := env.Options{
		Prefix: "AUTH_",
	}
	if err := env.ParseWithOptions(cfg, opts); err != nil {
		return nil, fmt.Errorf("lỗi đọc cấu hình auth từ biến môi trường: %w", err)
	}

	return cfg, nil
}
