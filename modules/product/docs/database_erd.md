# Sơ đồ Quan hệ Thực thể (ERD) Module Ecom

Tài liệu này mô tả cấu trúc cơ sở dữ liệu của module Ecom dựa trên các file migration.

## Tổng quan

Module Ecom quản lý các thực thể sau:
- <PERSON><PERSON> mục sản phẩm (Categories)
- <PERSON><PERSON><PERSON> ph<PERSON> (Products)
- <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> sản phẩm (Product Attributes)
- <PERSON><PERSON><PERSON><PERSON> thu<PERSON><PERSON> t<PERSON>h sản phẩm (Product Attribute Groups)
- <PERSON><PERSON><PERSON> chọ<PERSON> thuộc tính sản phẩm (Product Attribute Options)
- <PERSON><PERSON><PERSON><PERSON> thể sản phẩm (Product Variants)
- <PERSON><PERSON><PERSON> tr<PERSON> thuộc tính biến thể (Product Variant Attribute Values)
- Nhóm tùy chọn sản phẩm (Product Option Groups)
- G<PERSON><PERSON> trị tùy chọn sản phẩm (Product Option Values)
- <PERSON><PERSON><PERSON> kết tùy chọn sản phẩm (Product Options Link)

## M<PERSON> tả các bảng

### 1. <PERSON><PERSON> mục sản phẩm (ecom_product_categories)

<PERSON><PERSON><PERSON> trữ cấu trúc phân cấp danh mục sản phẩm sử dụng mô hình Nested Set.

| Trường | Kiểu dữ liệu | Mô tả |
|--------|--------------|-------|
| category_id | INT UNSIGNED | Khóa chính |
| tenant_id | INT UNSIGNED | ID của tenant |
| parent_id | INT UNSIGNED | ID của danh mục cha (NULL nếu là danh mục gốc) |
| name | VARCHAR(255) | Tên danh mục |
| slug | VARCHAR(255) | Slug URL của danh mục |
| description | TEXT | Mô tả danh mục |
| featured_image | VARCHAR(255) | URL hình ảnh đại diện |
| lft | INT | Giá trị trái trong mô hình Nested Set |
| rgt | INT | Giá trị phải trong mô hình Nested Set |
| depth | INT | Độ sâu trong cây danh mục |
| position | INT | Vị trí trong cùng cấp |
| is_active | BOOLEAN | Trạng thái hoạt động |
| is_featured | BOOLEAN | Danh mục nổi bật |

### 2. Nhóm thuộc tính sản phẩm (ecom_product_attribute_groups)

Nhóm các thuộc tính sản phẩm có liên quan với nhau.

| Trường | Kiểu dữ liệu | Mô tả |
|--------|--------------|-------|
| group_id | INT UNSIGNED | Khóa chính |
| tenant_id | INT UNSIGNED | ID của tenant |
| name | VARCHAR(255) | Tên nhóm thuộc tính |
| code | VARCHAR(100) | Mã định danh của nhóm |
| display_order | INT UNSIGNED | Thứ tự hiển thị |

### 3. Thuộc tính sản phẩm (ecom_product_attributes)

Định nghĩa các thuộc tính có thể áp dụng cho sản phẩm.

| Trường | Kiểu dữ liệu | Mô tả |
|--------|--------------|-------|
| attribute_id | INT UNSIGNED | Khóa chính |
| tenant_id | INT UNSIGNED | ID của tenant |
| group_id | INT UNSIGNED | ID của nhóm thuộc tính |
| name | VARCHAR(255) | Tên thuộc tính |
| code | VARCHAR(100) | Mã định danh của thuộc tính |
| type | ENUM | Kiểu dữ liệu của thuộc tính |
| unit | VARCHAR(50) | Đơn vị đo lường |
| validation_rules | TEXT | Quy tắc kiểm tra dữ liệu |
| is_configurable | BOOLEAN | Dùng để tạo biến thể sản phẩm |
| is_filterable | BOOLEAN | Dùng để lọc sản phẩm |
| is_searchable | BOOLEAN | Dùng để tìm kiếm |
| is_comparable | BOOLEAN | Dùng để so sánh sản phẩm |
| is_required | BOOLEAN | Bắt buộc nhập |
| frontend_input | VARCHAR(50) | Loại input control |
| display_order | INT UNSIGNED | Thứ tự hiển thị |

### 4. Tùy chọn thuộc tính sản phẩm (ecom_product_attribute_options)

Các giá trị có thể chọn cho thuộc tính kiểu SELECT/MULTISELECT.

| Trường | Kiểu dữ liệu | Mô tả |
|--------|--------------|-------|
| option_id | INT UNSIGNED | Khóa chính |
| tenant_id | INT UNSIGNED | ID của tenant |
| attribute_id | INT UNSIGNED | ID của thuộc tính |
| value | VARCHAR(255) | Giá trị của tùy chọn |
| label | VARCHAR(255) | Nhãn hiển thị |
| swatch_type | ENUM | Loại swatch (COLOR, IMAGE, TEXT) |
| swatch_value | VARCHAR(255) | Giá trị swatch |
| display_order | INT UNSIGNED | Thứ tự hiển thị |

### 5. Sản phẩm (ecom_products)

Thông tin cơ bản về sản phẩm.

| Trường | Kiểu dữ liệu | Mô tả |
|--------|--------------|-------|
| product_id | INT UNSIGNED | Khóa chính |
| tenant_id | INT UNSIGNED | ID của tenant |
| category_id | INT UNSIGNED | ID của danh mục chính |
| name | VARCHAR(255) | Tên sản phẩm |
| description | TEXT | Mô tả ngắn |
| content | LONGTEXT | Nội dung chi tiết |
| slug | VARCHAR(255) | Slug URL |
| image_url | VARCHAR(255) | URL hình ảnh chính |
| base_price | DECIMAL(18,2) | Giá cơ bản |
| cost_price | DECIMAL(18,2) | Giá vốn |
| product_type | ENUM | Loại sản phẩm |
| status | ENUM | Trạng thái xuất bản |
| product_code | VARCHAR(255) | Mã sản phẩm nội bộ |
| is_taxable | BOOLEAN | Chịu thuế hay không |
| is_virtual | BOOLEAN | Sản phẩm ảo |
| is_downloadable | BOOLEAN | Sản phẩm có thể tải xuống |

### 6. Thuộc tính cấu hình sản phẩm (ecom_product_configurable_attributes)

Xác định thuộc tính nào được sử dụng để tạo biến thể cho sản phẩm cấu hình.

| Trường | Kiểu dữ liệu | Mô tả |
|--------|--------------|-------|
| configurable_attribute_id | INT UNSIGNED | Khóa chính |
| tenant_id | INT UNSIGNED | ID của tenant |
| product_id | INT UNSIGNED | ID của sản phẩm |
| attribute_id | INT UNSIGNED | ID của thuộc tính |
| position | INT UNSIGNED | Thứ tự hiển thị |

### 7. Biến thể sản phẩm (ecom_product_variants)

Các biến thể của sản phẩm cấu hình.

| Trường | Kiểu dữ liệu | Mô tả |
|--------|--------------|-------|
| variant_id | INT UNSIGNED | Khóa chính |
| tenant_id | INT UNSIGNED | ID của tenant |
| product_id | INT UNSIGNED | ID của sản phẩm chính |
| sku | VARCHAR(100) | Mã SKU |
| price | DECIMAL(18,2) | Giá biến thể |
| cost_price | DECIMAL(18,2) | Giá vốn biến thể |
| image_url | VARCHAR(255) | URL hình ảnh biến thể |
| is_active | TINYINT(1) | Trạng thái hoạt động |

### 8. Giá trị thuộc tính biến thể (ecom_product_variant_attribute_values)

Giá trị thuộc tính cho từng biến thể sản phẩm.

| Trường | Kiểu dữ liệu | Mô tả |
|--------|--------------|-------|
| value_id | INT UNSIGNED | Khóa chính |
| tenant_id | INT UNSIGNED | ID của tenant |
| variant_id | INT UNSIGNED | ID của biến thể |
| attribute_id | INT UNSIGNED | ID của thuộc tính |
| attribute_option_id | INT UNSIGNED | ID của tùy chọn thuộc tính |

### 9. Nhóm tùy chọn sản phẩm (ecom_product_option_groups)

Nhóm các tùy chọn bổ sung cho sản phẩm (ví dụ: topping, độ cay).

| Trường | Kiểu dữ liệu | Mô tả |
|--------|--------------|-------|
| group_id | INT UNSIGNED | Khóa chính |
| tenant_id | INT UNSIGNED | ID của tenant |
| name | VARCHAR(255) | Tên nhóm tùy chọn |
| description | TEXT | Mô tả nhóm |
| type | ENUM | Kiểu chọn (RADIO, CHECKBOX, SELECT, ...) |
| required | BOOLEAN | Bắt buộc chọn |
| min_select | INT UNSIGNED | Số lượng tối thiểu phải chọn |
| max_select | INT UNSIGNED | Số lượng tối đa được chọn |
| display_order | INT UNSIGNED | Thứ tự hiển thị |

### 10. Giá trị tùy chọn sản phẩm (ecom_product_option_values)

Các giá trị cụ thể trong nhóm tùy chọn.

| Trường | Kiểu dữ liệu | Mô tả |
|--------|--------------|-------|
| value_id | INT UNSIGNED | Khóa chính |
| tenant_id | INT UNSIGNED | ID của tenant |
| group_id | INT UNSIGNED | ID của nhóm tùy chọn |
| linked_variant_id | INT UNSIGNED | ID biến thể liên kết (nếu có) |
| name | VARCHAR(255) | Tên giá trị tùy chọn |
| description | TEXT | Mô tả chi tiết |
| image_url | VARCHAR(255) | URL hình ảnh minh họa |
| price_adjustment | DECIMAL(18,2) | Điều chỉnh giá |
| price_adjustment_type | ENUM | Loại điều chỉnh (FIXED, PERCENTAGE) |
| is_default | BOOLEAN | Giá trị mặc định |
| display_order | INT UNSIGNED | Thứ tự hiển thị |

### 11. Liên kết tùy chọn sản phẩm (ecom_product_options_link)

Liên kết nhóm tùy chọn với sản phẩm.

| Trường | Kiểu dữ liệu | Mô tả |
|--------|--------------|-------|
| product_option_id | INT UNSIGNED | Khóa chính |
| tenant_id | INT UNSIGNED | ID của tenant |
| product_id | INT UNSIGNED | ID của sản phẩm |
| group_id | INT UNSIGNED | ID của nhóm tùy chọn |
| display_order | INT UNSIGNED | Thứ tự hiển thị |

## Mối quan hệ chính

1. **Sản phẩm - Danh mục**: Mỗi sản phẩm thuộc về một danh mục chính (1:N)
2. **Thuộc tính - Nhóm thuộc tính**: Mỗi thuộc tính có thể thuộc về một nhóm thuộc tính (1:N)
3. **Tùy chọn thuộc tính - Thuộc tính**: Mỗi tùy chọn thuộc về một thuộc tính (1:N)
4. **Sản phẩm - Thuộc tính cấu hình**: Mỗi sản phẩm cấu hình có nhiều thuộc tính cấu hình (1:N)
5. **Sản phẩm - Biến thể**: Mỗi sản phẩm cấu hình có nhiều biến thể (1:N)
6. **Biến thể - Giá trị thuộc tính**: Mỗi biến thể có nhiều giá trị thuộc tính (1:N)
7. **Sản phẩm - Nhóm tùy chọn**: Mối quan hệ nhiều-nhiều thông qua bảng liên kết (M:N)
8. **Nhóm tùy chọn - Giá trị tùy chọn**: Mỗi nhóm tùy chọn có nhiều giá trị tùy chọn (1:N)
