# Product Attribute Groups

Product attribute groups are used to organize product attributes into logical groups. For example, you might have a "Dimensions" group that contains attributes like "Width", "Height", and "Depth", or a "Technical Specifications" group that contains attributes like "Processor", "RAM", and "Storage".

## Database Schema

The `ecom_product_attribute_groups` table has the following structure:

```sql
CREATE TABLE IF NOT EXISTS ecom_product_attribute_groups (
  group_id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT 'ID duy nhất của nhóm thuộc tính',
  tenant_id INT UNSIGNED NOT NULL COMMENT 'ID của người thuê (tenant)',
  name VARCHAR(255) NOT NULL COMMENT 'Tên nhóm thuộc tính',
  code VARCHAR(100) NOT NULL COMMENT 'Mã nhóm thuộc tính (dùng để tham chiếu trong code)',
  display_order INT UNSIGNED NOT NULL DEFAULT 0 COMMENT 'Thứ tự hiển thị của nhóm thuộc tính',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Thời điểm tạo bản ghi',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Thời điểm cập nhật bản ghi gần nhất',
  UNIQUE KEY uk_attribute_group_code_tenant (tenant_id, code) COMMENT 'Mã nhóm thuộc tính là duy nhất trong tenant',
  INDEX idx_attribute_group_tenant (tenant_id) COMMENT 'Index theo tenant',
  INDEX idx_attribute_group_display_order (display_order) COMMENT 'Index theo thứ tự hiển thị'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Lưu trữ thông tin về các nhóm thuộc tính sản phẩm';
```

## API Endpoints

### List Product Attribute Groups

```
GET /api/admin/v1/ecom/product-attribute-groups
```

**Query Parameters:**
- `cursor` (optional): Cursor for pagination
- `limit` (optional): Number of items per page (default: 10)

**Response:**
```json
{
  "status": "success",
  "message": "Product attribute groups retrieved successfully",
  "data": [
    {
      "id": 1,
      "tenant_id": 1,
      "name": "Dimensions",
      "code": "dimensions",
      "display_order": 1,
      "created_at": "2023-01-01T00:00:00Z",
      "updated_at": "2023-01-01T00:00:00Z"
    },
    {
      "id": 2,
      "tenant_id": 1,
      "name": "Technical Specifications",
      "code": "tech_specs",
      "display_order": 2,
      "created_at": "2023-01-01T00:00:00Z",
      "updated_at": "2023-01-01T00:00:00Z"
    }
  ],
  "meta": {
    "next_cursor": "2",
    "has_more": true
  }
}
```

### Get All Product Attribute Groups

```
GET /api/admin/v1/ecom/product-attribute-groups/all
```

**Response:**
```json
{
  "status": "success",
  "message": "All product attribute groups retrieved successfully",
  "data": [
    {
      "id": 1,
      "tenant_id": 1,
      "name": "Dimensions",
      "code": "dimensions",
      "display_order": 1,
      "created_at": "2023-01-01T00:00:00Z",
      "updated_at": "2023-01-01T00:00:00Z"
    },
    {
      "id": 2,
      "tenant_id": 1,
      "name": "Technical Specifications",
      "code": "tech_specs",
      "display_order": 2,
      "created_at": "2023-01-01T00:00:00Z",
      "updated_at": "2023-01-01T00:00:00Z"
    }
  ]
}
```

### Get Product Attribute Group by ID

```
GET /api/admin/v1/ecom/product-attribute-groups/:id
```

**Response:**
```json
{
  "status": "success",
  "message": "Product attribute group retrieved successfully",
  "data": {
    "id": 1,
    "tenant_id": 1,
    "name": "Dimensions",
    "code": "dimensions",
    "display_order": 1,
    "created_at": "2023-01-01T00:00:00Z",
    "updated_at": "2023-01-01T00:00:00Z"
  }
}
```

### Get Product Attribute Group by Code

```
GET /api/admin/v1/ecom/product-attribute-groups/code/:code
```

**Response:**
```json
{
  "status": "success",
  "message": "Product attribute group retrieved successfully",
  "data": {
    "id": 1,
    "tenant_id": 1,
    "name": "Dimensions",
    "code": "dimensions",
    "display_order": 1,
    "created_at": "2023-01-01T00:00:00Z",
    "updated_at": "2023-01-01T00:00:00Z"
  }
}
```

### Create Product Attribute Group

```
POST /api/admin/v1/ecom/product-attribute-groups
```

**Request Body:**
```json
{
  "name": "Dimensions",
  "code": "dimensions",
  "display_order": 1
}
```

**Response:**
```json
{
  "status": "success",
  "message": "Product attribute group created successfully",
  "data": {
    "id": 1,
    "tenant_id": 1,
    "name": "Dimensions",
    "code": "dimensions",
    "display_order": 1,
    "created_at": "2023-01-01T00:00:00Z",
    "updated_at": "2023-01-01T00:00:00Z"
  }
}
```

### Update Product Attribute Group

```
PUT /api/admin/v1/ecom/product-attribute-groups/:id
```

**Request Body:**
```json
{
  "name": "Product Dimensions",
  "code": "dimensions",
  "display_order": 2
}
```

**Response:**
```json
{
  "status": "success",
  "message": "Product attribute group updated successfully",
  "data": {
    "id": 1,
    "tenant_id": 1,
    "name": "Product Dimensions",
    "code": "dimensions",
    "display_order": 2,
    "created_at": "2023-01-01T00:00:00Z",
    "updated_at": "2023-01-02T00:00:00Z"
  }
}
```

### Delete Product Attribute Group

```
DELETE /api/admin/v1/ecom/product-attribute-groups/:id
```

**Response:**
```json
{
  "status": "success",
  "message": "Product attribute group deleted successfully",
  "data": null
}
```
