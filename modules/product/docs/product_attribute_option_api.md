# API Tùy chọn Thuộc tính Sản phẩm (Product Attribute Options)

API này cho phép quản lý các tùy chọn cho thuộc tính sản phẩm có kiểu SELECT hoặc MULTISELECT. Mỗi tùy chọn thuộc tính có thể có các giá trị cụ thể, ví dụ như các màu sắ<PERSON>, kích thước,...

## Cấu trúc dữ liệu

### Tùy chọn <PERSON> t<PERSON> (ProductAttributeOption)

```json
{
  "option_id": 1,
  "attribute_id": 5,
  "value": "Đỏ",
  "label": "Màu Đỏ",
  "swatch_type": "COLOR",
  "swatch_value": "#FF0000",
  "display_order": 1,
  "created_at": "2023-08-05T10:00:00Z",
  "updated_at": "2023-08-05T10:00:00Z"
}
```

## Endpoints

### 1. <PERSON><PERSON><PERSON> danh sách tùy chọn thuộc t<PERSON>

```
GET /api/admin/v1/ecom/product-attribute-options
```

L<PERSON>y danh sách tùy chọn thuộc tính với phân trang.

#### Tham số truy vấn

| Tham số | Kiểu | Mô tả |
|---------|-----|-------|
| attribute_id | integer | (Tùy chọn) ID thuộc tính để lọc tùy chọn |
| cursor | string | (Tùy chọn) Cursor cho phân trang |
| limit | integer | (Tùy chọn) Số lượng kết quả trả về, mặc định là 10 |

#### Phản hồi thành công (200)

```json
{
  "status": {
    "code": 200,
    "message": "Lấy danh sách tùy chọn thuộc tính thành công",
    "success": true,
    "error_code": null,
    "path": "/api/admin/v1/ecom/product-attribute-options",
    "timestamp": "2023-08-05T10:00:00Z",
    "details": null
  },
  "data": [
    {
      "option_id": 1,
      "attribute_id": 5,
      "value": "Đỏ",
      "label": "Màu Đỏ",
      "swatch_type": "COLOR",
      "swatch_value": "#FF0000",
      "display_order": 1,
      "created_at": "2023-08-05T10:00:00Z",
      "updated_at": "2023-08-05T10:00:00Z"
    },
    // ...
  ],
  "meta": {
    "next_cursor": "base64_encoded_cursor",
    "has_more": true
  }
}
```

### 2. Lấy tùy chọn thuộc tính theo ID

```
GET /api/admin/v1/ecom/product-attribute-options/:id
```

Lấy thông tin chi tiết của một tùy chọn thuộc tính dựa trên ID.

#### Tham số đường dẫn

| Tham số | Kiểu | Mô tả |
|---------|-----|-------|
| id | integer | ID của tùy chọn thuộc tính |

#### Phản hồi thành công (200)

```json
{
  "status": {
    "code": 200,
    "message": "Lấy thông tin tùy chọn thuộc tính thành công",
    "success": true,
    "error_code": null,
    "path": "/api/admin/v1/ecom/product-attribute-options/1",
    "timestamp": "2023-08-05T10:00:00Z",
    "details": null
  },
  "data": {
    "option_id": 1,
    "attribute_id": 5,
    "value": "Đỏ",
    "label": "Màu Đỏ",
    "swatch_type": "COLOR",
    "swatch_value": "#FF0000",
    "display_order": 1,
    "created_at": "2023-08-05T10:00:00Z",
    "updated_at": "2023-08-05T10:00:00Z"
  }
}
```

### 3. Tạo tùy chọn thuộc tính mới

```
POST /api/admin/v1/ecom/product-attribute-options
```

Tạo một tùy chọn thuộc tính mới.

#### Body Request

```json
{
  "attribute_id": 5,
  "value": "Đỏ",
  "label": "Màu Đỏ",
  "swatch_type": "COLOR",
  "swatch_value": "#FF0000",
  "display_order": 1
}
```

#### Phản hồi thành công (200)

```json
{
  "status": {
    "code": 200,
    "message": "Tạo tùy chọn thuộc tính thành công",
    "success": true,
    "error_code": null,
    "path": "/api/admin/v1/ecom/product-attribute-options",
    "timestamp": "2023-08-05T10:00:00Z",
    "details": null
  },
  "data": {
    "option_id": 1,
    "attribute_id": 5,
    "value": "Đỏ",
    "label": "Màu Đỏ",
    "swatch_type": "COLOR",
    "swatch_value": "#FF0000",
    "display_order": 1,
    "created_at": "2023-08-05T10:00:00Z",
    "updated_at": "2023-08-05T10:00:00Z"
  }
}
```

### 4. Cập nhật tùy chọn thuộc tính

```
PUT /api/admin/v1/ecom/product-attribute-options/:id
```

Cập nhật thông tin của một tùy chọn thuộc tính dựa trên ID.

#### Tham số đường dẫn

| Tham số | Kiểu | Mô tả |
|---------|-----|-------|
| id | integer | ID của tùy chọn thuộc tính |

#### Body Request

```json
{
  "attribute_id": 5,
  "value": "Đỏ Tươi",
  "label": "Màu Đỏ Tươi",
  "swatch_type": "COLOR",
  "swatch_value": "#FF3333",
  "display_order": 2
}
```

#### Phản hồi thành công (200)

```json
{
  "status": {
    "code": 200,
    "message": "Cập nhật tùy chọn thuộc tính thành công",
    "success": true,
    "error_code": null,
    "path": "/api/admin/v1/ecom/product-attribute-options/1",
    "timestamp": "2023-08-05T10:00:00Z",
    "details": null
  },
  "data": {
    "option_id": 1,
    "attribute_id": 5,
    "value": "Đỏ Tươi",
    "label": "Màu Đỏ Tươi",
    "swatch_type": "COLOR",
    "swatch_value": "#FF3333",
    "display_order": 2,
    "created_at": "2023-08-05T10:00:00Z",
    "updated_at": "2023-08-05T10:30:00Z"
  }
}
```

### 5. Xóa tùy chọn thuộc tính

```
DELETE /api/admin/v1/ecom/product-attribute-options/:id
```

Xóa một tùy chọn thuộc tính dựa trên ID.

#### Tham số đường dẫn

| Tham số | Kiểu | Mô tả |
|---------|-----|-------|
| id | integer | ID của tùy chọn thuộc tính |

#### Phản hồi thành công (200)

```json
{
  "status": {
    "code": 200,
    "message": "Xóa tùy chọn thuộc tính thành công",
    "success": true,
    "error_code": null,
    "path": "/api/v1/ecom/product-attribute-options/1",
    "timestamp": "2023-08-05T10:00:00Z",
    "details": null
  },
  "data": null
}
```

### 6. Lấy tất cả tùy chọn thuộc tính

```
GET /api/v1/ecom/product-attribute-options/all
```

Lấy tất cả tùy chọn thuộc tính không phân trang.

#### Phản hồi thành công (200)

```json
{
  "status": {
    "code": 200,
    "message": "Lấy tất cả tùy chọn thuộc tính thành công",
    "success": true,
    "error_code": null,
    "path": "/api/v1/ecom/product-attribute-options/all",
    "timestamp": "2023-08-05T10:00:00Z",
    "details": null
  },
  "data": [
    {
      "option_id": 1,
      "attribute_id": 5,
      "value": "Đỏ",
      "label": "Màu Đỏ",
      "swatch_type": "COLOR",
      "swatch_value": "#FF0000",
      "display_order": 1,
      "created_at": "2023-08-05T10:00:00Z",
      "updated_at": "2023-08-05T10:00:00Z"
    },
    // ...
  ]
}
```

### 7. Lấy tùy chọn thuộc tính theo thuộc tính

```
GET /api/v1/ecom/product-attribute-options/attribute/:attribute_id
```

Lấy tất cả tùy chọn của một thuộc tính cụ thể.

#### Tham số đường dẫn

| Tham số | Kiểu | Mô tả |
|---------|-----|-------|
| attribute_id | integer | ID của thuộc tính |

#### Phản hồi thành công (200)

```json
{
  "status": {
    "code": 200,
    "message": "Lấy tùy chọn thuộc tính theo thuộc tính thành công",
    "success": true,
    "error_code": null,
    "path": "/api/v1/ecom/product-attribute-options/attribute/5",
    "timestamp": "2023-08-05T10:00:00Z",
    "details": null
  },
  "data": [
    {
      "option_id": 1,
      "attribute_id": 5,
      "value": "Đỏ",
      "label": "Màu Đỏ",
      "swatch_type": "COLOR",
      "swatch_value": "#FF0000",
      "display_order": 1,
      "created_at": "2023-08-05T10:00:00Z",
      "updated_at": "2023-08-05T10:00:00Z"
    },
    // ...
  ]
}
```

## Mã lỗi

| Mã lỗi | Mô tả |
|--------|-------|
| 400 | Dữ liệu không hợp lệ |
| 401 | Chưa đăng nhập hoặc token hết hạn |
| 403 | Không có quyền truy cập |
| 404 | Không tìm thấy tùy chọn thuộc tính |
| 409 | Tùy chọn thuộc tính đã tồn tại |
| 500 | Lỗi server |