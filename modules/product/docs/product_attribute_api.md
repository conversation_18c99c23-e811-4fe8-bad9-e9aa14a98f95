# API Thuộc tính Sản phẩm

## Giới thiệu

API Thuộc tính Sản phẩm cung cấp các endpoint để quản lý thuộc tính sản phẩm trong hệ thống E-commerce. Thuộc tính sản phẩm là các đặc điểm để mô tả sản phẩm như kích thướ<PERSON>, m<PERSON><PERSON>, vật li<PERSON>, v.v.

## Endpoints

### 1. <PERSON><PERSON><PERSON> danh sách thuộc tính sản phẩm

**URL**: `/api/admin/v1/ecom/product-attributes`

**Phương thức**: `GET`

**X<PERSON><PERSON> thực**: Y<PERSON><PERSON> cầu

**Quyền**: `ecom.product_attributes.read`

**Tham số Query**:
- `cursor` (string, không bắt buộc): Cursor để phân trang
- `limit` (int, không bắt buộc, mặc định: 10): <PERSON><PERSON> lượng bản ghi trên mỗi trang

**Phản hồi thành công**:

```json
{
  "status": {
    "code": 200,
    "message": "Operation completed successfully",
    "success": true,
    "error_code": null,
    "path": "/api/admin/v1/ecom/product-attributes",
    "timestamp": "2023-05-25T10:30:00Z",
    "details": null
  },
  "data": [
    {
      "attribute_id": 1,
      "group_id": 1,
      "name": "Màu sắc",
      "code": "color",
      "type": "SELECT",
      "unit": null,
      "validation_rules": null,
      "is_configurable": true,
      "is_filterable": true,
      "is_searchable": false,
      "is_comparable": true,
      "is_required": true,
      "frontend_input": "swatch",
      "display_order": 0,
      "created_at": "2023-05-25T10:00:00Z",
      "updated_at": "2023-05-25T10:00:00Z"
    }
  ],
  "meta": {
    "next_cursor": "eyJsYXN0X2lkIjoyfQ==",
    "has_more": true
  }
}
```

### 2. Lấy thuộc tính sản phẩm theo ID

**URL**: `/api/admin/v1/ecom/product-attributes/:id`

**Phương thức**: `GET`

**Xác thực**: Yêu cầu

**Quyền**: `ecom.product_attributes.read`

**Phản hồi thành công**:

```json
{
  "status": {
    "code": 200,
    "message": "Operation completed successfully",
    "success": true,
    "error_code": null,
    "path": "/api/admin/v1/ecom/product-attributes/1",
    "timestamp": "2023-05-25T10:30:00Z",
    "details": null
  },
  "data": {
    "attribute_id": 1,
    "group_id": 1,
    "name": "Màu sắc",
    "code": "color",
    "type": "SELECT",
    "unit": null,
    "validation_rules": null,
    "is_configurable": true,
    "is_filterable": true,
    "is_searchable": false,
    "is_comparable": true,
    "is_required": true,
    "frontend_input": "swatch",
    "display_order": 0,
    "created_at": "2023-05-25T10:00:00Z",
    "updated_at": "2023-05-25T10:00:00Z"
  }
}
```

### 3. Lấy thuộc tính sản phẩm theo mã

**URL**: `/api/admin/v1/ecom/product-attributes/code/:code`

**Phương thức**: `GET`

**Xác thực**: Yêu cầu

**Quyền**: `ecom.product_attributes.read`

**Phản hồi thành công**:

```json
{
  "status": {
    "code": 200,
    "message": "Operation completed successfully",
    "success": true,
    "error_code": null,
    "path": "/api/admin/v1/ecom/product-attributes/code/color",
    "timestamp": "2023-05-25T10:30:00Z",
    "details": null
  },
  "data": {
    "attribute_id": 1,
    "group_id": 1,
    "name": "Màu sắc",
    "code": "color",
    "type": "SELECT",
    "unit": null,
    "validation_rules": null,
    "is_configurable": true,
    "is_filterable": true,
    "is_searchable": false,
    "is_comparable": true,
    "is_required": true,
    "frontend_input": "swatch",
    "display_order": 0,
    "created_at": "2023-05-25T10:00:00Z",
    "updated_at": "2023-05-25T10:00:00Z"
  }
}
```

### 4. Tạo thuộc tính sản phẩm mới

**URL**: `/api/admin/v1/ecom/product-attributes`

**Phương thức**: `POST`

**Xác thực**: Yêu cầu

**Quyền**: `ecom.product_attributes.create`

**Body Request**:

```json
{
  "group_id": 1,
  "name": "Kích thước",
  "code": "size",
  "type": "SELECT",
  "unit": "cm",
  "validation_rules": null,
  "is_configurable": true,
  "is_filterable": true,
  "is_searchable": false,
  "is_comparable": true,
  "is_required": true,
  "frontend_input": "select",
  "display_order": 1
}
```

**Phản hồi thành công**:

```json
{
  "status": {
    "code": 200,
    "message": "Operation completed successfully",
    "success": true,
    "error_code": null,
    "path": "/api/admin/v1/ecom/product-attributes",
    "timestamp": "2023-05-25T10:30:00Z",
    "details": null
  },
  "data": {
    "attribute_id": 2,
    "group_id": 1,
    "name": "Kích thước",
    "code": "size",
    "type": "SELECT",
    "unit": "cm",
    "validation_rules": null,
    "is_configurable": true,
    "is_filterable": true,
    "is_searchable": false,
    "is_comparable": true,
    "is_required": true,
    "frontend_input": "select",
    "display_order": 1,
    "created_at": "2023-05-25T10:30:00Z",
    "updated_at": "2023-05-25T10:30:00Z"
  }
}
```

### 5. Cập nhật thuộc tính sản phẩm

**URL**: `/api/admin/v1/ecom/product-attributes/:id`

**Phương thức**: `PUT`

**Xác thực**: Yêu cầu

**Quyền**: `ecom.product_attributes.update`

**Body Request**:

```json
{
  "group_id": 1,
  "name": "Kích thước",
  "code": "size",
  "type": "SELECT",
  "unit": "cm",
  "validation_rules": null,
  "is_configurable": true,
  "is_filterable": true,
  "is_searchable": true,
  "is_comparable": true,
  "is_required": true,
  "frontend_input": "select",
  "display_order": 1
}
```

**Phản hồi thành công**:

```json
{
  "status": {
    "code": 200,
    "message": "Operation completed successfully",
    "success": true,
    "error_code": null,
    "path": "/api/admin/v1/ecom/product-attributes/2",
    "timestamp": "2023-05-25T10:35:00Z",
    "details": null
  },
  "data": {
    "attribute_id": 2,
    "group_id": 1,
    "name": "Kích thước",
    "code": "size",
    "type": "SELECT",
    "unit": "cm",
    "validation_rules": null,
    "is_configurable": true,
    "is_filterable": true,
    "is_searchable": true,
    "is_comparable": true,
    "is_required": true,
    "frontend_input": "select",
    "display_order": 1,
    "created_at": "2023-05-25T10:30:00Z",
    "updated_at": "2023-05-25T10:35:00Z"
  }
}
```

### 6. Xóa thuộc tính sản phẩm

**URL**: `/api/admin/v1/ecom/product-attributes/:id`

**Phương thức**: `DELETE`

**Xác thực**: Yêu cầu

**Quyền**: `ecom.product_attributes.delete`

**Phản hồi thành công**:

```json
{
  "status": {
    "code": 200,
    "message": "Operation completed successfully",
    "success": true,
    "error_code": null,
    "path": "/api/admin/v1/ecom/product-attributes/2",
    "timestamp": "2023-05-25T10:40:00Z",
    "details": null
  },
  "data": null
}
```

### 7. Lấy tất cả thuộc tính sản phẩm

**URL**: `/api/admin/v1/ecom/product-attributes/all`

**Phương thức**: `GET`

**Xác thực**: Yêu cầu

**Quyền**: `ecom.product_attributes.read`

**Phản hồi thành công**:

```json
{
  "status": {
    "code": 200,
    "message": "Operation completed successfully",
    "success": true,
    "error_code": null,
    "path": "/api/admin/v1/ecom/product-attributes/all",
    "timestamp": "2023-05-25T10:45:00Z",
    "details": null
  },
  "data": [
    {
      "attribute_id": 1,
      "group_id": 1,
      "name": "Màu sắc",
      "code": "color",
      "type": "SELECT",
      "unit": null,
      "validation_rules": null,
      "is_configurable": true,
      "is_filterable": true,
      "is_searchable": false,
      "is_comparable": true,
      "is_required": true,
      "frontend_input": "swatch",
      "display_order": 0,
      "created_at": "2023-05-25T10:00:00Z",
      "updated_at": "2023-05-25T10:00:00Z"
    },
    {
      "attribute_id": 3,
      "group_id": 1,
      "name": "Vật liệu",
      "code": "material",
      "type": "SELECT",
      "unit": null,
      "validation_rules": null,
      "is_configurable": false,
      "is_filterable": true,
      "is_searchable": true,
      "is_comparable": true,
      "is_required": false,
      "frontend_input": "select",
      "display_order": 2,
      "created_at": "2023-05-25T10:50:00Z",
      "updated_at": "2023-05-25T10:50:00Z"
    }
  ]
}
```

### 8. Lấy thuộc tính sản phẩm theo nhóm

**URL**: `/api/admin/v1/ecom/product-attributes/group/:group_id`

**Phương thức**: `GET`

**Xác thực**: Yêu cầu

**Quyền**: `ecom.product_attributes.read`

**Phản hồi thành công**:

```json
{
  "status": {
    "code": 200,
    "message": "Operation completed successfully",
    "success": true,
    "error_code": null,
    "path": "/api/admin/v1/ecom/product-attributes/group/1",
    "timestamp": "2023-05-25T10:55:00Z",
    "details": null
  },
  "data": [
    {
      "attribute_id": 1,
      "group_id": 1,
      "name": "Màu sắc",
      "code": "color",
      "type": "SELECT",
      "unit": null,
      "validation_rules": null,
      "is_configurable": true,
      "is_filterable": true,
      "is_searchable": false,
      "is_comparable": true,
      "is_required": true,
      "frontend_input": "swatch",
      "display_order": 0,
      "created_at": "2023-05-25T10:00:00Z",
      "updated_at": "2023-05-25T10:00:00Z"
    },
    {
      "attribute_id": 3,
      "group_id": 1,
      "name": "Vật liệu",
      "code": "material",
      "type": "SELECT",
      "unit": null,
      "validation_rules": null,
      "is_configurable": false,
      "is_filterable": true,
      "is_searchable": true,
      "is_comparable": true,
      "is_required": false,
      "frontend_input": "select",
      "display_order": 2,
      "created_at": "2023-05-25T10:50:00Z",
      "updated_at": "2023-05-25T10:50:00Z"
    }
  ]
}
```

### 9. Lấy thuộc tính sản phẩm có thể cấu hình

**URL**: `/api/admin/v1/ecom/product-attributes/configurable`

**Phương thức**: `GET`

**Xác thực**: Yêu cầu

**Quyền**: `ecom.product_attributes.read`

**Phản hồi thành công**:

```json
{
  "status": {
    "code": 200,
    "message": "Operation completed successfully",
    "success": true,
    "error_code": null,
    "path": "/api/admin/v1/ecom/product-attributes/configurable",
    "timestamp": "2023-05-25T11:00:00Z",
    "details": null
  },
  "data": [
    {
      "attribute_id": 1,
      "group_id": 1,
      "name": "Màu sắc",
      "code": "color",
      "type": "SELECT",
      "unit": null,
      "validation_rules": null,
      "is_configurable": true,
      "is_filterable": true,
      "is_searchable": false,
      "is_comparable": true,
      "is_required": true,
      "frontend_input": "swatch",
      "display_order": 0,
      "created_at": "2023-05-25T10:00:00Z",
      "updated_at": "2023-05-25T10:00:00Z"
    }
  ]
}
```

### 10. Lấy thuộc tính sản phẩm có thể lọc

**URL**: `/api/admin/v1/ecom/product-attributes/filterable`

**Phương thức**: `GET`

**Xác thực**: Yêu cầu

**Quyền**: `ecom.product_attributes.read`

**Phản hồi thành công**:

```json
{
  "status": {
    "code": 200,
    "message": "Operation completed successfully",
    "success": true,
    "error_code": null,
    "path": "/api/admin/v1/ecom/product-attributes/filterable",
    "timestamp": "2023-05-25T11:05:00Z",
    "details": null
  },
  "data": [
    {
      "attribute_id": 1,
      "group_id": 1,
      "name": "Màu sắc",
      "code": "color",
      "type": "SELECT",
      "unit": null,
      "validation_rules": null,
      "is_configurable": true,
      "is_filterable": true,
      "is_searchable": false,
      "is_comparable": true,
      "is_required": true,
      "frontend_input": "swatch",
      "display_order": 0,
      "created_at": "2023-05-25T10:00:00Z",
      "updated_at": "2023-05-25T10:00:00Z"
    },
    {
      "attribute_id": 3,
      "group_id": 1,
      "name": "Vật liệu",
      "code": "material",
      "type": "SELECT",
      "unit": null,
      "validation_rules": null,
      "is_configurable": false,
      "is_filterable": true,
      "is_searchable": true,
      "is_comparable": true,
      "is_required": false,
      "frontend_input": "select",
      "display_order": 2,
      "created_at": "2023-05-25T10:50:00Z",
      "updated_at": "2023-05-25T10:50:00Z"
    }
  ]
}
```

## Mã lỗi

- `ERROR_TENANT_IDENTIFICATION`: Không thể xác định tenant
- `ERROR_INVALID_LIMIT`: Limit không hợp lệ
- `ERROR_FETCHING_ATTRIBUTES`: Lỗi khi lấy danh sách thuộc tính sản phẩm
- `ERROR_INVALID_ID`: ID không hợp lệ
- `ERROR_ATTRIBUTE_NOT_FOUND`: Không tìm thấy thuộc tính sản phẩm
- `ERROR_INVALID_CODE`: Mã không hợp lệ
- `ERROR_INVALID_REQUEST_DATA`: Dữ liệu không hợp lệ
- `ERROR_CREATING_ATTRIBUTE`: Lỗi khi tạo thuộc tính sản phẩm
- `ERROR_UPDATING_ATTRIBUTE`: Lỗi khi cập nhật thuộc tính sản phẩm
- `ERROR_DELETING_ATTRIBUTE`: Lỗi khi xóa thuộc tính sản phẩm
- `ERROR_FETCHING_ATTRIBUTES_BY_GROUP`: Lỗi khi lấy thuộc tính theo nhóm
- `ERROR_INVALID_GROUP_ID`: ID nhóm không hợp lệ
- `ERROR_FETCHING_CONFIGURABLE_ATTRIBUTES`: Lỗi khi lấy thuộc tính có thể cấu hình
- `ERROR_FETCHING_FILTERABLE_ATTRIBUTES`: Lỗi khi lấy thuộc tính có thể lọc