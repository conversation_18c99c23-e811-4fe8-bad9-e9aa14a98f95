openapi: 3.0.0
info:
  title: Ecom Module API
  description: API specification for the Ecom module
  version: 1.0.0
  contact:
    name: Web New Team
servers:
  - url: http://wn-api.local
    description: Ecom API endpoint

security:
  - bearerAuth: []

tags:
  - name: Categories
    description: E-commerce category management endpoints
  - name: Products
    description: Product management endpoints
  - name: ProductAttributeGroups
    description: Product attribute group management endpoints
  - name: ProductAttributes
    description: Product attribute management endpoints
  - name: ProductAttributeOptions
    description: Product attribute option management endpoints
  - name: ProductAttributeValues
    description: Product attribute value management endpoints
  - name: ProductVariants
    description: Product variant management endpoints
  - name: ProductVariantAttributeValues
    description: Product variant attribute value management endpoints

paths:
  /api/admin/v1/ecom/categories:
    get:
      summary: List categories
      description: Retrieve a paginated list of categories
      tags:
        - Categories
      parameters:
        - $ref: "#/components/parameters/tenantIdParam"
        - name: cursor
          in: query
          description: Cursor for pagination
          schema:
            type: string
        - name: limit
          in: query
          description: Number of items per page
          schema:
            type: integer
            default: 10
        - name: search
          in: query
          description: Search query
          schema:
            type: string
      responses:
        "200":
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CategoryListResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
    post:
      summary: Create a category
      description: Create a new category
      tags:
        - Categories
      security:
        - bearerAuth: []
      parameters:
        - $ref: "#/components/parameters/tenantIdParam"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CategoryCreate"
      responses:
        "201":
          description: Category created successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CategoryResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"

  /api/admin/v1/ecom/categories/tree:
    get:
      summary: Get category tree
      description: Retrieve the full category tree
      tags:
        - Categories
      parameters:
        - $ref: "#/components/parameters/tenantIdParam"
      responses:
        "200":
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CategoryListResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"

  /api/admin/v1/ecom/categories/{id}:
    get:
      summary: Get category
      description: Retrieve details of a specific category
      tags:
        - Categories
      parameters:
        - $ref: "#/components/parameters/tenantIdParam"
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        "200":
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CategoryResponse"
        "404":
          $ref: "#/components/responses/NotFound"
        "401":
          $ref: "#/components/responses/Unauthorized"
    put:
      summary: Update category
      description: Update an existing category
      tags:
        - Categories
      security:
        - bearerAuth: []
      parameters:
        - $ref: "#/components/parameters/tenantIdParam"
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CategoryUpdate"
      responses:
        "200":
          description: Category updated successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CategoryResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "404":
          $ref: "#/components/responses/NotFound"
    delete:
      summary: Delete category
      description: Delete a category
      tags:
        - Categories
      security:
        - bearerAuth: []
      parameters:
        - $ref: "#/components/parameters/tenantIdParam"
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        "200":
          description: Category deleted successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/StatusOnlyResponse"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"

  /api/admin/v1/ecom/categories/{id}/move:
    post:
      summary: Move category node
      description: Move a category node to a new parent/position
      tags:
        - Categories
      security:
        - bearerAuth: []
      parameters:
        - $ref: "#/components/parameters/tenantIdParam"
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/MoveNodeRequest"
      responses:
        "200":
          description: Category moved successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/StatusOnlyResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"

  /api/admin/v1/ecom/categories/move-node:
    post:
      summary: Move category node (relative)
      description: Move a category node relative to another node
      tags:
        - Categories
      security:
        - bearerAuth: []
      parameters:
        - $ref: "#/components/parameters/tenantIdParam"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/MoveNodeRequest"
      responses:
        "200":
          description: Category moved successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/StatusOnlyResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"

  /api/admin/v1/ecom/categories/move-node-root:
    post:
      summary: Move category node to root
      description: Move a category node to become a root node
      tags:
        - Categories
      security:
        - bearerAuth: []
      parameters:
        - $ref: "#/components/parameters/tenantIdParam"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/MoveNodeRootRequest"
      responses:
        "200":
          description: Category moved to root successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/StatusOnlyResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"

  /api/admin/v1/ecom/categories/update-position:
    post:
      summary: Update category position
      description: Update the position of a category relative to another
      tags:
        - Categories
      security:
        - bearerAuth: []
      parameters:
        - $ref: "#/components/parameters/tenantIdParam"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdatePositionRequest"
      responses:
        "200":
          description: Category position updated successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/StatusOnlyResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"

  /api/admin/v1/ecom/categories/rebuild:
    post:
      summary: Rebuild category tree
      description: Rebuild the entire category tree structure
      tags:
        - Categories
      security:
        - bearerAuth: []
      parameters:
        - $ref: "#/components/parameters/tenantIdParam"
      responses:
        "200":
          description: Category tree rebuilt successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/StatusOnlyResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"

  /api/admin/v1/ecom/products:
    get:
      summary: List products
      description: Retrieve a paginated list of products
      tags:
        - Products
      parameters:
        - $ref: "#/components/parameters/tenantIdParam"
        - name: cursor
          in: query
          description: Cursor for pagination
          schema:
            type: string
        - name: limit
          in: query
          description: Number of items per page
          schema:
            type: integer
            default: 10
        - name: search
          in: query
          description: Search query
          schema:
            type: string
      responses:
        "200":
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProductListResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
    post:
      summary: Create a product
      description: Create a new product
      tags:
        - Products
      security:
        - bearerAuth: []
      parameters:
        - $ref: "#/components/parameters/tenantIdParam"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ProductCreate"
      responses:
        "201":
          description: Product created successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProductResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"

  /api/admin/v1/ecom/products/{id}:
    get:
      summary: Get product
      description: Retrieve details of a specific product
      tags:
        - Products
      parameters:
        - $ref: "#/components/parameters/tenantIdParam"
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        "200":
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProductResponse"
        "404":
          $ref: "#/components/responses/NotFound"
        "401":
          $ref: "#/components/responses/Unauthorized"
    put:
      summary: Update product
      description: Update an existing product
      tags:
        - Products
      security:
        - bearerAuth: []
      parameters:
        - $ref: "#/components/parameters/tenantIdParam"
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ProductUpdate"
      responses:
        "200":
          description: Product updated successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProductResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "404":
          $ref: "#/components/responses/NotFound"
    delete:
      summary: Delete product
      description: Delete a product
      tags:
        - Products
      security:
        - bearerAuth: []
      parameters:
        - $ref: "#/components/parameters/tenantIdParam"
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        "200":
          description: Product deleted successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/StatusOnlyResponse"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"

  /api/admin/v1/ecom/products/categories:
    get:
      summary: Get product categories
      description: Retrieve all categories that have products
      tags:
        - Products
      parameters:
        - $ref: "#/components/parameters/tenantIdParam"
      responses:
        "200":
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CategoryListResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"

  /api/admin/v1/ecom/products/search:
    get:
      summary: Search products
      description: Search products with advanced filtering
      tags:
        - Products
      parameters:
        - $ref: "#/components/parameters/tenantIdParam"
        - name: cursor
          in: query
          description: Cursor for pagination
          schema:
            type: string
        - name: limit
          in: query
          description: Number of items per page
          schema:
            type: integer
            default: 10
        - name: search
          in: query
          description: Search query
          schema:
            type: string
        - name: category_id
          in: query
          description: Filter by category ID
          schema:
            type: integer
        - name: status
          in: query
          description: Filter by product status
          schema:
            type: string
            enum: [DRAFT, PUBLISHED, ARCHIVED, PENDING_APPROVAL]
        - name: product_type
          in: query
          description: Filter by product type
          schema:
            type: string
            enum: [SIMPLE, CONFIGURABLE, BUNDLE, VIRTUAL, DOWNLOADABLE, COMPOSITE, GIFT_CARD, GROUPED]
        - name: min_price
          in: query
          description: Filter by minimum price
          schema:
            type: number
            format: float
        - name: max_price
          in: query
          description: Filter by maximum price
          schema:
            type: number
            format: float
      responses:
        "200":
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProductListResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"

  /api/admin/v1/ecom/products/all:
    get:
      summary: Get all products
      description: Retrieve all products without pagination
      tags:
        - Products
      parameters:
        - $ref: "#/components/parameters/tenantIdParam"
      responses:
        "200":
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProductListResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"

  /api/admin/v1/ecom/product-attribute-groups:
    get:
      summary: List product attribute groups
      description: Retrieve a paginated list of product attribute groups
      tags:
        - ProductAttributeGroups
      parameters:
        - $ref: "#/components/parameters/tenantIdParam"
        - name: cursor
          in: query
          description: Cursor for pagination
          schema:
            type: string
        - name: limit
          in: query
          description: Number of items per page
          schema:
            type: integer
            default: 10
        - name: search
          in: query
          description: Search query
          schema:
            type: string
      responses:
        "200":
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProductAttributeGroupListResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
    post:
      summary: Create a product attribute group
      description: Create a new product attribute group
      tags:
        - ProductAttributeGroups
      security:
        - bearerAuth: []
      parameters:
        - $ref: "#/components/parameters/tenantIdParam"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ProductAttributeGroupCreate"
      responses:
        "201":
          description: Product attribute group created successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProductAttributeGroupResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"

  /api/admin/v1/ecom/product-attribute-groups/{id}:
    get:
      summary: Get product attribute group
      description: Retrieve details of a specific product attribute group
      tags:
        - ProductAttributeGroups
      parameters:
        - $ref: "#/components/parameters/tenantIdParam"
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        "200":
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProductAttributeGroupResponse"
        "404":
          $ref: "#/components/responses/NotFound"
        "401":
          $ref: "#/components/responses/Unauthorized"
    put:
      summary: Update product attribute group
      description: Update an existing product attribute group
      tags:
        - ProductAttributeGroups
      security:
        - bearerAuth: []
      parameters:
        - $ref: "#/components/parameters/tenantIdParam"
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ProductAttributeGroupUpdate"
      responses:
        "200":
          description: Product attribute group updated successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProductAttributeGroupResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "404":
          $ref: "#/components/responses/NotFound"
    delete:
      summary: Delete product attribute group
      description: Delete a product attribute group
      tags:
        - ProductAttributeGroups
      security:
        - bearerAuth: []
      parameters:
        - $ref: "#/components/parameters/tenantIdParam"
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        "200":
          description: Product attribute group deleted successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/StatusOnlyResponse"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"

  /api/admin/v1/ecom/product-attribute-groups/code/{code}:
    get:
      summary: Get product attribute group by code
      description: Retrieve details of a specific product attribute group by its code
      tags:
        - ProductAttributeGroups
      parameters:
        - $ref: "#/components/parameters/tenantIdParam"
        - name: code
          in: path
          required: true
          schema:
            type: string
      responses:
        "200":
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProductAttributeGroupResponse"
        "404":
          $ref: "#/components/responses/NotFound"
        "401":
          $ref: "#/components/responses/Unauthorized"

  /api/admin/v1/ecom/product-attribute-groups/all:
    get:
      summary: Get all product attribute groups
      description: Retrieve all product attribute groups without pagination
      tags:
        - ProductAttributeGroups
      parameters:
        - $ref: "#/components/parameters/tenantIdParam"
      responses:
        "200":
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProductAttributeGroupListResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"

  /api/admin/v1/ecom/product-attributes:
    get:
      summary: List product attributes
      description: Retrieve a paginated list of product attributes
      tags:
        - ProductAttributes
      parameters:
        - $ref: "#/components/parameters/tenantIdParam"
        - name: cursor
          in: query
          description: Cursor for pagination
          schema:
            type: string
        - name: limit
          in: query
          description: Number of items per page
          schema:
            type: integer
            default: 10
        - name: search
          in: query
          description: Search query
          schema:
            type: string
      responses:
        "200":
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProductAttributeListResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
    post:
      summary: Create a product attribute
      description: Create a new product attribute
      tags:
        - ProductAttributes
      security:
        - bearerAuth: []
      parameters:
        - $ref: "#/components/parameters/tenantIdParam"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ProductAttributeCreate"
      responses:
        "201":
          description: Product attribute created successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProductAttributeResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"

  /api/admin/v1/ecom/product-attributes/{id}:
    get:
      summary: Get product attribute
      description: Retrieve details of a specific product attribute
      tags:
        - ProductAttributes
      parameters:
        - $ref: "#/components/parameters/tenantIdParam"
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        "200":
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProductAttributeResponse"
        "404":
          $ref: "#/components/responses/NotFound"
        "401":
          $ref: "#/components/responses/Unauthorized"
    put:
      summary: Update product attribute
      description: Update an existing product attribute
      tags:
        - ProductAttributes
      security:
        - bearerAuth: []
      parameters:
        - $ref: "#/components/parameters/tenantIdParam"
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ProductAttributeUpdate"
      responses:
        "200":
          description: Product attribute updated successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProductAttributeResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "404":
          $ref: "#/components/responses/NotFound"
    delete:
      summary: Delete product attribute
      description: Delete a product attribute
      tags:
        - ProductAttributes
      security:
        - bearerAuth: []
      parameters:
        - $ref: "#/components/parameters/tenantIdParam"
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        "200":
          description: Product attribute deleted successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/StatusOnlyResponse"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"

  /api/admin/v1/ecom/product-attributes/code/{code}:
    get:
      summary: Get product attribute by code
      description: Retrieve details of a specific product attribute by its code
      tags:
        - ProductAttributes
      parameters:
        - $ref: "#/components/parameters/tenantIdParam"
        - name: code
          in: path
          required: true
          schema:
            type: string
      responses:
        "200":
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProductAttributeResponse"
        "404":
          $ref: "#/components/responses/NotFound"
        "401":
          $ref: "#/components/responses/Unauthorized"

  /api/admin/v1/ecom/product-attributes/all:
    get:
      summary: Get all product attributes
      description: Retrieve all product attributes without pagination
      tags:
        - ProductAttributes
      parameters:
        - $ref: "#/components/parameters/tenantIdParam"
      responses:
        "200":
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProductAttributeListResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"

  /api/admin/v1/ecom/product-attributes/group/{group_id}:
    get:
      summary: Get product attributes by group
      description: Retrieve all product attributes belonging to a specific group
      tags:
        - ProductAttributes
      parameters:
        - $ref: "#/components/parameters/tenantIdParam"
        - name: group_id
          in: path
          required: true
          schema:
            type: integer
      responses:
        "200":
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProductAttributeListResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"

  /api/admin/v1/ecom/product-attributes/configurable:
    get:
      summary: Get configurable product attributes
      description: Retrieve all product attributes that can be used for product configuration
      tags:
        - ProductAttributes
      parameters:
        - $ref: "#/components/parameters/tenantIdParam"
      responses:
        "200":
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProductAttributeListResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"

  /api/admin/v1/ecom/product-attributes/filterable:
    get:
      summary: Get filterable product attributes
      description: Retrieve all product attributes that can be used for product filtering
      tags:
        - ProductAttributes
      parameters:
        - $ref: "#/components/parameters/tenantIdParam"
      responses:
        "200":
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProductAttributeListResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"

  /api/admin/v1/ecom/product-attribute-options:
    get:
      summary: List product attribute options
      description: Retrieve a paginated list of product attribute options
      tags:
        - ProductAttributeOptions
      parameters:
        - $ref: "#/components/parameters/tenantIdParam"
        - name: cursor
          in: query
          description: Cursor for pagination
          schema:
            type: string
        - name: limit
          in: query
          description: Number of items per page
          schema:
            type: integer
            default: 10
        - name: search
          in: query
          description: Search query
          schema:
            type: string
      responses:
        "200":
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProductAttributeOptionListResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
    post:
      summary: Create a product attribute option
      description: Create a new product attribute option
      tags:
        - ProductAttributeOptions
      security:
        - bearerAuth: []
      parameters:
        - $ref: "#/components/parameters/tenantIdParam"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ProductAttributeOptionCreate"
      responses:
        "201":
          description: Product attribute option created successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProductAttributeOptionResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"

  /api/admin/v1/ecom/product-attribute-options/{id}:
    get:
      summary: Get product attribute option
      description: Retrieve details of a specific product attribute option
      tags:
        - ProductAttributeOptions
      parameters:
        - $ref: "#/components/parameters/tenantIdParam"
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        "200":
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProductAttributeOptionResponse"
        "404":
          $ref: "#/components/responses/NotFound"
        "401":
          $ref: "#/components/responses/Unauthorized"
    put:
      summary: Update product attribute option
      description: Update an existing product attribute option
      tags:
        - ProductAttributeOptions
      security:
        - bearerAuth: []
      parameters:
        - $ref: "#/components/parameters/tenantIdParam"
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ProductAttributeOptionUpdate"
      responses:
        "200":
          description: Product attribute option updated successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProductAttributeOptionResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "404":
          $ref: "#/components/responses/NotFound"
    delete:
      summary: Delete product attribute option
      description: Delete a product attribute option
      tags:
        - ProductAttributeOptions
      security:
        - bearerAuth: []
      parameters:
        - $ref: "#/components/parameters/tenantIdParam"
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        "200":
          description: Product attribute option deleted successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/StatusOnlyResponse"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"

  /api/admin/v1/ecom/product-attribute-options/all:
    get:
      summary: Get all product attribute options
      description: Retrieve all product attribute options without pagination
      tags:
        - ProductAttributeOptions
      parameters:
        - $ref: "#/components/parameters/tenantIdParam"
      responses:
        "200":
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProductAttributeOptionListResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"

  /api/admin/v1/ecom/product-attribute-options/attribute/{attribute_id}:
    get:
      summary: Get product attribute options by attribute
      description: Retrieve all product attribute options belonging to a specific attribute
      tags:
        - ProductAttributeOptions
      parameters:
        - $ref: "#/components/parameters/tenantIdParam"
        - name: attribute_id
          in: path
          required: true
          schema:
            type: integer
      responses:
        "200":
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProductAttributeOptionListResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"

  /api/admin/v1/ecom/product-attribute-values:
    post:
      summary: Create a product attribute value
      description: Create a new product attribute value
      tags:
        - ProductAttributeValues
      security:
        - bearerAuth: []
      parameters:
        - $ref: "#/components/parameters/tenantIdParam"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ProductAttributeValueCreate"
      responses:
        "201":
          description: Product attribute value created successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProductAttributeValueResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"

  /api/admin/v1/ecom/product-attribute-values/batch:
    post:
      summary: Create multiple product attribute values
      description: Create multiple product attribute values in a single request
      tags:
        - ProductAttributeValues
      security:
        - bearerAuth: []
      parameters:
        - $ref: "#/components/parameters/tenantIdParam"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - product_id
                - attribute_values
              properties:
                product_id:
                  type: integer
                  description: ID of the product to associate attribute values with
                attribute_values:
                  type: array
                  description: List of attribute values to create
                  items:
                    type: object
                    required:
                      - attribute_id
                    properties:
                      attribute_id:
                        type: integer
                        description: ID of the attribute
                      value_text:
                        type: string
                        nullable: true
                        description: Text value for TEXT, TEXTAREA type attributes
                      value_numeric:
                        type: number
                        format: float
                        nullable: true
                        description: Numeric value for NUMBER, PRICE type attributes
                      value_date:
                        type: string
                        format: date-time
                        nullable: true
                        description: Date/time value for DATE, DATETIME type attributes
                      value_boolean:
                        type: boolean
                        nullable: true
                        description: Boolean value for BOOLEAN type attributes
                      attribute_option_id:
                        type: integer
                        nullable: true
                        description: Option ID for SELECT, MULTISELECT type attributes
      responses:
        "201":
          description: Product attribute values created successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/BatchResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"

  /api/admin/v1/ecom/product-attribute-values/{id}:
    get:
      summary: Get product attribute value
      description: Retrieve details of a specific product attribute value
      tags:
        - ProductAttributeValues
      parameters:
        - $ref: "#/components/parameters/tenantIdParam"
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        "200":
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProductAttributeValueResponse"
        "404":
          $ref: "#/components/responses/NotFound"
        "401":
          $ref: "#/components/responses/Unauthorized"
    put:
      summary: Update product attribute value
      description: Update an existing product attribute value
      tags:
        - ProductAttributeValues
      security:
        - bearerAuth: []
      parameters:
        - $ref: "#/components/parameters/tenantIdParam"
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ProductAttributeValueUpdate"
      responses:
        "200":
          description: Product attribute value updated successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProductAttributeValueResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "404":
          $ref: "#/components/responses/NotFound"
    delete:
      summary: Delete product attribute value
      description: Delete a product attribute value
      tags:
        - ProductAttributeValues
      security:
        - bearerAuth: []
      parameters:
        - $ref: "#/components/parameters/tenantIdParam"
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        "200":
          description: Product attribute value deleted successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/StatusOnlyResponse"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"

  /api/admin/v1/ecom/product-attribute-values/product/{product_id}:
    get:
      summary: Get product attribute values by product
      description: Retrieve all product attribute values belonging to a specific product
      tags:
        - ProductAttributeValues
      parameters:
        - $ref: "#/components/parameters/tenantIdParam"
        - name: product_id
          in: path
          required: true
          schema:
            type: integer
      responses:
        "200":
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProductAttributeValueListResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
    delete:
      summary: Delete product attribute values by product
      description: Delete all product attribute values belonging to a specific product
      tags:
        - ProductAttributeValues
      security:
        - bearerAuth: []
      parameters:
        - $ref: "#/components/parameters/tenantIdParam"
        - name: product_id
          in: path
          required: true
          schema:
            type: integer
      responses:
        "200":
          description: Product attribute values deleted successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/StatusOnlyResponse"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"

  /api/admin/v1/ecom/product-variants:
    get:
      summary: List product variants
      description: Retrieve a paginated list of product variants
      tags:
        - ProductVariants
      parameters:
        - $ref: "#/components/parameters/tenantIdParam"
        - name: cursor
          in: query
          description: Cursor for pagination
          schema:
            type: string
        - name: limit
          in: query
          description: Number of items per page
          schema:
            type: integer
            default: 10
        - name: search
          in: query
          description: Search query
          schema:
            type: string
      responses:
        "200":
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProductVariantListResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
    post:
      summary: Create a product variant
      description: Create a new product variant
      tags:
        - ProductVariants
      security:
        - bearerAuth: []
      parameters:
        - $ref: "#/components/parameters/tenantIdParam"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ProductVariantCreate"
      responses:
        "201":
          description: Product variant created successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProductVariantResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"

  /api/admin/v1/ecom/product-variants/batch:
    post:
      summary: Create multiple product variants
      description: Create multiple product variants in a single request
      tags:
        - ProductVariants
      security:
        - bearerAuth: []
      parameters:
        - $ref: "#/components/parameters/tenantIdParam"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                variants:
                  type: array
                  items:
                    $ref: "#/components/schemas/ProductVariantCreate"
      responses:
        "201":
          description: Product variants created successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/BatchResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"

  /api/admin/v1/ecom/product-variants/{id}:
    get:
      summary: Get product variant
      description: Retrieve details of a specific product variant
      tags:
        - ProductVariants
      parameters:
        - $ref: "#/components/parameters/tenantIdParam"
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        "200":
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProductVariantResponse"
        "404":
          $ref: "#/components/responses/NotFound"
        "401":
          $ref: "#/components/responses/Unauthorized"
    put:
      summary: Update product variant
      description: Update an existing product variant
      tags:
        - ProductVariants
      security:
        - bearerAuth: []
      parameters:
        - $ref: "#/components/parameters/tenantIdParam"
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ProductVariantUpdate"
      responses:
        "200":
          description: Product variant updated successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProductVariantResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "404":
          $ref: "#/components/responses/NotFound"
    delete:
      summary: Delete product variant
      description: Delete a product variant
      tags:
        - ProductVariants
      security:
        - bearerAuth: []
      parameters:
        - $ref: "#/components/parameters/tenantIdParam"
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        "200":
          description: Product variant deleted successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/StatusOnlyResponse"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"

  /api/admin/v1/ecom/product-variants/product/{productId}:
    get:
      summary: Get product variants by product
      description: Retrieve all product variants belonging to a specific product
      tags:
        - ProductVariants
      parameters:
        - $ref: "#/components/parameters/tenantIdParam"
        - name: productId
          in: path
          required: true
          schema:
            type: integer
      responses:
        "200":
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProductVariantListResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
    delete:
      summary: Delete product variants by product
      description: Delete all product variants belonging to a specific product
      tags:
        - ProductVariants
      security:
        - bearerAuth: []
      parameters:
        - $ref: "#/components/parameters/tenantIdParam"
        - name: productId
          in: path
          required: true
          schema:
            type: integer
      responses:
        "200":
          description: Product variants deleted successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/StatusOnlyResponse"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"

  /api/admin/v1/ecom/product-variant-attribute-values:
    post:
      summary: Create a product variant attribute value
      description: Create a new product variant attribute value
      tags:
        - ProductVariantAttributeValues
      security:
        - bearerAuth: []
      parameters:
        - $ref: "#/components/parameters/tenantIdParam"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ProductVariantAttributeValueCreate"
      responses:
        "201":
          description: Product variant attribute value created successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProductVariantAttributeValueResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"

  /api/admin/v1/ecom/product-variant-attribute-values/batch:
    post:
      summary: Create multiple product variant attribute values
      description: Create multiple product variant attribute values in a single request
      tags:
        - ProductVariantAttributeValues
      security:
        - bearerAuth: []
      parameters:
        - $ref: "#/components/parameters/tenantIdParam"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                values:
                  type: array
                  items:
                    $ref: "#/components/schemas/ProductVariantAttributeValueCreate"
      responses:
        "201":
          description: Product variant attribute values created successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/BatchResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"

  /api/admin/v1/ecom/product-variant-attribute-values/{id}:
    get:
      summary: Get product variant attribute value
      description: Retrieve details of a specific product variant attribute value
      tags:
        - ProductVariantAttributeValues
      parameters:
        - $ref: "#/components/parameters/tenantIdParam"
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        "200":
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProductVariantAttributeValueResponse"
        "404":
          $ref: "#/components/responses/NotFound"
        "401":
          $ref: "#/components/responses/Unauthorized"
    put:
      summary: Update product variant attribute value
      description: Update an existing product variant attribute value
      tags:
        - ProductVariantAttributeValues
      security:
        - bearerAuth: []
      parameters:
        - $ref: "#/components/parameters/tenantIdParam"
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ProductVariantAttributeValueUpdate"
      responses:
        "200":
          description: Product variant attribute value updated successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProductVariantAttributeValueResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "404":
          $ref: "#/components/responses/NotFound"
    delete:
      summary: Delete product variant attribute value
      description: Delete a product variant attribute value
      tags:
        - ProductVariantAttributeValues
      security:
        - bearerAuth: []
      parameters:
        - $ref: "#/components/parameters/tenantIdParam"
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        "200":
          description: Product variant attribute value deleted successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/StatusOnlyResponse"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"

  /api/admin/v1/ecom/product-variant-attribute-values/product-variant/{variant_id}:
    get:
      summary: Get product variant attribute values by variant
      description: Retrieve all product variant attribute values belonging to a specific variant
      tags:
        - ProductVariantAttributeValues
      parameters:
        - $ref: "#/components/parameters/tenantIdParam"
        - name: variant_id
          in: path
          required: true
          schema:
            type: integer
      responses:
        "200":
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProductVariantAttributeValueListResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
    delete:
      summary: Delete product variant attribute values by variant
      description: Delete all product variant attribute values belonging to a specific variant
      tags:
        - ProductVariantAttributeValues
      security:
        - bearerAuth: []
      parameters:
        - $ref: "#/components/parameters/tenantIdParam"
        - name: variant_id
          in: path
          required: true
          schema:
            type: integer
      responses:
        "200":
          description: Product variant attribute values deleted successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/StatusOnlyResponse"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"




  /api/admin/v1/ecom/product-variants/product/{productId}:
    get:
      summary: Get product variants by product
      description: Retrieve all product variants belonging to a specific product
      tags:
        - ProductVariants
      parameters:
        - $ref: "#/components/parameters/tenantIdParam"
        - name: productId
          in: path
          required: true
          schema:
            type: integer
      responses:
        "200":
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProductVariantListResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
    delete:
      summary: Delete product variants by product
      description: Delete all product variants belonging to a specific product
      tags:
        - ProductVariants
      security:
        - bearerAuth: []
      parameters:
        - $ref: "#/components/parameters/tenantIdParam"
        - name: productId
          in: path
          required: true
          schema:
            type: integer
      responses:
        "200":
          description: Product variants deleted successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/StatusOnlyResponse"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"

  /api/admin/v1/ecom/product-variant-attribute-values:
    post:
      summary: Create a product variant attribute value
      description: Create a new product variant attribute value
      tags:
        - ProductVariantAttributeValues
      security:
        - bearerAuth: []
      parameters:
        - $ref: "#/components/parameters/tenantIdParam"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ProductVariantAttributeValueCreate"
      responses:
        "201":
          description: Product variant attribute value created successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProductVariantAttributeValueResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"

  /api/admin/v1/ecom/product-variant-attribute-values/batch:
    post:
      summary: Create multiple product variant attribute values
      description: Create multiple product variant attribute values in a single request
      tags:
        - ProductVariantAttributeValues
      security:
        - bearerAuth: []
      parameters:
        - $ref: "#/components/parameters/tenantIdParam"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                values:
                  type: array
                  items:
                    $ref: "#/components/schemas/ProductVariantAttributeValueCreate"
      responses:
        "201":
          description: Product variant attribute values created successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/BatchResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"

  /api/admin/v1/ecom/product-variant-attribute-values/{id}:
    get:
      summary: Get product variant attribute value
      description: Retrieve details of a specific product variant attribute value
      tags:
        - ProductVariantAttributeValues
      parameters:
        - $ref: "#/components/parameters/tenantIdParam"
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        "200":
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProductVariantAttributeValueResponse"
        "404":
          $ref: "#/components/responses/NotFound"
        "401":
          $ref: "#/components/responses/Unauthorized"
    put:
      summary: Update product variant attribute value
      description: Update an existing product variant attribute value
      tags:
        - ProductVariantAttributeValues
      security:
        - bearerAuth: []
      parameters:
        - $ref: "#/components/parameters/tenantIdParam"
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ProductVariantAttributeValueUpdate"
      responses:
        "200":
          description: Product variant attribute value updated successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProductVariantAttributeValueResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "404":
          $ref: "#/components/responses/NotFound"
    delete:
      summary: Delete product variant attribute value
      description: Delete a product variant attribute value
      tags:
        - ProductVariantAttributeValues
      security:
        - bearerAuth: []
      parameters:
        - $ref: "#/components/parameters/tenantIdParam"
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        "200":
          description: Product variant attribute value deleted successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/StatusOnlyResponse"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"

  /api/admin/v1/ecom/product-variant-attribute-values/product-variant/{variant_id}:
    get:
      summary: Get product variant attribute values by variant
      description: Retrieve all product variant attribute values belonging to a specific variant
      tags:
        - ProductVariantAttributeValues
      parameters:
        - $ref: "#/components/parameters/tenantIdParam"
        - name: variant_id
          in: path
          required: true
          schema:
            type: integer
      responses:
        "200":
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProductVariantAttributeValueListResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
    delete:
      summary: Delete product variant attribute values by variant
      description: Delete all product variant attribute values belonging to a specific variant
      tags:
        - ProductVariantAttributeValues
      security:
        - bearerAuth: []
      parameters:
        - $ref: "#/components/parameters/tenantIdParam"
        - name: variant_id
          in: path
          required: true
          schema:
            type: integer
      responses:
        "200":
          description: Product variant attribute values deleted successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/StatusOnlyResponse"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
  parameters:
    tenantIdParam:
      name: X-Tenant-ID
      in: header
      required: true
      schema:
        type: string
  responses:
    BadRequest:
      description: Bad request
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/Error"
    Unauthorized:
      description: Unauthorized
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/Error"
    Forbidden:
      description: Forbidden
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/Error"
    NotFound:
      description: Not found
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/Error"
  schemas:
    Category:
      type: object
      properties:
        id:
          type: integer
        name:
          type: string
        slug:
          type: string
        parent_id:
          type: integer
        position:
          type: integer
        is_active:
          type: boolean
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
    CategoryCreate:
      type: object
      required:
        - name
      properties:
        name:
          type: string
        slug:
          type: string
        parent_id:
          type: integer
        position:
          type: integer
        is_active:
          type: boolean
    CategoryUpdate:
      type: object
      properties:
        name:
          type: string
        slug:
          type: string
        parent_id:
          type: integer
        position:
          type: integer
        is_active:
          type: boolean
    CategoryListResponse:
      type: object
      properties:
        status:
          $ref: "#/components/schemas/Status"
        data:
          type: array
          items:
            $ref: "#/components/schemas/Category"
        meta:
          type: object
          properties:
            next_cursor:
              type: string
              nullable: true
            has_more:
              type: boolean
    CategoryResponse:
      type: object
      properties:
        status:
          $ref: "#/components/schemas/Status"
        data:
          $ref: "#/components/schemas/Category"

    StatusOnlyResponse:
      type: object
      properties:
        status:
          $ref: "#/components/schemas/Status"
        data:
          oneOf:
            - type: object
            - type: "null"

    MoveNodeRequest:
      type: object
      properties:
        id:
          type: integer
        parent_id:
          type: integer
        position:
          type: integer

    MoveNodeRootRequest:
      type: object
      properties:
        id:
          type: integer
        position:
          type: integer

    UpdatePositionRequest:
      type: object
      properties:
        id:
          type: integer
        target_id:
          type: integer
        position:
          type: integer
    Product:
      type: object
      properties:
        product_id:
          type: integer
        tenant_id:
          type: integer
        category_id:
          type: integer
        name:
          type: string
        description:
          type: string
        content:
          type: string
        slug:
          type: string
        image_url:
          type: string
        base_price:
          type: number
          format: float
        cost_price:
          type: number
          format: float
        product_type:
          type: string
          enum: [simple, configurable, virtual, downloadable]
        status:
          type: string
          enum: [draft, active, inactive, archived]
        product_code:
          type: string
        is_taxable:
          type: boolean
        is_virtual:
          type: boolean
        is_downloadable:
          type: boolean
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
    ProductCreate:
      type: object
      required:
        - name
        - category_id
      properties:
        category_id:
          type: integer
        name:
          type: string
        description:
          type: string
        content:
          type: string
        slug:
          type: string
        image_url:
          type: string
        base_price:
          type: number
          format: float
        cost_price:
          type: number
          format: float
        product_type:
          type: string
          enum: [simple, configurable, virtual, downloadable]
        status:
          type: string
          enum: [draft, active, inactive, archived]
        product_code:
          type: string
        is_taxable:
          type: boolean
        is_virtual:
          type: boolean
        is_downloadable:
          type: boolean
    ProductUpdate:
      type: object
      properties:
        category_id:
          type: integer
        name:
          type: string
        description:
          type: string
        content:
          type: string
        slug:
          type: string
        image_url:
          type: string
        base_price:
          type: number
          format: float
        cost_price:
          type: number
          format: float
        product_type:
          type: string
          enum: [simple, configurable, virtual, downloadable]
        status:
          type: string
          enum: [draft, active, inactive, archived]
        product_code:
          type: string
        is_taxable:
          type: boolean
        is_virtual:
          type: boolean
        is_downloadable:
          type: boolean
    ProductListResponse:
      type: object
      properties:
        status:
          $ref: "#/components/schemas/Status"
        data:
          type: array
          items:
            $ref: "#/components/schemas/Product"
        meta:
          type: object
          properties:
            next_cursor:
              type: [string, "null"]
            has_more:
              type: boolean
    ProductResponse:
      type: object
      properties:
        status:
          $ref: "#/components/schemas/Status"
        data:
          $ref: "#/components/schemas/Product"
    ProductAttributeGroup:
      type: object
      properties:
        group_id:
          type: integer
        tenant_id:
          type: integer
        name:
          type: string
        code:
          type: string
        display_order:
          type: integer
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
    ProductAttributeGroupCreate:
      type: object
      required:
        - name
        - code
      properties:
        name:
          type: string
        code:
          type: string
        display_order:
          type: integer
    ProductAttributeGroupUpdate:
      type: object
      properties:
        name:
          type: string
        code:
          type: string
        display_order:
          type: integer
    ProductAttributeGroupListResponse:
      type: object
      properties:
        status:
          $ref: "#/components/schemas/Status"
        data:
          type: array
          items:
            $ref: "#/components/schemas/ProductAttributeGroup"
        meta:
          type: object
          properties:
            next_cursor:
              type: string
              nullable: true
            has_more:
              type: boolean
    ProductAttributeGroupResponse:
      type: object
      properties:
        status:
          $ref: "#/components/schemas/Status"
        data:
          $ref: "#/components/schemas/ProductAttributeGroup"
    ProductAttribute:
      type: object
      properties:
        attribute_id:
          type: integer
        tenant_id:
          type: integer
        group_id:
          type: integer
          nullable: true
        name:
          type: string
        code:
          type: string
        type:
          type: string
          enum: [TEXT, TEXTAREA, SELECT, MULTISELECT, BOOLEAN, DATE, DATETIME, NUMBER, PRICE]
        unit:
          type: string
          nullable: true
        validation_rules:
          type: string
          nullable: true
        is_configurable:
          type: boolean
        is_filterable:
          type: boolean
        is_searchable:
          type: boolean
        is_comparable:
          type: boolean
        is_required:
          type: boolean
        frontend_input:
          type: string
          nullable: true
        display_order:
          type: integer
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
    ProductAttributeCreate:
      type: object
      required:
        - name
        - code
        - type
      properties:
        group_id:
          type: integer
          nullable: true
        name:
          type: string
        code:
          type: string
        type:
          type: string
          enum: [TEXT, TEXTAREA, SELECT, MULTISELECT, BOOLEAN, DATE, DATETIME, NUMBER, PRICE]
        unit:
          type: string
          nullable: true
        validation_rules:
          type: string
          nullable: true
        is_configurable:
          type: boolean
          default: false
        is_filterable:
          type: boolean
          default: false
        is_searchable:
          type: boolean
          default: false
        is_comparable:
          type: boolean
          default: false
        is_required:
          type: boolean
          default: false
        frontend_input:
          type: string
          nullable: true
        display_order:
          type: integer
          default: 0
    ProductAttributeUpdate:
      type: object
      required:
        - name
        - code
        - type
      properties:
        group_id:
          type: integer
          nullable: true
        name:
          type: string
        code:
          type: string
        type:
          type: string
          enum: [TEXT, TEXTAREA, SELECT, MULTISELECT, BOOLEAN, DATE, DATETIME, NUMBER, PRICE]
        unit:
          type: string
          nullable: true
        validation_rules:
          type: string
          nullable: true
        is_configurable:
          type: boolean
          default: false
        is_filterable:
          type: boolean
          default: false
        is_searchable:
          type: boolean
          default: false
        is_comparable:
          type: boolean
          default: false
        is_required:
          type: boolean
          default: false
        frontend_input:
          type: string
          nullable: true
        display_order:
          type: integer
          default: 0
    ProductAttributeListResponse:
      type: object
      properties:
        status:
          $ref: "#/components/schemas/Status"
        data:
          type: array
          items:
            $ref: "#/components/schemas/ProductAttribute"
        meta:
          type: object
          properties:
            next_cursor:
              type: string
              nullable: true
            has_more:
              type: boolean
    ProductAttributeResponse:
      type: object
      properties:
        status:
          $ref: "#/components/schemas/Status"
        data:
          $ref: "#/components/schemas/ProductAttribute"
    ProductAttributeOption:
      type: object
      properties:
        option_id:
          type: integer
        tenant_id:
          type: integer
        attribute_id:
          type: integer
        value:
          type: string
        label:
          type: string
          nullable: true
        swatch_type:
          type: string
          enum: [COLOR, IMAGE, TEXT]
          nullable: true
        swatch_value:
          type: string
          nullable: true
        display_order:
          type: integer
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
    ProductAttributeOptionCreate:
      type: object
      required:
        - attribute_id
        - value
      properties:
        attribute_id:
          type: integer
        value:
          type: string
        label:
          type: string
          nullable: true
        swatch_type:
          type: string
          enum: [COLOR, IMAGE, TEXT]
          nullable: true
        swatch_value:
          type: string
          nullable: true
        display_order:
          type: integer
    ProductAttributeOptionUpdate:
      type: object
      properties:
        attribute_id:
          type: integer
        value:
          type: string
        label:
          type: string
          nullable: true
        swatch_type:
          type: string
          enum: [COLOR, IMAGE, TEXT]
          nullable: true
        swatch_value:
          type: string
          nullable: true
        display_order:
          type: integer
    ProductAttributeOptionListResponse:
      type: object
      properties:
        status:
          $ref: "#/components/schemas/Status"
        data:
          type: array
          items:
            $ref: "#/components/schemas/ProductAttributeOption"
        meta:
          type: object
          properties:
            next_cursor:
              type: string
              nullable: true
            has_more:
              type: boolean
    ProductAttributeOptionResponse:
      type: object
      properties:
        status:
          $ref: "#/components/schemas/Status"
        data:
          $ref: "#/components/schemas/ProductAttributeOption"
    ProductAttributeValue:
      type: object
      properties:
        value_id:
          type: integer
        tenant_id:
          type: integer
        reference_id:
          type: integer
        reference_type:
          type: string
          enum: [PRODUCT]
        attribute_id:
          type: integer
        value_text:
          type: string
          nullable: true
        value_numeric:
          type: number
          format: float
          nullable: true
        value_date:
          type: string
          format: date-time
          nullable: true
        value_boolean:
          type: boolean
          nullable: true
        attribute_option_id:
          type: integer
          nullable: true
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        attribute:
          $ref: "#/components/schemas/ProductAttribute"
          nullable: true
        attribute_option:
          $ref: "#/components/schemas/ProductAttributeOption"
          nullable: true
    ProductAttributeValueCreate:
      type: object
      required:
        - reference_id
        - reference_type
        - attribute_id
      properties:
        reference_id:
          type: integer
          description: ID of the product or other entity this attribute value belongs to
        reference_type:
          type: string
          enum: [PRODUCT]
          description: Type of entity this attribute value belongs to
        attribute_id:
          type: integer
          description: ID of the attribute this value is for
        value_text:
          type: string
          nullable: true
          description: Text value for TEXT, TEXTAREA type attributes
        value_numeric:
          type: number
          format: float
          nullable: true
          description: Numeric value for NUMBER, PRICE type attributes
        value_date:
          type: string
          format: date-time
          nullable: true
          description: Date/time value for DATE, DATETIME type attributes
        value_boolean:
          type: boolean
          nullable: true
          description: Boolean value for BOOLEAN type attributes
        attribute_option_id:
          type: integer
          nullable: true
          description: Option ID for SELECT, MULTISELECT type attributes
    ProductAttributeValueUpdate:
      type: object
      properties:
        value_text:
          type: string
          nullable: true
          description: Text value for TEXT, TEXTAREA type attributes
        value_numeric:
          type: number
          format: float
          nullable: true
          description: Numeric value for NUMBER, PRICE type attributes
        value_date:
          type: string
          format: date-time
          nullable: true
          description: Date/time value for DATE, DATETIME type attributes
        value_boolean:
          type: boolean
          nullable: true
          description: Boolean value for BOOLEAN type attributes
        attribute_option_id:
          type: integer
          nullable: true
          description: Option ID for SELECT, MULTISELECT type attributes
    ProductAttributeValueListResponse:
      type: object
      properties:
        status:
          $ref: "#/components/schemas/Status"
        data:
          type: array
          items:
            $ref: "#/components/schemas/ProductAttributeValue"
        meta:
          type: object
          properties:
            next_cursor:
              type: string
              nullable: true
            has_more:
              type: boolean
    ProductAttributeValueResponse:
      type: object
      properties:
        status:
          $ref: "#/components/schemas/Status"
        data:
          $ref: "#/components/schemas/ProductAttributeValue"
    BatchResponse:
      type: object
      properties:
        status:
          $ref: "#/components/schemas/Status"
        data:
          type: object
          properties:
            success_count:
              type: integer
            failed_count:
              type: integer
            failed_items:
              type: array
              items:
                type: object
                properties:
                  index:
                    type: integer
                  error:
                    type: string
    ProductVariant:
      type: object
      properties:
        variant_id:
          type: integer
        tenant_id:
          type: integer
        product_id:
          type: integer
        sku:
          type: string
        price:
          type: number
          format: float
          nullable: true
        cost_price:
          type: number
          format: float
          nullable: true
        image_url:
          type: string
          nullable: true
        is_active:
          type: boolean
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        attribute_values:
          type: array
          items:
            $ref: "#/components/schemas/ProductVariantAttributeValue"
          nullable: true
    ProductVariantCreate:
      type: object
      required:
        - product_id
        - sku
      properties:
        product_id:
          type: integer
        sku:
          type: string
        price:
          type: number
          format: float
          nullable: true
        cost_price:
          type: number
          format: float
          nullable: true
        image_url:
          type: string
          nullable: true
        is_active:
          type: boolean
        attribute_values:
          type: array
          items:
            $ref: "#/components/schemas/ProductVariantAttributeValueCreate"
          nullable: true
    ProductVariantUpdate:
      type: object
      properties:
        product_id:
          type: integer
        sku:
          type: string
        price:
          type: number
          format: float
          nullable: true
        cost_price:
          type: number
          format: float
          nullable: true
        image_url:
          type: string
          nullable: true
        is_active:
          type: boolean
    ProductVariantListResponse:
      type: object
      properties:
        status:
          $ref: "#/components/schemas/Status"
        data:
          type: array
          items:
            $ref: "#/components/schemas/ProductVariant"
        meta:
          type: object
          properties:
            next_cursor:
              type: string
              nullable: true
            has_more:
              type: boolean
    ProductVariantResponse:
      type: object
      properties:
        status:
          $ref: "#/components/schemas/Status"
        data:
          $ref: "#/components/schemas/ProductVariant"
    ProductVariantAttributeValue:
      type: object
      properties:
        value_id:
          type: integer
        tenant_id:
          type: integer
        variant_id:
          type: integer
        attribute_id:
          type: integer
        value:
          type: string
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        attribute:
          $ref: "#/components/schemas/ProductAttribute"
          nullable: true
    ProductVariantAttributeValueCreate:
      type: object
      required:
        - attribute_id
        - value
      properties:
        variant_id:
          type: integer
        attribute_id:
          type: integer
        value:
          type: string
    ProductVariantAttributeValueUpdate:
      type: object
      properties:
        variant_id:
          type: integer
        attribute_id:
          type: integer
        value:
          type: string
    ProductVariantAttributeValueListResponse:
      type: object
      properties:
        status:
          $ref: "#/components/schemas/Status"
        data:
          type: array
          items:
            $ref: "#/components/schemas/ProductVariantAttributeValue"
        meta:
          type: object
          properties:
            next_cursor:
              type: string
              nullable: true
            has_more:
              type: boolean
    ProductVariantAttributeValueResponse:
      type: object
      properties:
        status:
          $ref: "#/components/schemas/Status"
        data:
          $ref: "#/components/schemas/ProductVariantAttributeValue"
    Error:
      type: object
      properties:
        status:
          $ref: "#/components/schemas/Status"

    Status:
      type: object
      properties:
        code:
          type: integer
          description: HTTP status code
          example: 400
        message:
          type: string
          description: Error message
          example: Bad Request
        success:
          type: boolean
          description: Whether the request was successful
          example: false
        error_code:
          type: string
          description: Application-specific error code
          example: VALIDATION_ERROR
        path:
          type: string
          description: Request path
          example: /api/admin/v1/ecom/categories
        timestamp:
          type: string
          format: date-time
          description: Time when the error occurred
        details:
          type: array
          description: Detailed error information
          items:
            type: object
            properties:
              field:
                type: string
                description: Field with error (for validation errors)
                example: name
              message:
                type: string
                description: Detailed error message
                example: Name is required
