package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"wnapi/modules/product/dto/request"
	"wnapi/modules/product/service"
	// "wnapi/internal/pkg/auth"
	"wnapi/internal/pkg/response"
)

// ProductHandler handles HTTP requests for ecom products
type ProductHandler struct {
	productService service.ProductService
	jwtService     *// auth.JWTService
}

// NewProductHandler creates a new product handler instance
func NewProductHandler(productService service.ProductService, jwtService *// auth.JWTService) *ProductHandler {
	return &ProductHandler{
		productService: productService,
		jwtService:     jwtService,
	}
}

// CreateProduct handles the creation of a new product
func (h *ProductHandler) CreateProduct(c *gin.Context) {
	// Get tenant ID from context
	tenantIDStr, err := getTenantIDStringFromContext(c)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusUnauthorized, "Unauthorized", "UNAUTHORIZED", details)
		return
	}

	tenantID, err := strconv.Atoi(tenantIDStr)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid tenant ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid tenant ID", "INVALID_ID", details)
		return
	}

	// Parse request body
	var req request.CreateProductRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid request data", "INVALID_REQUEST", details)
		return
	}

	// Call service
	product, err := h.productService.CreateProduct(c.Request.Context(), tenantID, &req)
	if err != nil {
		handleError(c, err)
		return
	}

	// Return success response
	response.Success(c, http.StatusCreated, "Product created successfully", product)
}

// GetProduct handles retrieving a product by ID
func (h *ProductHandler) GetProduct(c *gin.Context) {
	// Get tenant ID from context
	tenantIDStr, err := getTenantIDStringFromContext(c)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusUnauthorized, "Unauthorized", "UNAUTHORIZED", details)
		return
	}

	tenantID, err := strconv.Atoi(tenantIDStr)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid tenant ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid tenant ID", "INVALID_ID", details)
		return
	}

	// Get product ID from URL
	idStr := c.Param("id")
	if idStr == "" {
		details := []interface{}{map[string]string{"message": "Product ID is required"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Missing product ID", "MISSING_ID", details)
		return
	}

	// Convert to int
	productID, err := strconv.Atoi(idStr)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid product ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid product ID", "INVALID_ID", details)
		return
	}

	// Call service
	product, err := h.productService.GetProduct(c.Request.Context(), tenantID, productID)
	if err != nil {
		handleError(c, err)
		return
	}

	// Return success response
	response.Success(c, http.StatusOK, "Product retrieved successfully", product)
}

// UpdateProduct handles updating an existing product
func (h *ProductHandler) UpdateProduct(c *gin.Context) {
	// Get tenant ID from context
	tenantIDStr, err := getTenantIDStringFromContext(c)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusUnauthorized, "Unauthorized", "UNAUTHORIZED", details)
		return
	}

	tenantID, err := strconv.Atoi(tenantIDStr)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid tenant ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid tenant ID", "INVALID_ID", details)
		return
	}

	// Get product ID from URL
	idStr := c.Param("id")
	if idStr == "" {
		details := []interface{}{map[string]string{"message": "Product ID is required"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Missing product ID", "MISSING_ID", details)
		return
	}

	// Convert to int
	productID, err := strconv.Atoi(idStr)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid product ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid product ID", "INVALID_ID", details)
		return
	}

	// Parse request body
	var req request.UpdateProductRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid request data", "INVALID_REQUEST", details)
		return
	}

	// Call service
	product, err := h.productService.UpdateProduct(c.Request.Context(), tenantID, productID, &req)
	if err != nil {
		handleError(c, err)
		return
	}

	// Return success response
	response.Success(c, http.StatusOK, "Product updated successfully", product)
}

// DeleteProduct handles deleting a product
func (h *ProductHandler) DeleteProduct(c *gin.Context) {
	// Get tenant ID from context
	tenantIDStr, err := getTenantIDStringFromContext(c)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusUnauthorized, "Unauthorized", "UNAUTHORIZED", details)
		return
	}

	tenantID, err := strconv.Atoi(tenantIDStr)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid tenant ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid tenant ID", "INVALID_ID", details)
		return
	}

	// Get product ID from URL
	idStr := c.Param("id")
	if idStr == "" {
		details := []interface{}{map[string]string{"message": "Product ID is required"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Missing product ID", "MISSING_ID", details)
		return
	}

	// Convert to int
	productID, err := strconv.Atoi(idStr)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid product ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid product ID", "INVALID_ID", details)
		return
	}

	// Call service
	err = h.productService.DeleteProduct(c.Request.Context(), tenantID, productID)
	if err != nil {
		handleError(c, err)
		return
	}

	// Return success response
	response.Success(c, http.StatusOK, "Product deleted successfully", nil)
}

// ListProducts handles listing products with pagination
func (h *ProductHandler) ListProducts(c *gin.Context) {
	// Get tenant ID from context
	tenantIDStr, err := getTenantIDStringFromContext(c)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusUnauthorized, "Unauthorized", "UNAUTHORIZED", details)
		return
	}

	tenantID, err := strconv.Atoi(tenantIDStr)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid tenant ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid tenant ID", "INVALID_ID", details)
		return
	}

	// Parse query parameters
	var req request.ListProductRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid query parameters", "INVALID_QUERY", details)
		return
	}

	// Call service
	result, err := h.productService.ListProducts(c.Request.Context(), tenantID, &req)
	if err != nil {
		handleError(c, err)
		return
	}

	// Return success response
	meta := map[string]interface{}{
		"next_cursor": result.Meta.NextCursor,
		"has_more":    result.Meta.HasMore,
	}
	response.SuccessWithMeta(c, http.StatusOK, "Products retrieved successfully", result.Products, meta)
}

// SearchProducts handles searching for products by keyword
func (h *ProductHandler) SearchProducts(c *gin.Context) {
	// Get tenant ID from context
	tenantIDStr, err := getTenantIDStringFromContext(c)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusUnauthorized, "Unauthorized", "UNAUTHORIZED", details)
		return
	}

	tenantID, err := strconv.Atoi(tenantIDStr)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid tenant ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid tenant ID", "INVALID_ID", details)
		return
	}

	// Parse query parameters
	var req request.SearchProductRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid query parameters", "INVALID_QUERY", details)
		return
	}

	// Call service
	result, err := h.productService.SearchProducts(c.Request.Context(), tenantID, &req)
	if err != nil {
		handleError(c, err)
		return
	}

	// Return success response
	meta := map[string]interface{}{
		"next_cursor": result.Meta.NextCursor,
		"has_more":    result.Meta.HasMore,
	}
	response.SuccessWithMeta(c, http.StatusOK, "Products searched successfully", result.Products, meta)
}

// GetAllProducts handles retrieving all products for a tenant
func (h *ProductHandler) GetAllProducts(c *gin.Context) {
	// Get tenant ID from context
	tenantIDStr, err := getTenantIDStringFromContext(c)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusUnauthorized, "Unauthorized", "UNAUTHORIZED", details)
		return
	}

	tenantID, err := strconv.Atoi(tenantIDStr)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid tenant ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid tenant ID", "INVALID_ID", details)
		return
	}

	// Call service
	products, err := h.productService.GetAllProducts(c.Request.Context(), tenantID)
	if err != nil {
		handleError(c, err)
		return
	}

	// Return success response
	response.Success(c, http.StatusOK, "All products retrieved successfully", products)
}

// GetProductCategories handles retrieving all categories that have products
func (h *ProductHandler) GetProductCategories(c *gin.Context) {
	// Get tenant ID from context
	tenantIDStr, err := getTenantIDStringFromContext(c)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusUnauthorized, "Unauthorized", "UNAUTHORIZED", details)
		return
	}

	tenantID, err := strconv.Atoi(tenantIDStr)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid tenant ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid tenant ID", "INVALID_ID", details)
		return
	}

	// Call service
	categories, err := h.productService.GetProductCategories(c.Request.Context(), tenantID)
	if err != nil {
		handleError(c, err)
		return
	}

	// Return success response
	response.Success(c, http.StatusOK, "Product categories retrieved successfully", categories)
}
