package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"wnapi/internal/pkg/response"
	"wnapi/modules/product/dto/request"
	"wnapi/modules/product/service"
	// TODO: Add auth import when available
	// // "wnapi/internal/pkg/auth"
)

// CategoryHandler handles HTTP requests for product categories
type CategoryHandler struct {
	categoryService service.CategoryService
	// TODO: Add JWT service when auth package is available
	// jwtService      *// auth.JWTService
}

// NewCategoryHandler creates a new category handler instance
func NewCategoryHandler(categoryService service.CategoryService /* jwtService *// auth.JWTService */) *CategoryHandler {
	return &CategoryHandler{
		categoryService: categoryService,
		// jwtService:      jwtService,
	}
}

// Create handles the creation of a new category
func (h *CategoryHandler) Create(c *gin.Context) {
	// Get tenant ID from context
	tenantIDStr, err := getTenantIDStringFromContext(c)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusUnauthorized, "Unauthorized", "UNAUTHORIZED", details)
		return
	}

	tenantID, err := strconv.Atoi(tenantIDStr)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid tenant ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid tenant ID", "INVALID_ID", details)
		return
	}

	// Parse request body
	var req request.CreateCategoryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid request data", "INVALID_REQUEST", details)
		return
	}

	// Call service
	category, err := h.categoryService.CreateCategory(c.Request.Context(), tenantID, req)
	if err != nil {
		handleError(c, err)
		return
	}

	// Return success response
	response.Success(c, http.StatusCreated, "Category created successfully", category)
}

// Get handles retrieval of a category by ID
func (h *CategoryHandler) Get(c *gin.Context) {
	// Get tenant ID from context
	tenantIDStr, err := getTenantIDStringFromContext(c)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusUnauthorized, "Unauthorized", "UNAUTHORIZED", details)
		return
	}

	tenantID, err := strconv.Atoi(tenantIDStr)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid tenant ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid tenant ID", "INVALID_ID", details)
		return
	}

	// Get category ID from URL
	idStr := c.Param("id")
	if idStr == "" {
		details := []interface{}{map[string]string{"message": "Category ID is required"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Missing category ID", "MISSING_ID", details)
		return
	}

	// Convert to int
	categoryID, err := strconv.Atoi(idStr)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid category ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid category ID", "INVALID_ID", details)
		return
	}

	// Call service
	category, err := h.categoryService.GetCategory(c.Request.Context(), tenantID, categoryID)
	if err != nil {
		handleError(c, err)
		return
	}

	// Return success response
	response.Success(c, http.StatusOK, "Category retrieved successfully", category)
}

// GetBySlug handles retrieval of a category by slug
func (h *CategoryHandler) GetBySlug(c *gin.Context) {
	// Get tenant ID from context
	tenantIDStr, err := getTenantIDStringFromContext(c)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusUnauthorized, "Unauthorized", "UNAUTHORIZED", details)
		return
	}

	tenantID, err := strconv.Atoi(tenantIDStr)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid tenant ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid tenant ID", "INVALID_ID", details)
		return
	}

	// Get slug from URL
	slug := c.Param("slug")
	if slug == "" {
		details := []interface{}{map[string]string{"message": "Category slug is required"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Missing category slug", "MISSING_SLUG", details)
		return
	}

	// Call service
	category, err := h.categoryService.GetCategoryBySlug(c.Request.Context(), tenantID, slug)
	if err != nil {
		handleError(c, err)
		return
	}

	// Return success response
	response.Success(c, http.StatusOK, "Category retrieved successfully", category)
}

// Update handles updating an existing category
func (h *CategoryHandler) Update(c *gin.Context) {
	// Get tenant ID from context
	tenantIDStr, err := getTenantIDStringFromContext(c)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusUnauthorized, "Unauthorized", "UNAUTHORIZED", details)
		return
	}

	tenantID, err := strconv.Atoi(tenantIDStr)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid tenant ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid tenant ID", "INVALID_ID", details)
		return
	}

	// Get category ID from URL
	idStr := c.Param("id")
	if idStr == "" {
		details := []interface{}{map[string]string{"message": "Category ID is required"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Missing category ID", "MISSING_ID", details)
		return
	}

	// Convert to int
	categoryID, err := strconv.Atoi(idStr)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid category ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid category ID", "INVALID_ID", details)
		return
	}

	// Parse request body
	var req request.UpdateCategoryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid request data", "INVALID_REQUEST", details)
		return
	}

	// Call service
	category, err := h.categoryService.UpdateCategory(c.Request.Context(), tenantID, categoryID, req)
	if err != nil {
		handleError(c, err)
		return
	}

	// Return success response
	response.Success(c, http.StatusOK, "Category updated successfully", category)
}

// Delete handles deletion of a category
func (h *CategoryHandler) Delete(c *gin.Context) {
	// Get tenant ID from context
	tenantIDStr, err := getTenantIDStringFromContext(c)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusUnauthorized, "Unauthorized", "UNAUTHORIZED", details)
		return
	}

	tenantID, err := strconv.Atoi(tenantIDStr)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid tenant ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid tenant ID", "INVALID_ID", details)
		return
	}

	// Get category ID from URL
	idStr := c.Param("id")
	if idStr == "" {
		details := []interface{}{map[string]string{"message": "Category ID is required"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Missing category ID", "MISSING_ID", details)
		return
	}

	// Convert to int
	categoryID, err := strconv.Atoi(idStr)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid category ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid category ID", "INVALID_ID", details)
		return
	}

	// Call service
	err = h.categoryService.DeleteCategory(c.Request.Context(), tenantID, categoryID)
	if err != nil {
		handleError(c, err)
		return
	}

	// Return success response
	response.Success(c, http.StatusOK, "Category deleted successfully", nil)
}

// List handles listing of categories with pagination
func (h *CategoryHandler) List(c *gin.Context) {
	// Get tenant ID from context
	tenantIDStr, err := getTenantIDStringFromContext(c)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusUnauthorized, "Unauthorized", "UNAUTHORIZED", details)
		return
	}

	tenantID, err := strconv.Atoi(tenantIDStr)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid tenant ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid tenant ID", "INVALID_ID", details)
		return
	}

	// Parse query parameters
	var req request.ListCategoryRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid query parameters", "INVALID_QUERY", details)
		return
	}

	// Call service
	result, err := h.categoryService.ListCategories(c.Request.Context(), tenantID, req)
	if err != nil {
		handleError(c, err)
		return
	}

	// Return success response
	meta := map[string]interface{}{
		"next_cursor": result.Meta.NextCursor,
		"has_more":    result.Meta.HasMore,
	}
	response.SuccessWithMeta(c, http.StatusOK, "Categories retrieved successfully", result.Categories, meta)
}

// GetTree handles retrieving the full category tree
func (h *CategoryHandler) GetTree(c *gin.Context) {
	// Get tenant ID from context
	tenantIDStr, err := getTenantIDStringFromContext(c)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusUnauthorized, "Unauthorized", "UNAUTHORIZED", details)
		return
	}

	tenantID, err := strconv.Atoi(tenantIDStr)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid tenant ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid tenant ID", "INVALID_ID", details)
		return
	}

	// Call service
	result, err := h.categoryService.GetCategoryTree(c.Request.Context(), tenantID)
	if err != nil {
		handleError(c, err)
		return
	}

	// Return success response with tree structure
	response.Success(c, http.StatusOK, "Category tree retrieved successfully", result.Categories)
}

// GetSubtree handles retrieving a category subtree
func (h *CategoryHandler) GetSubtree(c *gin.Context) {
	// Get tenant ID from context
	tenantIDStr, err := getTenantIDStringFromContext(c)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusUnauthorized, "Unauthorized", "UNAUTHORIZED", details)
		return
	}

	tenantID, err := strconv.Atoi(tenantIDStr)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid tenant ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid tenant ID", "INVALID_ID", details)
		return
	}

	// Get category ID from URL
	idStr := c.Param("id")
	if idStr == "" {
		details := []interface{}{map[string]string{"message": "Category ID is required"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Missing category ID", "MISSING_ID", details)
		return
	}

	// Convert to int
	categoryID, err := strconv.Atoi(idStr)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid category ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid category ID", "INVALID_ID", details)
		return
	}

	// Call service
	result, err := h.categoryService.GetCategorySubtree(c.Request.Context(), tenantID, categoryID)
	if err != nil {
		handleError(c, err)
		return
	}

	// Return success response
	response.Success(c, http.StatusOK, "Category subtree retrieved successfully", result.Categories)
}

// MoveNode handles moving a category node in the tree with specified parent and position
func (h *CategoryHandler) MoveNode(c *gin.Context) {
	// Get tenant ID from context
	tenantIDStr, err := getTenantIDStringFromContext(c)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusUnauthorized, "Unauthorized", "UNAUTHORIZED", details)
		return
	}

	tenantID, err := strconv.Atoi(tenantIDStr)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid tenant ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid tenant ID", "INVALID_ID", details)
		return
	}

	// Parse request body
	var req request.MoveNodeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid request data", "INVALID_REQUEST", details)
		return
	}

	// Call service - sử dụng service mới MoveNodeRelative
	err = h.categoryService.MoveCategoryNode(c.Request.Context(), tenantID, req)
	if err != nil {
		handleError(c, err)
		return
	}

	// Return success response
	response.Success(c, http.StatusOK, "Category moved successfully", nil)
}

// UpdatePosition handles updating the position of a category relative to a target category
func (h *CategoryHandler) UpdatePosition(c *gin.Context) {
	// Get tenant ID from context
	tenantIDStr, err := getTenantIDStringFromContext(c)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusUnauthorized, "Unauthorized", "UNAUTHORIZED", details)
		return
	}

	tenantID, err := strconv.Atoi(tenantIDStr)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid tenant ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid tenant ID", "INVALID_ID", details)
		return
	}

	// Parse request body
	var req request.UpdatePositionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid request data", "INVALID_REQUEST", details)
		return
	}

	// Call service - sử dụng service mới
	err = h.categoryService.MoveCategorySibling(c.Request.Context(), tenantID, req)
	if err != nil {
		handleError(c, err)
		return
	}

	// Return success response
	response.Success(c, http.StatusOK, "Category position updated successfully", nil)
}

// RebuildTree handles rebuilding the entire category tree structure
func (h *CategoryHandler) RebuildTree(c *gin.Context) {
	// Get tenant ID from context
	tenantIDStr, err := getTenantIDStringFromContext(c)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusUnauthorized, "Unauthorized", "UNAUTHORIZED", details)
		return
	}

	tenantID, err := strconv.Atoi(tenantIDStr)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid tenant ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid tenant ID", "INVALID_ID", details)
		return
	}

	// Call service to rebuild the tree
	err = h.categoryService.RebuildCategoryTree(c.Request.Context(), tenantID)
	if err != nil {
		handleError(c, err)
		return
	}

	// Return success response
	response.Success(c, http.StatusOK, "Category tree structure successfully rebuilt", nil)
}

// MoveNodeRoot handles moving a category node to become a root node at specified position
func (h *CategoryHandler) MoveNodeRoot(c *gin.Context) {
	// Get tenant ID from context
	tenantIDStr, err := getTenantIDStringFromContext(c)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusUnauthorized, "Unauthorized", "UNAUTHORIZED", details)
		return
	}

	tenantID, err := strconv.Atoi(tenantIDStr)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid tenant ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid tenant ID", "INVALID_ID", details)
		return
	}

	// Parse request body
	var req request.MoveNodeRootRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid request data", "INVALID_REQUEST", details)
		return
	}

	// Call service to move node to root
	err = h.categoryService.MoveNodeRoot(c.Request.Context(), tenantID, req)
	if err != nil {
		handleError(c, err)
		return
	}

	// Return success response
	response.Success(c, http.StatusOK, "Category moved to root successfully", nil)
}
