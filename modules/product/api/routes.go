package api

import (
	"fmt"

	"github.com/gin-gonic/gin"
	"github.com/jmoiron/sqlx"
	"gorm.io/gorm"

	"wnapi/internal/pkg/tracing"
	"wnapi/modules/product/api/handlers"
	"wnapi/modules/product/repository/mysql"
	"wnapi/modules/product/service"
	// TODO: Add imports when auth is available
	// "wnapi/internal/pkg/auth"
	// pkgMiddleware "wnapi/internal/middleware"
)

// Handler là đối tượng chính xử lý API cho module Product
type Handler struct {
	// Active handlers
	categoryHandler *handlers.CategoryHandler
	productHandler  *handlers.ProductHandler

	// TODO: Add remaining handlers when available
	// productAttributeGroupHandler        *handlers.ProductAttributeGroupHandler
	// productAttributeHandler             *handlers.ProductAttributeHandler
	// productAttributeOptionHandler       *handlers.ProductAttributeOptionHandler
	// productAttributeValueHandler        *handlers.ProductAttributeValueHandler
	// productVariantHandler               *handlers.ProductVariantHandler
	// productVariantAttributeValueHandler *handlers.ProductVariantAttributeValueHandler

	// Route tracking
	routes []string

	// Dependencies
	db     *sqlx.DB
	gormDB *gorm.DB
}

// NewHandler tạo một handler mới
func NewHandler(db *sqlx.DB, gormDB *gorm.DB) *Handler {
	// Initialize repositories
	categoryRepo := mysql.NewCategoryRepository(db)
	productRepo := mysql.NewProductRepository(db, gormDB)

	// Initialize services
	categoryService := service.NewCategoryService(categoryRepo)
	productService := service.NewProductService(productRepo, nil, nil, nil, nil, nil) // TODO: Add other dependencies

	// Initialize handlers
	categoryHandler := handlers.NewCategoryHandler(categoryService)
	productHandler := handlers.NewProductHandler(productService)

	return &Handler{
		categoryHandler: categoryHandler,
		productHandler:  productHandler,
		routes:          make([]string, 0),
		db:              db,
		gormDB:          gormDB,
	}
}

// RegisterRoutes đăng ký tất cả routes cho module Product
func (h *Handler) RegisterRoutes(router *gin.Engine) error {
	// API Group
	apiGroup := router.Group("/api/v1/product")

	// Thêm middleware tracing cho tất cả các route product
	apiGroup.Use(tracing.GinMiddleware("product"))

	// Lưu lại danh sách các route để hiển thị
	basePath := "/api/v1/product"

	// Health check endpoint
	apiGroup.GET("/health", h.healthCheck)
	h.routes = append(h.routes, fmt.Sprintf("GET %s/health", basePath))

	// Category routes
	categoryGroup := apiGroup.Group("/categories")
	{
		// CRUD operations
		categoryGroup.POST("", h.categoryHandler.Create)
		h.routes = append(h.routes, fmt.Sprintf("POST %s/categories", basePath))

		categoryGroup.GET("/:id", h.categoryHandler.Get)
		h.routes = append(h.routes, fmt.Sprintf("GET %s/categories/:id", basePath))

		categoryGroup.PUT("/:id", h.categoryHandler.Update)
		h.routes = append(h.routes, fmt.Sprintf("PUT %s/categories/:id", basePath))

		categoryGroup.DELETE("/:id", h.categoryHandler.Delete)
		h.routes = append(h.routes, fmt.Sprintf("DELETE %s/categories/:id", basePath))

		// List and search
		categoryGroup.GET("", h.categoryHandler.List)
		h.routes = append(h.routes, fmt.Sprintf("GET %s/categories", basePath))

		// Tree operations
		categoryGroup.GET("/tree", h.categoryHandler.GetTree)
		h.routes = append(h.routes, fmt.Sprintf("GET %s/categories/tree", basePath))

		categoryGroup.GET("/:id/subtree", h.categoryHandler.GetSubtree)
		h.routes = append(h.routes, fmt.Sprintf("GET %s/categories/:id/subtree", basePath))

		// Slug-based retrieval
		categoryGroup.GET("/slug/:slug", h.categoryHandler.GetBySlug)
		h.routes = append(h.routes, fmt.Sprintf("GET %s/categories/slug/:slug", basePath))

		// Tree management
		categoryGroup.POST("/move", h.categoryHandler.MoveNode)
		h.routes = append(h.routes, fmt.Sprintf("POST %s/categories/move", basePath))

		categoryGroup.POST("/position", h.categoryHandler.UpdatePosition)
		h.routes = append(h.routes, fmt.Sprintf("POST %s/categories/position", basePath))

		categoryGroup.POST("/rebuild", h.categoryHandler.RebuildTree)
		h.routes = append(h.routes, fmt.Sprintf("POST %s/categories/rebuild", basePath))

		categoryGroup.POST("/move-root", h.categoryHandler.MoveNodeRoot)
		h.routes = append(h.routes, fmt.Sprintf("POST %s/categories/move-root", basePath))
	}

	// Product routes
	productGroup := apiGroup.Group("/products")
	{
		// CRUD operations
		productGroup.POST("", h.productHandler.CreateProduct)
		h.routes = append(h.routes, fmt.Sprintf("POST %s/products", basePath))

		productGroup.GET("/:id", h.productHandler.GetProduct)
		h.routes = append(h.routes, fmt.Sprintf("GET %s/products/:id", basePath))

		productGroup.PUT("/:id", h.productHandler.UpdateProduct)
		h.routes = append(h.routes, fmt.Sprintf("PUT %s/products/:id", basePath))

		productGroup.DELETE("/:id", h.productHandler.DeleteProduct)
		h.routes = append(h.routes, fmt.Sprintf("DELETE %s/products/:id", basePath))

		// List and search
		productGroup.GET("", h.productHandler.ListProducts)
		h.routes = append(h.routes, fmt.Sprintf("GET %s/products", basePath))

		productGroup.GET("/search", h.productHandler.SearchProducts)
		h.routes = append(h.routes, fmt.Sprintf("GET %s/products/search", basePath))

		productGroup.GET("/all", h.productHandler.GetAllProducts)
		h.routes = append(h.routes, fmt.Sprintf("GET %s/products/all", basePath))

		productGroup.GET("/categories", h.productHandler.GetProductCategories)
		h.routes = append(h.routes, fmt.Sprintf("GET %s/products/categories", basePath))
	}

	return nil
}

// ListRoutes trả về danh sách tất cả các routes đã đăng ký
func (h *Handler) ListRoutes() []string {
	return h.routes
}

// PrintRoutes in ra console danh sách tất cả các routes đã đăng ký
func (h *Handler) PrintRoutes() {
	fmt.Println("=== PRODUCT MODULE ROUTES ===")
	for _, route := range h.routes {
		fmt.Println(route)
	}
	fmt.Println("=============================")
}

// printRoutes prints all registered routes (for consistency with other modules)
func printRoutes(routes []string) {
	fmt.Println("=== PRODUCT MODULE ROUTES ===")
	for _, route := range routes {
		fmt.Println(route)
	}
	fmt.Println("=============================")
}

// healthCheck là endpoint kiểm tra trạng thái hoạt động của module
func (h *Handler) healthCheck(c *gin.Context) {
	c.JSON(200, gin.H{
		"status":  "ok",
		"module":  "product",
		"message": "Product module is running",
	})
}

// RegisterRoutes registers all API routes for the product module (backward compatibility)
func RegisterRoutes(router *gin.Engine, db *sqlx.DB, gormDB *gorm.DB /* jwtService *auth.JWTService, permService pkgMiddleware.PermissionService */) {
	handler := NewHandler(db, gormDB)
	handler.RegisterRoutes(router)
}

// SetupRoutes sets up all the product module routes (for backward compatibility)
func SetupRoutes(router *gin.Engine, db *sqlx.DB, gormDB *gorm.DB /* jwtService *auth.JWTService, permService pkgMiddleware.PermissionService */) {
	RegisterRoutes(router, db, gormDB)
}
