package api

import (
	"fmt"

	"github.com/gin-gonic/gin"
	"github.com/jmoiron/sqlx"
	"gorm.io/gorm"

	"wnapi/internal/pkg/tracing"
	// TODO: Add imports when available
	// "wnapi/modules/product/api/handlers"
	// "wnapi/modules/product/api/middleware"
	// "wnapi/modules/product/repository/mysql"
	// "wnapi/modules/product/service"
	// "wnapi/internal/pkg/auth"
	// pkgMiddleware "wnapi/internal/middleware"
)

// Handler là đối tượng chính xử lý API cho module Product
type Handler struct {
	// TODO: Add handlers when available
	// categoryHandler                     *handlers.CategoryHandler
	// productHandler                      *handlers.ProductHandler
	// productAttributeGroupHandler        *handlers.ProductAttributeGroupHandler
	// productAttributeHandler             *handlers.ProductAttributeHandler
	// productAttributeOptionHandler       *handlers.ProductAttributeOptionHandler
	// productAttributeValueHandler        *handlers.ProductAttributeValueHandler
	// productVariantHandler               *handlers.ProductVariantHandler
	// productVariantAttributeValueHandler *handlers.ProductVariantAttributeValueHandler

	// Route tracking
	routes []string

	// Dependencies
	db     *sqlx.DB
	gormDB *gorm.DB
}

// NewHandler tạo một handler mới
func NewHandler(db *sqlx.DB, gormDB *gorm.DB) *Handler {
	// TODO: Initialize services and handlers properly in Task 02
	// For now, create placeholder handlers
	return &Handler{
		// categoryHandler:                     handlers.NewCategoryHandler(nil),
		// productHandler:                      handlers.NewProductHandler(nil),
		// productAttributeGroupHandler:        handlers.NewProductAttributeGroupHandler(nil),
		// productAttributeHandler:             handlers.NewProductAttributeHandler(nil),
		// productAttributeOptionHandler:       handlers.NewProductAttributeOptionHandler(nil),
		// productAttributeValueHandler:        handlers.NewProductAttributeValueHandler(nil),
		// productVariantHandler:               handlers.NewProductVariantHandler(nil),
		// productVariantAttributeValueHandler: handlers.NewProductVariantAttributeValueHandler(nil),
		routes: make([]string, 0),
		db:     db,
		gormDB: gormDB,
	}
}

// RegisterRoutes đăng ký tất cả routes cho module Product
func (h *Handler) RegisterRoutes(router *gin.Engine) error {
	// API Group
	apiGroup := router.Group("/api/v1/product")

	// Thêm middleware tracing cho tất cả các route product
	apiGroup.Use(tracing.GinMiddleware("product"))

	// Lưu lại danh sách các route để hiển thị
	basePath := "/api/v1/product"

	// Health check endpoint
	apiGroup.GET("/health", h.healthCheck)
	h.routes = append(h.routes, fmt.Sprintf("GET %s/health", basePath))

	// TODO: Implement full route registration in Task 02
	// For now, just register the health endpoint

	return nil
}

// ListRoutes trả về danh sách tất cả các routes đã đăng ký
func (h *Handler) ListRoutes() []string {
	return h.routes
}

// PrintRoutes in ra console danh sách tất cả các routes đã đăng ký
func (h *Handler) PrintRoutes() {
	fmt.Println("=== PRODUCT MODULE ROUTES ===")
	for _, route := range h.routes {
		fmt.Println(route)
	}
	fmt.Println("=============================")
}

// healthCheck là endpoint kiểm tra trạng thái hoạt động của module
func (h *Handler) healthCheck(c *gin.Context) {
	c.JSON(200, gin.H{
		"status":  "ok",
		"module":  "product",
		"message": "Product module is running",
	})
}

// RegisterRoutes registers all API routes for the product module (backward compatibility)
func RegisterRoutes(router *gin.Engine, db *sqlx.DB, gormDB *gorm.DB /* jwtService *auth.JWTService, permService pkgMiddleware.PermissionService */) {
	handler := NewHandler(db, gormDB)
	handler.RegisterRoutes(router)
}

// SetupRoutes sets up all the product module routes (for backward compatibility)
func SetupRoutes(router *gin.Engine, db *sqlx.DB, gormDB *gorm.DB /* jwtService *auth.JWTService, permService pkgMiddleware.PermissionService */) {
	RegisterRoutes(router, db, gormDB)
}
