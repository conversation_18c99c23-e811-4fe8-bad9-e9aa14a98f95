package mysql

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/jmoiron/sqlx"

	"wnapi/modules/product/dto/request"
	"wnapi/modules/product/models"
)

// Define error variants
var (
	ErrNotFound         = errors.New("not found")
	ErrConflict         = errors.New("conflict")
	ErrInvalidOperation = errors.New("invalid operation")
)

// CategoryRepository implements the repository.CategoryRepository interface with MySQL
type CategoryRepository struct {
	db *sqlx.DB
}

// NewCategoryRepository creates a new CategoryRepository instance
func NewCategoryRepository(db *sqlx.DB) *CategoryRepository {
	return &CategoryRepository{
		db: db,
	}
}

// Create adds a new category to the database
func (r *CategoryRepository) Create(ctx context.Context, category *models.Category) error {
	tx, err := r.db.BeginTxx(ctx, nil)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}

	defer func() {
		if err != nil {
			_ = tx.Rollback()
		}
	}()

	// Generate slug if not provided
	if category.Slug == "" {
		category.Slug = generateSlug(category.Name)
	}

	// Check if slug exists for this tenant
	var count int
	err = tx.GetContext(ctx, &count, "SELECT COUNT(*) FROM ecom_product_categories WHERE tenant_id = ? AND slug = ?",
		category.TenantID, category.Slug)
	if err != nil {
		return fmt.Errorf("failed to check slug uniqueness: %w", err)
	}

	if count > 0 {
		return ErrConflict
	}

	// Get parent's left, right, and depth values if parent exists
	var left, right, depth int
	if category.ParentID != nil {
		var parent models.Category
		err = tx.GetContext(ctx, &parent,
			"SELECT lft, rgt, depth FROM ecom_product_categories WHERE tenant_id = ? AND category_id = ?",
			category.TenantID, *category.ParentID)

		if err != nil {
			if err == sql.ErrNoRows {
				return ErrNotFound
			}
			return fmt.Errorf("failed to get parent category: %w", err)
		}

		// New node will be inserted as the last child
		left = parent.Right
		right = parent.Right + 1
		depth = parent.Depth + 1

		// Make space for the new node by updating existing nodes
		_, err = tx.ExecContext(ctx,
			"UPDATE ecom_product_categories SET rgt = rgt + 2 WHERE tenant_id = ? AND rgt >= ? ORDER BY rgt DESC",
			category.TenantID, parent.Right)
		if err != nil {
			return fmt.Errorf("failed to update right values: %w", err)
		}

		_, err = tx.ExecContext(ctx,
			"UPDATE ecom_product_categories SET lft = lft + 2 WHERE tenant_id = ? AND lft > ? ORDER BY lft DESC",
			category.TenantID, parent.Right)
		if err != nil {
			return fmt.Errorf("failed to update left values: %w", err)
		}
	} else {
		// Find the right-most value to place the new root node after
		var maxRight int
		err = tx.GetContext(ctx, &maxRight,
			"SELECT COALESCE(MAX(rgt), 0) FROM ecom_product_categories WHERE tenant_id = ?",
			category.TenantID)
		if err != nil {
			return fmt.Errorf("failed to get max right value: %w", err)
		}

		left = maxRight + 1
		right = maxRight + 2
		depth = 0
	}

	// Insert the new category
	query := `
		INSERT INTO ecom_product_categories (
			tenant_id, parent_id, name, slug, description, featured_image,
			lft, rgt, depth, position, is_active, is_featured,
			meta_title, meta_description, created_at, updated_at, created_by, updated_by
		) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`

	result, err := tx.ExecContext(ctx, query,
		category.TenantID, category.ParentID, category.Name, category.Slug,
		category.Description, category.FeaturedImage, left, right, depth,
		category.Position, category.IsActive, category.IsFeatured,
		category.MetaTitle, category.MetaDescription, time.Now(), time.Now(),
		category.CreatedBy, category.UpdatedBy)

	if err != nil {
		return fmt.Errorf("failed to insert category: %w", err)
	}

	// Get the inserted ID
	categoryID, err := result.LastInsertId()
	if err != nil {
		return fmt.Errorf("failed to get inserted id: %w", err)
	}

	category.CategoryID = uint(categoryID)
	category.Left = left
	category.Right = right
	category.Depth = depth

	// Commit transaction
	if err = tx.Commit(); err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	return nil
}

// GetByID retrieves a category by its ID
func (r *CategoryRepository) GetByID(ctx context.Context, tenantID, categoryID int) (*models.Category, error) {
	var category models.Category

	query := `
		SELECT * FROM ecom_product_categories
		WHERE tenant_id = ? AND category_id = ?
	`

	err := r.db.GetContext(ctx, &category, query, tenantID, categoryID)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, ErrNotFound
		}
		return nil, fmt.Errorf("failed to get category: %w", err)
	}

	return &category, nil
}

// GetBySlug retrieves a category by its slug
func (r *CategoryRepository) GetBySlug(ctx context.Context, tenantID int, slug string) (*models.Category, error) {
	var category models.Category

	query := `
		SELECT * FROM ecom_product_categories
		WHERE tenant_id = ? AND slug = ?
	`

	err := r.db.GetContext(ctx, &category, query, tenantID, slug)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, ErrNotFound
		}
		return nil, fmt.Errorf("failed to get category by slug: %w", err)
	}

	return &category, nil
}

// Update updates an existing category
func (r *CategoryRepository) Update(ctx context.Context, category *models.Category) error {
	tx, err := r.db.BeginTxx(ctx, nil)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}

	defer func() {
		if err != nil {
			_ = tx.Rollback()
		}
	}()

	// Check if the category exists
	var existingCategory models.Category
	err = tx.GetContext(ctx, &existingCategory,
		"SELECT * FROM ecom_product_categories WHERE tenant_id = ? AND category_id = ?",
		category.TenantID, category.CategoryID)

	if err != nil {
		if err == sql.ErrNoRows {
			return ErrNotFound
		}
		return fmt.Errorf("failed to check if category exists: %w", err)
	}

	// Check if slug changed and is unique
	if category.Slug != existingCategory.Slug {
		var count int
		err = tx.GetContext(ctx, &count,
			"SELECT COUNT(*) FROM ecom_product_categories WHERE tenant_id = ? AND slug = ? AND category_id != ?",
			category.TenantID, category.Slug, category.CategoryID)

		if err != nil {
			return fmt.Errorf("failed to check slug uniqueness: %w", err)
		}

		if count > 0 {
			return ErrConflict
		}
	}

	// Update category fields
	query := `
		UPDATE ecom_product_categories SET
			name = ?,
			slug = ?,
			description = ?,
			featured_image = ?,
			is_active = ?,
			is_featured = ?,
			meta_title = ?,
			meta_description = ?,
			updated_by = ?,
			updated_at = ?
		WHERE tenant_id = ? AND category_id = ?
	`

	_, err = tx.ExecContext(ctx, query,
		category.Name,
		category.Slug,
		category.Description,
		category.FeaturedImage,
		category.IsActive,
		category.IsFeatured,
		category.MetaTitle,
		category.MetaDescription,
		category.UpdatedBy,
		time.Now(),
		category.TenantID,
		category.CategoryID,
	)

	if err != nil {
		return fmt.Errorf("failed to update category: %w", err)
	}

	// If parent ID changed, we would update the tree structure here
	// But for simplicity, we'll just update the parent_id field
	if (category.ParentID == nil && existingCategory.ParentID != nil) ||
		(category.ParentID != nil && existingCategory.ParentID == nil) ||
		(category.ParentID != nil && existingCategory.ParentID != nil && *category.ParentID != *existingCategory.ParentID) {

		_, err = tx.ExecContext(ctx,
			"UPDATE ecom_product_categories SET parent_id = ? WHERE tenant_id = ? AND category_id = ?",
			category.ParentID, category.TenantID, category.CategoryID)

		if err != nil {
			return fmt.Errorf("failed to update parent id: %w", err)
		}
	}

	// Commit transaction
	if err = tx.Commit(); err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	return nil
}

// Delete removes a category and optionally its children
func (r *CategoryRepository) Delete(ctx context.Context, tenantID, categoryID int) error {
	tx, err := r.db.BeginTxx(ctx, nil)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}

	defer func() {
		if err != nil {
			_ = tx.Rollback()
		}
	}()

	// Check if the category exists and get its left and right values
	var category models.Category
	err = tx.GetContext(ctx, &category,
		"SELECT * FROM ecom_product_categories WHERE tenant_id = ? AND category_id = ?",
		tenantID, categoryID)

	if err != nil {
		if err == sql.ErrNoRows {
			return ErrNotFound
		}
		return fmt.Errorf("failed to check if category exists: %w", err)
	}

	// Delete the category and all its descendants
	_, err = tx.ExecContext(ctx,
		"DELETE FROM ecom_product_categories WHERE tenant_id = ? AND lft BETWEEN ? AND ?",
		tenantID, category.Left, category.Right)

	if err != nil {
		return fmt.Errorf("failed to delete categories: %w", err)
	}

	// Update left and right values for remaining nodes
	width := category.Right - category.Left + 1

	_, err = tx.ExecContext(ctx,
		"UPDATE ecom_product_categories SET lft = lft - ? WHERE tenant_id = ? AND lft > ?",
		width, tenantID, category.Right)

	if err != nil {
		return fmt.Errorf("failed to update left values: %w", err)
	}

	_, err = tx.ExecContext(ctx,
		"UPDATE ecom_product_categories SET rgt = rgt - ? WHERE tenant_id = ? AND rgt > ?",
		width, tenantID, category.Right)

	if err != nil {
		return fmt.Errorf("failed to update right values: %w", err)
	}

	// Commit transaction
	if err = tx.Commit(); err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	return nil
}

// GetTree retrieves the entire category tree for a tenant
func (r *CategoryRepository) GetTree(ctx context.Context, tenantID int) ([]*models.Category, error) {
	query := `
		SELECT * FROM ecom_product_categories
		WHERE tenant_id = ?
		ORDER BY lft
	`

	var categories []*models.Category
	err := r.db.SelectContext(ctx, &categories, query, tenantID)
	if err != nil {
		return nil, fmt.Errorf("failed to get category tree: %w", err)
	}

	// Xây dựng lại cấu trúc cây với node con được sắp xếp theo position
	tree := buildCategoryTree(categories)

	return tree, nil
}

// buildCategoryTree xây dựng cấu trúc cây từ danh sách phẳng các categories
// và sắp xếp các node con theo position
func buildCategoryTree(flatCategories []*models.Category) []*models.Category {
	if len(flatCategories) == 0 {
		return []*models.Category{}
	}

	// Tạo map để lưu trữ tham chiếu đến các đối tượng category theo ID
	categoryMap := make(map[int]*models.Category)
	for _, category := range flatCategories {
		categoryMap[int(category.CategoryID)] = category
		category.Children = []*models.Category{} // Khởi tạo slice Children rỗng
	}

	// Danh sách các node gốc
	var rootCategories []*models.Category

	// Xây dựng cấu trúc cây
	for _, category := range flatCategories {
		if category.ParentID == nil || *category.ParentID == 0 {
			// Đây là node gốc
			rootCategories = append(rootCategories, category)
		} else {
			// Đây là node con, thêm vào danh sách Children của node cha
			parentID := *category.ParentID
			if parent, exists := categoryMap[int(parentID)]; exists {
				parent.Children = append(parent.Children, category)
			} else {
				// Nếu không tìm thấy node cha, coi như node gốc
				rootCategories = append(rootCategories, category)
			}
		}
	}

	// Sắp xếp các node con theo position
	sortCategoriesByPosition(rootCategories)
	for _, category := range flatCategories {
		sortCategoriesByPosition(category.Children)
	}

	return rootCategories
}

// sortCategoriesByPosition sắp xếp danh sách các category theo position tăng dần
func sortCategoriesByPosition(categories []*models.Category) {
	sort.Slice(categories, func(i, j int) bool {
		// Sắp xếp theo position tăng dần
		if categories[i].Position != categories[j].Position {
			return categories[i].Position < categories[j].Position
		}
		// Nếu position bằng nhau, sắp xếp theo ID để đảm bảo tính ổn định
		return categories[i].CategoryID < categories[j].CategoryID
	})
}

// GetSubtree retrieves a category and all its descendants
func (r *CategoryRepository) GetSubtree(ctx context.Context, tenantID, categoryID int) ([]*models.Category, error) {
	// First get the category to find its left and right values
	var category models.Category
	err := r.db.GetContext(ctx, &category,
		"SELECT * FROM ecom_product_categories WHERE tenant_id = ? AND category_id = ?",
		tenantID, categoryID)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, ErrNotFound
		}
		return nil, fmt.Errorf("failed to get category: %w", err)
	}

	// Then get all categories between those values
	query := `
		SELECT * FROM ecom_product_categories
		WHERE tenant_id = ? AND lft >= ? AND rgt <= ?
		ORDER BY lft
	`

	var categories []*models.Category
	err = r.db.SelectContext(ctx, &categories, query, tenantID, category.Left, category.Right)
	if err != nil {
		return nil, fmt.Errorf("failed to get category subtree: %w", err)
	}

	return categories, nil
}

// GetAncestors retrieves all ancestors of a category
func (r *CategoryRepository) GetAncestors(ctx context.Context, tenantID, categoryID int) ([]*models.Category, error) {
	// First get the category to find its left and right values
	var category models.Category
	err := r.db.GetContext(ctx, &category,
		"SELECT * FROM ecom_product_categories WHERE tenant_id = ? AND category_id = ?",
		tenantID, categoryID)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, ErrNotFound
		}
		return nil, fmt.Errorf("failed to get category: %w", err)
	}

	// Then get all ancestors
	query := `
		SELECT * FROM ecom_product_categories
		WHERE tenant_id = ? AND lft < ? AND rgt > ?
		ORDER BY lft
	`

	var categories []*models.Category
	err = r.db.SelectContext(ctx, &categories, query, tenantID, category.Left, category.Right)
	if err != nil {
		return nil, fmt.Errorf("failed to get category ancestors: %w", err)
	}

	return categories, nil
}

// GetChildren retrieves direct children of a category
func (r *CategoryRepository) GetChildren(ctx context.Context, tenantID, categoryID int) ([]*models.Category, error) {
	query := `
		SELECT * FROM ecom_product_categories
		WHERE tenant_id = ? AND parent_id = ?
		ORDER BY position
	`

	var categories []*models.Category
	err := r.db.SelectContext(ctx, &categories, query, tenantID, categoryID)
	if err != nil {
		return nil, fmt.Errorf("failed to get category children: %w", err)
	}

	return categories, nil
}

// MoveNode di chuyển một category (và tất cả các con của nó) sang một vị trí mới
// Có thể di chuyển vào một category cha mới hoặc làm category gốc (root)
// Hàm này thay thế cho stored procedure `ecom_product_categories_move_node`
func (r *CategoryRepository) MoveNode(ctx context.Context, tenantID, nodeIDToMove int, newParentID *int, targetPosition int) error {
	// Bắt đầu transaction
	tx, err := r.db.BeginTxx(ctx, nil)
	if err != nil {
		return fmt.Errorf("không thể bắt đầu transaction: %w", err)
	}
	defer func() {
		if err != nil {
			_ = tx.Rollback()
		}
	}()

	// === Bước 1: Lấy thông tin và khóa node cần di chuyển ===
	var nodeLft, nodeRgt, nodeDepth int
	var currentParentID sql.NullInt64

	err = tx.QueryRowContext(ctx,
		"SELECT lft, rgt, depth, parent_id FROM ecom_product_categories WHERE category_id = ? AND tenant_id = ? FOR UPDATE",
		nodeIDToMove, tenantID).Scan(&nodeLft, &nodeRgt, &nodeDepth, &currentParentID)

	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return ErrNotFound
		}
		return fmt.Errorf("lỗi khi lấy thông tin node cần di chuyển: %w", err)
	}

	// Tính toán độ rộng của nhánh cần di chuyển
	nodeWidth := nodeRgt - nodeLft + 1

	// === Xác định và lấy thông tin vị trí đích ===
	// Biến isMovingToRoot để đánh dấu đang di chuyển ra làm root
	isMovingToRoot := newParentID == nil

	var newParentLft, newParentRgt, newParentDepth int

	if isMovingToRoot {
		// Đang di chuyển ra làm ROOT
		newParentDepth = -1 // Quy ước depth của "cha" của root là -1
	} else {
		// Đang di chuyển vào một node cha cụ thể
		err = tx.QueryRowContext(ctx,
			"SELECT lft, rgt, depth FROM ecom_product_categories WHERE category_id = ? AND tenant_id = ? FOR UPDATE",
			*newParentID, tenantID).Scan(&newParentLft, &newParentRgt, &newParentDepth)

		if err != nil {
			if errors.Is(err, sql.ErrNoRows) {
				return ErrNotFound
			}
			return fmt.Errorf("lỗi khi lấy thông tin node cha mới: %w", err)
		}

		// Kiểm tra tính hợp lệ: không thể di chuyển node vào con của chính nó
		if newParentLft >= nodeLft && newParentLft < nodeRgt {
			return ErrInvalidOperation
		}
	}

	// === Bước 3: Đánh dấu (tag) nhánh cần di chuyển ===
	// Tạm thời thay đổi giá trị lft, rgt của nhánh thành số âm
	_, err = tx.ExecContext(ctx,
		"UPDATE ecom_product_categories SET lft = 0 - lft, rgt = 0 - rgt WHERE lft >= ? AND rgt <= ? AND tenant_id = ?",
		nodeLft, nodeRgt, tenantID)
	if err != nil {
		return fmt.Errorf("lỗi khi đánh dấu nhánh cần di chuyển: %w", err)
	}

	// === Bước 4: Đóng khoảng trống ở vị trí cũ ===
	// Giảm giá trị lft của các node bên phải
	_, err = tx.ExecContext(ctx,
		"UPDATE ecom_product_categories SET lft = lft - ? WHERE lft > ? AND tenant_id = ?",
		nodeWidth, nodeRgt, tenantID)
	if err != nil {
		return fmt.Errorf("lỗi khi điều chỉnh lft sau khi xóa nhánh: %w", err)
	}

	// Giảm giá trị rgt của các node bên phải
	_, err = tx.ExecContext(ctx,
		"UPDATE ecom_product_categories SET rgt = rgt - ? WHERE rgt > ? AND tenant_id = ?",
		nodeWidth, nodeRgt, tenantID)
	if err != nil {
		return fmt.Errorf("lỗi khi điều chỉnh rgt sau khi xóa nhánh: %w", err)
	}

	// === Bước 5: Xác định điểm chèn (Insertion Point) ===
	var insertionPoint int // Sẽ lưu giá trị rgt của node cha mới

	if isMovingToRoot {
		// Nếu di chuyển ra root, điểm chèn là sau node có RGT lớn nhất
		err = tx.QueryRowContext(ctx,
			"SELECT COALESCE(MAX(rgt), 0) FROM ecom_product_categories WHERE tenant_id = ? AND rgt > 0",
			tenantID).Scan(&insertionPoint)
		if err != nil {
			return fmt.Errorf("lỗi khi xác định điểm chèn cho root: %w", err)
		}
	} else {
		// Nếu di chuyển vào node cha cụ thể, lấy lại RGT của cha mới
		err = tx.QueryRowContext(ctx,
			"SELECT rgt FROM ecom_product_categories WHERE category_id = ? AND tenant_id = ?",
			*newParentID, tenantID).Scan(&insertionPoint)
		if err != nil {
			return fmt.Errorf("lỗi khi lấy rgt của node cha mới: %w", err)
		}
	}

	// === Bước 6: Tạo khoảng trống ở vị trí mới ===
	if !isMovingToRoot {
		// Tăng giá trị lft của các node bên phải
		_, err = tx.ExecContext(ctx,
			"UPDATE ecom_product_categories SET lft = lft + ? WHERE lft >= ? AND tenant_id = ? AND lft > 0",
			nodeWidth, insertionPoint, tenantID)
		if err != nil {
			return fmt.Errorf("lỗi khi tạo khoảng trống cho lft: %w", err)
		}

		// Tăng giá trị rgt của các node bên phải
		_, err = tx.ExecContext(ctx,
			"UPDATE ecom_product_categories SET rgt = rgt + ? WHERE rgt >= ? AND tenant_id = ? AND rgt > 0",
			nodeWidth, insertionPoint, tenantID)
		if err != nil {
			return fmt.Errorf("lỗi khi tạo khoảng trống cho rgt: %w", err)
		}
	}

	// === Bước 7: Di chuyển nhánh đã tag vào vị trí mới ===
	// Tính toán độ lệch (offset) cho lft/rgt và depth
	var moveOffset, depthOffset int

	if isMovingToRoot {
		// LFT mới = max_rgt + 1, Offset = LFT mới - LFT cũ
		moveOffset = (insertionPoint + 1) - nodeLft
		// Depth mới của root là 0, Offset = Depth mới - Depth cũ
		depthOffset = 0 - nodeDepth
	} else {
		// LFT mới = RGT của cha, Offset = LFT mới - LFT cũ
		moveOffset = insertionPoint - nodeLft
		// Depth mới = Depth cha + 1, Offset = Depth mới - Depth cũ
		depthOffset = (newParentDepth + 1) - nodeDepth
	}

	// Cập nhật lft, rgt, depth cho tất cả các node trong nhánh đã tag
	_, err = tx.ExecContext(ctx,
		`UPDATE ecom_product_categories
		SET lft = (lft * -1) + ?,
			rgt = (rgt * -1) + ?,
			depth = depth + ?
		WHERE tenant_id = ? AND lft < 0`,
		moveOffset, moveOffset, depthOffset, tenantID)
	if err != nil {
		return fmt.Errorf("lỗi khi di chuyển nhánh đã tag: %w", err)
	}

	// === Bước 8: Cập nhật parent_id và position ===
	// 8.1: Tìm vị trí lớn nhất hiện tại của các node anh em mới
	var maxSiblingPosition int = -1
	if isMovingToRoot {
		err = tx.QueryRowContext(ctx,
			`SELECT COALESCE(MAX(position), -1) FROM ecom_product_categories
			WHERE parent_id IS NULL AND tenant_id = ? AND category_id != ?`,
			tenantID, nodeIDToMove).Scan(&maxSiblingPosition)
	} else {
		err = tx.QueryRowContext(ctx,
			`SELECT COALESCE(MAX(position), -1) FROM ecom_product_categories
			WHERE parent_id = ? AND tenant_id = ? AND category_id != ?`,
			*newParentID, tenantID, nodeIDToMove).Scan(&maxSiblingPosition)
	}
	if err != nil {
		return fmt.Errorf("lỗi khi tìm vị trí lớn nhất của các node anh em: %w", err)
	}

	// Chuẩn hóa vị trí đích
	actualTargetPosition := targetPosition
	if actualTargetPosition < 0 {
		actualTargetPosition = 0
	} else if actualTargetPosition > maxSiblingPosition+1 {
		actualTargetPosition = maxSiblingPosition + 1
	}

	// 8.2: Dịch chuyển vị trí của các node anh em hiện có
	if isMovingToRoot {
		_, err = tx.ExecContext(ctx,
			`UPDATE ecom_product_categories SET position = position + 1
			WHERE parent_id IS NULL AND tenant_id = ?
			AND position >= ? AND category_id != ?`,
			tenantID, actualTargetPosition, nodeIDToMove)
	} else {
		_, err = tx.ExecContext(ctx,
			`UPDATE ecom_product_categories SET position = position + 1
			WHERE parent_id = ? AND tenant_id = ?
			AND position >= ? AND category_id != ?`,
			*newParentID, tenantID, actualTargetPosition, nodeIDToMove)
	}
	if err != nil {
		return fmt.Errorf("lỗi khi dịch chuyển vị trí các node anh em: %w", err)
	}

	// 8.3: Cập nhật parent_id và position cho node được di chuyển
	var parentIDArg interface{} = nil
	if !isMovingToRoot {
		parentIDArg = *newParentID
	}

	_, err = tx.ExecContext(ctx,
		"UPDATE ecom_product_categories SET parent_id = ?, position = ? WHERE category_id = ? AND tenant_id = ?",
		parentIDArg, actualTargetPosition, nodeIDToMove, tenantID)
	if err != nil {
		return fmt.Errorf("lỗi khi cập nhật parent_id và position: %w", err)
	}

	// Commit transaction
	if err = tx.Commit(); err != nil {
		return fmt.Errorf("lỗi khi commit transaction: %w", err)
	}

	return nil
}

// ChangeParent changes a category's parent
func (r *CategoryRepository) ChangeParent(ctx context.Context, tenantID, categoryID, newParentID int) error {
	var category models.Category
	category.CategoryID = uint(categoryID)
	category.TenantID = uint(tenantID)
	uintParentID := uint(newParentID)
	category.ParentID = &uintParentID

	return r.Update(ctx, &category)
}

// List retrieves a paginated list of categories using cursor pagination
func (r *CategoryRepository) List(ctx context.Context, tenantID int, req request.ListCategoryRequest) ([]*models.Category, string, bool, error) {
	// Base query
	baseQuery := "FROM ecom_product_categories WHERE tenant_id = ?"
	args := []interface{}{tenantID}

	// Add filters
	if req.ParentID != nil {
		baseQuery += " AND parent_id = ?"
		args = append(args, *req.ParentID)
	}

	if req.OnlyActive {
		baseQuery += " AND is_active = 1"
	}

	// Cursor-based pagination
	limitQuery := ""
	if req.Limit <= 0 {
		req.Limit = 50 // Default limit
	}

	sortOrder := "ASC"
	if req.Cursor != "" {
		// For simplicity, we'll just use the category_id for cursor pagination
		categoryID, err := strconv.Atoi(req.Cursor)
		if err != nil {
			return nil, "", false, fmt.Errorf("invalid cursor: %w", err)
		}

		if categoryID > 0 {
			baseQuery += " AND category_id > ?"
			args = append(args, categoryID)
		}
	}

	// Add ordering and limit
	orderBy := fmt.Sprintf(" ORDER BY category_id %s", sortOrder)
	limitQuery = fmt.Sprintf(" LIMIT %d", req.Limit+1) // Get one extra to check if there are more

	// Create final query
	dataQuery := fmt.Sprintf("SELECT * %s%s%s", baseQuery, orderBy, limitQuery)

	// Get the data
	var categories []*models.Category
	err := r.db.SelectContext(ctx, &categories, dataQuery, args...)
	if err != nil {
		return nil, "", false, fmt.Errorf("failed to list categories: %w", err)
	}

	// Check if there are more results
	hasMore := false
	if len(categories) > req.Limit {
		hasMore = true
		categories = categories[:req.Limit]
	}

	// Generate the next cursor
	var nextCursor string
	if hasMore && len(categories) > 0 {
		lastCategory := categories[len(categories)-1]
		nextCursor = fmt.Sprintf("%d", lastCategory.CategoryID)
	}

	return categories, nextCursor, hasMore, nil
}

// MoveNodeRelativeProcedure moves a category relative to another category (as child, before, or after)
func (r *CategoryRepository) MoveNodeRelativeProcedure(ctx context.Context, tenantID, categoryID, targetID int, mode string) error {
	// Start transaction
	tx, err := r.db.BeginTxx(ctx, nil)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer func() {
		if err != nil {
			_ = tx.Rollback()
		}
	}()

	// Get the target category
	var targetCategory models.Category
	err = tx.GetContext(ctx, &targetCategory,
		"SELECT * FROM ecom_product_categories WHERE category_id = ? AND tenant_id = ? FOR UPDATE",
		targetID, tenantID)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return ErrNotFound
		}
		return fmt.Errorf("failed to get target category: %w", err)
	}

	// Handle different modes
	switch mode {
	case "child":
		// Move as a child of the target
		// Convert targetID to a pointer for MoveNode
		targetIDPtr := targetID
		err = r.MoveNode(ctx, tenantID, categoryID, &targetIDPtr, 0) // Position 0 means first child
	case "before":
		// Move before the target (same parent, position before target)
		var parentID *int
		if targetCategory.ParentID != nil {
			parentIDInt := int(*targetCategory.ParentID)
			parentID = &parentIDInt
		}
		// Get target's position
		var targetPosition int
		err = tx.GetContext(ctx, &targetPosition,
			"SELECT position FROM ecom_product_categories WHERE category_id = ? AND tenant_id = ?",
			targetID, tenantID)
		if err != nil {
			return fmt.Errorf("failed to get target position: %w", err)
		}
		// Move to same parent but at target's position
		err = r.MoveNode(ctx, tenantID, categoryID, parentID, targetPosition)
	case "after":
		// Move after the target (same parent, position after target)
		var parentID *int
		if targetCategory.ParentID != nil {
			parentIDInt := int(*targetCategory.ParentID)
			parentID = &parentIDInt
		}
		// Get target's position
		var targetPosition int
		err = tx.GetContext(ctx, &targetPosition,
			"SELECT position FROM ecom_product_categories WHERE category_id = ? AND tenant_id = ?",
			targetID, tenantID)
		if err != nil {
			return fmt.Errorf("failed to get target position: %w", err)
		}
		// Move to same parent but at position after target
		err = r.MoveNode(ctx, tenantID, categoryID, parentID, targetPosition+1)
	default:
		return fmt.Errorf("invalid mode: %s", mode)
	}

	if err != nil {
		return err
	}

	// Commit transaction
	if err = tx.Commit(); err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	return nil
}

// GetCategoriesWithPostCount retrieves categories with post count
func (r *CategoryRepository) GetCategoriesWithPostCount(ctx context.Context, tenantID int) ([]*models.Category, error) {
	query := `
		SELECT c.*, COUNT(pc.post_id) as post_count
		FROM ecom_product_categories c
		LEFT JOIN blog_post_categories pc ON c.category_id = pc.category_id
		WHERE c.tenant_id = ?
		GROUP BY c.category_id
		ORDER BY c.lft
	`

	var categories []*models.Category
	err := r.db.SelectContext(ctx, &categories, query, tenantID)
	if err != nil {
		return nil, fmt.Errorf("failed to get categories with post count: %w", err)
	}

	return categories, nil
}

// Helper functions

// convertUintPtrToIntPtr converts a *uint to *int
func convertUintPtrToIntPtr(uintPtr *uint) *int {
	if uintPtr == nil {
		return nil
	}
	intVal := int(*uintPtr)
	return &intVal
}

func generateSlug(name string) string {
	// Convert to lowercase
	slug := strings.ToLower(name)

	// Replace spaces with hyphens
	slug = strings.ReplaceAll(slug, " ", "-")

	// Remove special characters
	slug = strings.Map(func(r rune) rune {
		if (r >= 'a' && r <= 'z') || (r >= '0' && r <= '9') || r == '-' {
			return r
		}
		return -1
	}, slug)

	// Remove consecutive hyphens
	for strings.Contains(slug, "--") {
		slug = strings.ReplaceAll(slug, "--", "-")
	}

	// Trim hyphens from ends
	slug = strings.Trim(slug, "-")

	return slug
}

// RebuildTree tính toán lại lft, rgt, depth và position cho tất cả các category của một tenant.
// Nó đọc toàn bộ cây vào bộ nhớ, xây dựng lại quan hệ cha-con,
// rồi duyệt cây để gán lại các giá trị nested set và position.
func (r *CategoryRepository) RebuildTree(ctx context.Context, tenantID int) (err error) { // Sử dụng named return để defer dễ xử lý lỗi
	tx, err := r.db.BeginTxx(ctx, nil)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	// Đảm bảo rollback nếu có lỗi xảy ra trong quá trình thực thi
	defer func() {
		if p := recover(); p != nil { // Bắt panic nếu có
			_ = tx.Rollback()
			panic(p) // Re-panic sau khi rollback
		} else if err != nil { // Rollback nếu có lỗi được return
			_ = tx.Rollback()
		} else { // Commit nếu không có lỗi
			err = tx.Commit()
			if err != nil {
				err = fmt.Errorf("failed to commit transaction: %w", err)
			}
		}
	}()

	// 1. Lấy tất cả categories cho tenant.
	// QUAN TRỌNG: Thứ tự sắp xếp ở đây ảnh hưởng đến thứ tự Children ban đầu.
	// Sắp xếp theo parent_id, sau đó theo position hiện có (để cố gắng giữ thứ tự cũ nếu hợp lệ),
	// cuối cùng theo ID để đảm bảo tính nhất quán nếu position trùng lặp.
	// Nếu muốn reset position hoàn toàn theo tên chẳng hạn, đổi thành: ORDER BY COALESCE(parent_id, 0), name
	query := `
		SELECT * FROM ecom_product_categories
		WHERE tenant_id = ?
		ORDER BY COALESCE(parent_id, 0), position, category_id
	`
	var categories []*models.Category
	if err = tx.SelectContext(ctx, &categories, query, tenantID); err != nil {
		return fmt.Errorf("failed to get categories: %w", err)
	}

	if len(categories) == 0 {
		// Không có gì để rebuild nếu không có category nào
		return nil
	}

	// 2. Xây dựng map và liên kết cha-con trong bộ nhớ
	categoryMap := make(map[int]*models.Category)
	var rootCategories []*models.Category // Slice chứa các category gốc (parent_id IS NULL)

	for i := range categories {
		cat := categories[i] // Lấy con trỏ đến category trong slice gốc
		categoryMap[int(cat.CategoryID)] = cat
		cat.Children = []*models.Category{} // Khởi tạo slice Children rỗng
	}

	// Liên kết con với cha và xác định gốc
	for _, cat := range categories {
		if cat.ParentID != nil && *cat.ParentID != 0 { // Kiểm tra ParentID hợp lệ
			if parent, exists := categoryMap[int(*cat.ParentID)]; exists {
				parent.Children = append(parent.Children, cat)
			} else {
				// Xử lý trường hợp Orphan (cha không tồn tại trong danh sách):
				// Coi nó như một node gốc. Dữ liệu này có thể cần được làm sạch sau.
				fmt.Printf("Warning: Category %d (Name: %s) has non-existent parent %d. Treating as root.\n",
					cat.CategoryID, cat.Name, *cat.ParentID)
				cat.ParentID = nil // Đặt lại ParentID thành nil
				rootCategories = append(rootCategories, cat)
			}
		} else {
			// Là category gốc
			rootCategories = append(rootCategories, cat)
		}
	}

	// 3. Sắp xếp các node gốc theo position cũ (hoặc tiêu chí khác nếu cần)
	// Điều này quan trọng nếu bạn muốn các cây gốc có thứ tự nhất quán.
	sort.SliceStable(rootCategories, func(i, j int) bool {
		if rootCategories[i].Position != rootCategories[j].Position {
			return rootCategories[i].Position < rootCategories[j].Position
		}
		return rootCategories[i].CategoryID < rootCategories[j].CategoryID // Fallback để ổn định
	})

	// 4. Rebuild cây (tính lft, rgt, depth, position) bắt đầu từ các gốc
	counter := 1 // Bộ đếm cho lft/rgt
	for i, root := range rootCategories {
		root.Position = i // Gán lại position cho các node gốc (0, 1, 2...)
		counter = r.rebuildNodeRecursive(root, 0, counter)
	}

	// 5. Cập nhật tất cả categories trong database với giá trị mới
	// Sử dụng một câu lệnh UPDATE duy nhất trong vòng lặp không phải là tối ưu nhất
	// với số lượng lớn, nhưng đơn giản để minh họa.
	// Cân nhắc dùng bulk update hoặc prepared statement nếu hiệu năng là vấn đề.
	updateQuery := `
		UPDATE ecom_product_categories
		SET lft = ?, rgt = ?, depth = ?, position = ?
		WHERE tenant_id = ? AND category_id = ?
	`
	for _, category := range categories {
		// Đảm bảo rằng các giá trị lft, rgt được tính toán hợp lệ
		// (Ví dụ: không có node nào có lft=0 hoặc rgt=0 trừ khi cây rỗng)
		if category.Left == 0 || category.Right == 0 {
			return fmt.Errorf("internal error: category %d (Name: %s) has invalid LFT/RGT value after rebuild (%d, %d)",
				category.CategoryID, category.Name, category.Left, category.Right)
		}

		_, execErr := tx.ExecContext(ctx, updateQuery,
			category.Left,
			category.Right,
			category.Depth,
			category.Position, // Thêm position vào update
			tenantID,
			category.CategoryID,
		)
		if execErr != nil {
			// Lỗi được defer func xử lý rollback
			return fmt.Errorf("failed to update category %d (Name: %s): %w", category.CategoryID, category.Name, execErr)
		}
	}

	// Transaction sẽ được commit hoặc rollback bởi defer func
	return nil
}

// rebuildNodeRecursive duyệt cây con theo chiều sâu (depth-first),
// tính toán và gán giá trị lft, rgt, depth, và position cho các node con.
func (r *CategoryRepository) rebuildNodeRecursive(node *models.Category, depth int, counter int) int {
	node.Depth = depth
	node.Left = counter
	counter++ // Tăng bộ đếm cho lft của node hiện tại

	// Sắp xếp các con trước khi duyệt đệ quy để đảm bảo thứ tự position đúng
	// Dựa vào thứ tự ban đầu từ DB hoặc tiêu chí khác nếu cần reset hoàn toàn.
	// Slice node.Children đã được sắp xếp theo ORDER BY của query ban đầu.
	// Nếu muốn đảm bảo sắp xếp lại ở đây (ví dụ theo tên):
	// sort.Slice(node.Children, func(i, j int) bool {
	//     return node.Children[i].Name < node.Children[j].Name
	// })

	// Duyệt qua các con và gán position, sau đó gọi đệ quy
	for i, child := range node.Children {
		child.Position = i                                        // Gán position mới cho node con (0, 1, 2...)
		counter = r.rebuildNodeRecursive(child, depth+1, counter) // Gọi đệ quy và cập nhật bộ đếm
	}

	// Sau khi duyệt xong tất cả con cháu, gán rgt cho node hiện tại
	node.Right = counter
	counter++ // Tăng bộ đếm cho rgt của node hiện tại

	return counter // Trả về bộ đếm đã cập nhật
}

// UpdatePosition cập nhật vị trí của category trong cùng một cấp
// Hàm này thay thế cho stored procedure `ecom_product_categories_update_position`
func (r *CategoryRepository) UpdatePosition(ctx context.Context, tenantID, categoryID int, newPosition int) error {
	// Bắt đầu transaction
	tx, err := r.db.BeginTxx(ctx, nil)
	if err != nil {
		return fmt.Errorf("không thể bắt đầu transaction: %w", err)
	}
	defer func() {
		if err != nil {
			_ = tx.Rollback()
		}
	}()

	// Lấy thông tin category và khóa
	var parentID sql.NullInt64
	var currentPosition int
	err = tx.QueryRowContext(ctx,
		"SELECT parent_id, position FROM ecom_product_categories WHERE category_id = ? AND tenant_id = ? FOR UPDATE",
		categoryID, tenantID).Scan(&parentID, &currentPosition)

	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return ErrNotFound
		}
		return fmt.Errorf("lỗi khi lấy thông tin category: %w", err)
	}

	// Xác định vị trí tối đa hiện tại của các categories anh em
	var maxPosition int = -1

	// Query với điều kiện parent_id <=> ? để xử lý NULL an toàn
	// (<=> là toán tử so sánh NULL-safe trong MySQL)
	// Trong Go, chúng ta cần kiểm tra NULL riêng
	var query string
	var args []interface{}

	if parentID.Valid {
		query = `SELECT COALESCE(MAX(position), -1) FROM ecom_product_categories
				WHERE parent_id = ? AND tenant_id = ? AND category_id != ?`
		args = []interface{}{parentID.Int64, tenantID, categoryID}
	} else {
		query = `SELECT COALESCE(MAX(position), -1) FROM ecom_product_categories
				WHERE parent_id IS NULL AND tenant_id = ? AND category_id != ?`
		args = []interface{}{tenantID, categoryID}
	}

	err = tx.QueryRowContext(ctx, query, args...).Scan(&maxPosition)
	if err != nil {
		return fmt.Errorf("lỗi khi xác định vị trí tối đa: %w", err)
	}

	// Chuẩn hóa vị trí mới
	actualNewPosition := newPosition
	if actualNewPosition < 0 {
		actualNewPosition = 0
	} else if actualNewPosition > maxPosition+1 {
		actualNewPosition = maxPosition + 1
	}

	// Nếu vị trí mới giống vị trí cũ thì không cần làm gì
	if actualNewPosition == currentPosition {
		return tx.Commit() // Commit transaction trống
	}

	// Dịch chuyển các node anh em
	if actualNewPosition < currentPosition {
		// Node di chuyển LÊN (vị trí giảm)
		if parentID.Valid {
			_, err = tx.ExecContext(ctx,
				`UPDATE ecom_product_categories SET position = position + 1
				WHERE parent_id = ? AND tenant_id = ? AND position >= ? AND position < ?`,
				parentID.Int64, tenantID, actualNewPosition, currentPosition)
		} else {
			_, err = tx.ExecContext(ctx,
				`UPDATE ecom_product_categories SET position = position + 1
				WHERE parent_id IS NULL AND tenant_id = ? AND position >= ? AND position < ?`,
				tenantID, actualNewPosition, currentPosition)
		}
	} else {
		// Node di chuyển XUỐNG (vị trí tăng)
		if parentID.Valid {
			_, err = tx.ExecContext(ctx,
				`UPDATE ecom_product_categories SET position = position - 1
				WHERE parent_id = ? AND tenant_id = ? AND position > ? AND position <= ?`,
				parentID.Int64, tenantID, currentPosition, actualNewPosition)
		} else {
			_, err = tx.ExecContext(ctx,
				`UPDATE ecom_product_categories SET position = position - 1
				WHERE parent_id IS NULL AND tenant_id = ? AND position > ? AND position <= ?`,
				tenantID, currentPosition, actualNewPosition)
		}
	}

	if err != nil {
		return fmt.Errorf("lỗi khi dịch chuyển các category anh em: %w", err)
	}

	// Cập nhật vị trí mới cho category
	_, err = tx.ExecContext(ctx,
		"UPDATE ecom_product_categories SET position = ? WHERE category_id = ? AND tenant_id = ?",
		actualNewPosition, categoryID, tenantID)

	if err != nil {
		return fmt.Errorf("lỗi khi cập nhật vị trí mới cho category: %w", err)
	}

	// Commit transaction
	if err = tx.Commit(); err != nil {
		return fmt.Errorf("lỗi khi commit transaction: %w", err)
	}

	return nil
}

// MoveNodeSibling di chuyển một category để đứng trước một category khác
// (Di chuyển node để trở thành anh em đứng liền trước target node)
// Hàm này thay thế cho stored procedure `ecom_product_categories_move_node_sibling`
func (r *CategoryRepository) MoveNodeSibling(ctx context.Context, tenantID, sourceCategoryID, targetCategoryID int) error {
	// Bắt đầu transaction
	tx, err := r.db.BeginTxx(ctx, nil)
	if err != nil {
		return fmt.Errorf("không thể bắt đầu transaction: %w", err)
	}
	defer func() {
		if err != nil {
			_ = tx.Rollback()
		}
	}()

	// Kiểm tra và lấy thông tin node nguồn
	var sourceParentID sql.NullInt64
	var sourcePosition int
	err = tx.QueryRowContext(ctx,
		"SELECT parent_id, position FROM ecom_product_categories WHERE category_id = ? AND tenant_id = ? FOR UPDATE",
		sourceCategoryID, tenantID).Scan(&sourceParentID, &sourcePosition)

	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return ErrNotFound
		}
		return fmt.Errorf("lỗi khi lấy thông tin source category: %w", err)
	}

	// Kiểm tra và lấy thông tin node đích
	var targetParentID sql.NullInt64
	var targetPosition int
	err = tx.QueryRowContext(ctx,
		"SELECT parent_id, position FROM ecom_product_categories WHERE category_id = ? AND tenant_id = ? FOR UPDATE",
		targetCategoryID, tenantID).Scan(&targetParentID, &targetPosition)

	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return ErrNotFound
		}
		return fmt.Errorf("lỗi khi lấy thông tin target category: %w", err)
	}

	// Kiểm tra các điều kiện hợp lệ
	// Không thể di chuyển một node tương đối với chính nó
	if sourceCategoryID == targetCategoryID {
		return ErrInvalidOperation
	}

	// Hai node phải là anh em (cùng cha, hoặc cùng là root)
	if (sourceParentID.Valid != targetParentID.Valid) ||
		(sourceParentID.Valid && sourceParentID.Int64 != targetParentID.Int64) {
		return ErrInvalidOperation
	}

	// Nếu node nguồn đã ở ngay trước node đích thì không cần làm gì
	if sourcePosition == targetPosition-1 {
		return tx.Commit() // Không có thay đổi, commit transaction rỗng
	}

	// Dịch chuyển các node anh em để tạo khoảng trống
	var updateQuery string
	var queryArgs []interface{}

	if sourceParentID.Valid {
		updateQuery = `UPDATE ecom_product_categories SET position = position + 1
				WHERE parent_id = ? AND tenant_id = ? AND position >= ?
				AND category_id != ?`
		queryArgs = []interface{}{sourceParentID.Int64, tenantID, targetPosition, sourceCategoryID}
	} else {
		updateQuery = `UPDATE ecom_product_categories SET position = position + 1
				WHERE parent_id IS NULL AND tenant_id = ? AND position >= ?
				AND category_id != ?`
		queryArgs = []interface{}{tenantID, targetPosition, sourceCategoryID}
	}

	_, err = tx.ExecContext(ctx, updateQuery, queryArgs...)
	if err != nil {
		return fmt.Errorf("lỗi khi cập nhật vị trí các category anh em: %w", err)
	}

	// Cập nhật vị trí cho node nguồn bằng vị trí *cũ* của node đích
	_, err = tx.ExecContext(ctx,
		"UPDATE ecom_product_categories SET position = ? WHERE category_id = ? AND tenant_id = ?",
		targetPosition, sourceCategoryID, tenantID)
	if err != nil {
		return fmt.Errorf("lỗi khi cập nhật vị trí cho source category: %w", err)
	}

	// Chuẩn hóa lại các position để tránh lỗ hổng
	// Lấy tất cả các anh em theo thứ tự position
	var rows *sql.Rows
	if sourceParentID.Valid {
		rows, err = tx.QueryContext(ctx,
			"SELECT category_id FROM ecom_product_categories WHERE parent_id = ? AND tenant_id = ? ORDER BY position",
			sourceParentID.Int64, tenantID)
	} else {
		rows, err = tx.QueryContext(ctx,
			"SELECT category_id FROM ecom_product_categories WHERE parent_id IS NULL AND tenant_id = ? ORDER BY position",
			tenantID)
	}
	if err != nil {
		return fmt.Errorf("lỗi khi lấy danh sách categories anh em: %w", err)
	}
	defer rows.Close()

	// Cập nhật position từ 0 -> n-1 theo thứ tự hiện tại
	position := 0
	for rows.Next() {
		var categoryID int
		if err := rows.Scan(&categoryID); err != nil {
			return fmt.Errorf("lỗi khi đọc category ID: %w", err)
		}

		_, err = tx.ExecContext(ctx,
			"UPDATE ecom_product_categories SET position = ? WHERE category_id = ? AND tenant_id = ?",
			position, categoryID, tenantID)
		if err != nil {
			return fmt.Errorf("lỗi khi chuẩn hóa position: %w", err)
		}
		position++
	}

	// Kiểm tra lỗi từ rows.Next()
	if err := rows.Err(); err != nil {
		return fmt.Errorf("lỗi khi lặp qua các categories anh em: %w", err)
	}

	// Commit transaction
	if err = tx.Commit(); err != nil {
		return fmt.Errorf("lỗi khi commit transaction: %w", err)
	}

	return nil
}

// MoveNodeProcedure calls the ecom_product_categories_move_node stored procedure
func (r *CategoryRepository) MoveNodeProcedure(ctx context.Context, tenantID int, categoryID int, newParentID int, position int) error {
	var parentID *int = nil
	if newParentID != 0 {
		parentID = &newParentID
	}
	return r.MoveNode(ctx, tenantID, categoryID, parentID, position)
}

// UpdatePositionProcedure calls the ecom_product_categories_update_position stored procedure
func (r *CategoryRepository) UpdatePositionProcedure(ctx context.Context, tenantID int, categoryID int, position int) error {
	return r.UpdatePosition(ctx, tenantID, categoryID, position)
}

// MoveNodeSiblingProcedure calls the ecom_product_categories_move_node_sibling stored procedure
func (r *CategoryRepository) MoveNodeSiblingProcedure(ctx context.Context, tenantID int, categoryID int, targetID int) error {
	return r.MoveNodeSibling(ctx, tenantID, categoryID, targetID)
}
