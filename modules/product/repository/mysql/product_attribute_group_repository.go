package mysql

import (
	"context"
	"fmt"
	"time"

	"github.com/jmoiron/sqlx"
	"gorm.io/gorm"

	"wnapi/modules/product/models"
	"wnapi/modules/product/repository"
)

// ProductAttributeGroupRepository implements the repository.ProductAttributeGroupRepository interface with MySQL
type ProductAttributeGroupRepository struct {
	db     *sqlx.DB
	gormDB *gorm.DB
}

// NewProductAttributeGroupRepository creates a new ProductAttributeGroupRepository instance
func NewProductAttributeGroupRepository(db *sqlx.DB, gormDB *gorm.DB) repository.ProductAttributeGroupRepository {
	return &ProductAttributeGroupRepository{
		db:     db,
		gormDB: gormDB,
	}
}

// Create adds a new product attribute group to the database
func (r *ProductAttributeGroupRepository) Create(ctx context.Context, group *models.ProductAttributeGroup) error {
	// Set timestamps if not already set
	now := time.Now()
	if group.CreatedAt.IsZero() {
		group.CreatedAt = now
	}
	if group.UpdatedAt.IsZero() {
		group.UpdatedAt = now
	}

	// Create group using GORM
	result := r.gormDB.WithContext(ctx).Create(group)
	if result.Error != nil {
		return fmt.Errorf("failed to create product attribute group: %w", result.Error)
	}

	return nil
}

// GetByID retrieves a product attribute group by its ID
func (r *ProductAttributeGroupRepository) GetByID(ctx context.Context, tenantID, groupID int) (*models.ProductAttributeGroup, error) {
	var group models.ProductAttributeGroup
	result := r.gormDB.WithContext(ctx).Where("tenant_id = ? AND group_id = ?", tenantID, groupID).First(&group)
	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return nil, ErrNotFound
		}
		return nil, fmt.Errorf("failed to get product attribute group: %w", result.Error)
	}

	return &group, nil
}

// GetByCode retrieves a product attribute group by its code
func (r *ProductAttributeGroupRepository) GetByCode(ctx context.Context, tenantID int, code string) (*models.ProductAttributeGroup, error) {
	var group models.ProductAttributeGroup
	result := r.gormDB.WithContext(ctx).Where("tenant_id = ? AND code = ?", tenantID, code).First(&group)
	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return nil, ErrNotFound
		}
		return nil, fmt.Errorf("failed to get product attribute group by code: %w", result.Error)
	}

	return &group, nil
}

// Update updates an existing product attribute group
func (r *ProductAttributeGroupRepository) Update(ctx context.Context, group *models.ProductAttributeGroup) error {
	// Set updated timestamp
	group.UpdatedAt = time.Now()

	// Update group using GORM
	result := r.gormDB.WithContext(ctx).Save(group)
	if result.Error != nil {
		return fmt.Errorf("failed to update product attribute group: %w", result.Error)
	}

	return nil
}

// Delete removes a product attribute group from the database
func (r *ProductAttributeGroupRepository) Delete(ctx context.Context, tenantID, groupID int) error {
	result := r.gormDB.WithContext(ctx).Where("tenant_id = ? AND group_id = ?", tenantID, groupID).Delete(&models.ProductAttributeGroup{})
	if result.Error != nil {
		return fmt.Errorf("failed to delete product attribute group: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return ErrNotFound
	}

	return nil
}

// List retrieves a list of product attribute groups with pagination
func (r *ProductAttributeGroupRepository) List(ctx context.Context, tenantID int, cursor string, limit int) ([]*models.ProductAttributeGroup, string, bool, error) {
	if limit <= 0 {
		limit = 10 // Default limit
	}

	// Start query
	query := r.gormDB.WithContext(ctx).Model(&models.ProductAttributeGroup{}).Where("tenant_id = ?", tenantID)

	// Apply cursor-based pagination
	if cursor != "" {
		query = query.Where("group_id > ?", cursor)
	}

	// Order by ID and limit results
	query = query.Order("group_id ASC").Limit(limit + 1) // +1 to check if there are more results

	// Execute query
	var groups []*models.ProductAttributeGroup
	if err := query.Find(&groups).Error; err != nil {
		return nil, "", false, fmt.Errorf("failed to list product attribute groups: %w", err)
	}

	// Check if there are more results
	hasMore := false
	nextCursor := ""
	if len(groups) > limit {
		hasMore = true
		groups = groups[:limit]
	}

	// Set next cursor if there are more results
	if hasMore && len(groups) > 0 {
		nextCursor = fmt.Sprintf("%d", groups[len(groups)-1].GroupID)
	}

	return groups, nextCursor, hasMore, nil
}

// GetAll retrieves all product attribute groups for a tenant
func (r *ProductAttributeGroupRepository) GetAll(ctx context.Context, tenantID int) ([]*models.ProductAttributeGroup, error) {
	var groups []*models.ProductAttributeGroup
	result := r.gormDB.WithContext(ctx).Where("tenant_id = ?", tenantID).Order("display_order ASC, name ASC").Find(&groups)
	if result.Error != nil {
		return nil, fmt.Errorf("failed to get all product attribute groups: %w", result.Error)
	}

	return groups, nil
}
