package product

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/jmoiron/sqlx"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"

	"wnapi/modules/product/configs"
	"wnapi/modules/product/service"
	// TODO: Add auth import when pkg/auth is available
)

// Module represents the product module
type Module struct {
	DB              *sqlx.DB
	GormDB          *gorm.DB
	Config          *configs.Config
	server          *http.Server
	router          *gin.Engine
	categoryService service.CategoryService
	productService  service.ProductService
}

// NewModule creates a new instance of the product module
func NewModule(db *sqlx.DB) *Module {
	// Khởi tạo GormDB từ chuỗi kết nối hiện có
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn: db.DB,
	}), &gorm.Config{})

	if err != nil {
		log.Fatalf("Failed to initialize GORM: %v", err)
	}

	return &Module{
		DB:     db,
		GormDB: gormDB,
	}
}

// NewModuleWithConfig creates a new instance of the product module with configuration
func NewModuleWithConfig(cfg *configs.Config) *Module {
	// Set up database connection
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?parseTime=true",
		cfg.DB.Username,
		cfg.DB.Password,
		cfg.DB.Host,
		cfg.DB.Port,
		cfg.DB.Database,
	)

	db, err := sqlx.Connect("mysql", dsn)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// Set connection pool settings
	db.SetMaxOpenConns(25)
	db.SetMaxIdleConns(5)
	db.SetConnMaxLifetime(5 * time.Minute)

	// Khởi tạo GormDB từ chuỗi kết nối
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		DSN: dsn,
	}), &gorm.Config{})

	if err != nil {
		log.Fatalf("Failed to initialize GORM: %v", err)
	}

	return &Module{
		DB:     db,
		GormDB: gormDB,
		Config: cfg,
		router: gin.Default(),
	}
}

// RegisterRoutes registers all routes for the product module
func (m *Module) RegisterRoutes(router *gin.Engine) {
	// TODO: Implement proper route registration in Task 02
	// Temporarily commented out auth-related code until pkg/auth is available

	/*
		// Create JWT service từ cấu hình config.yaml
		jwtConfig := auth.JWTConfig{
			AccessSigningKey:       m.Config.JWT.AccessSigningKey,
			RefreshSigningKey:      m.Config.JWT.RefreshSigningKey,
			AccessTokenExpiration:  24 * time.Hour,  // Mặc định nếu không đọc được từ config
			RefreshTokenExpiration: 720 * time.Hour, // Mặc định nếu không đọc được từ config
			Issuer:                 m.Config.JWT.Issuer,
		}

		// Xử lý chuỗi thời gian từ config
		if m.Config.JWT.AccessTokenExpiration != "" {
			accessDuration, err := time.ParseDuration(m.Config.JWT.AccessTokenExpiration)
			if err == nil {
				jwtConfig.AccessTokenExpiration = accessDuration
			} else {
				log.Printf("WARN: Không thể parse AccessTokenExpiration: %v, sử dụng giá trị mặc định", err)
			}
		}

		if m.Config.JWT.RefreshTokenExpiration != "" {
			refreshDuration, err := time.ParseDuration(m.Config.JWT.RefreshTokenExpiration)
			if err == nil {
				jwtConfig.RefreshTokenExpiration = refreshDuration
			} else {
				log.Printf("WARN: Không thể parse RefreshTokenExpiration: %v, sử dụng giá trị mặc định", err)
			}
		}

		jwtService := auth.NewJWTService(jwtConfig)

		// Create mock permission service for development
		permService := &mockPermissionService{}

		// Register routes using the new function
		api.RegisterRoutes(router, m.DB, m.GormDB, jwtService, permService)
	*/
}

// mockPermissionService is a simple implementation of the PermissionService interface
type mockPermissionService struct{}

// CheckPermission implements the PermissionService interface, always returns true in development
func (s *mockPermissionService) CheckPermission(ctx *gin.Context, tenantID, userID uint, permission string) (bool, error) {
	// For development, always allow access
	return true, nil
}

// Start starts the ecom module server
func (m *Module) Start() error {
	// If router is not set, use the one from the module
	if m.router == nil {
		m.router = gin.Default()
	}

	// Register routes
	m.RegisterRoutes(m.router)

	// Create HTTP server
	m.server = &http.Server{
		Addr:    fmt.Sprintf("%s:%d", m.Config.Server.Host, m.Config.Server.Port),
		Handler: m.router,
	}

	// Start server in a goroutine
	go func() {
		log.Printf("Starting product service on %s:%d", m.Config.Server.Host, m.Config.Server.Port)
		if err := m.server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Failed to start server: %v", err)
		}
	}()

	return nil
}

// Stop gracefully stops the product module server
func (m *Module) Stop() error {
	if m.server == nil {
		return nil
	}

	// Create a context with timeout for shutdown
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Shutdown server
	if err := m.server.Shutdown(ctx); err != nil {
		return fmt.Errorf("server shutdown failed: %w", err)
	}

	// Close database connection
	if m.DB != nil {
		if err := m.DB.Close(); err != nil {
			return fmt.Errorf("database connection close failed: %w", err)
		}
	}

	log.Println("Product service stopped gracefully")
	return nil
}
