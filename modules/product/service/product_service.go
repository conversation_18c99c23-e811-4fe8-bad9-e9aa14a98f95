package service

import (
	"context"
	"fmt"
	"time"

	"github.com/gosimple/slug"

	"wnapi/modules/product/dto/request"
	"wnapi/modules/product/dto/response"
	"wnapi/modules/product/models"
	"wnapi/modules/product/repository"
)

// ProductService handles business logic for products
type ProductService interface {
	CreateProduct(ctx context.Context, tenantID int, req *request.CreateProductRequest) (*response.ProductResponse, error)
	GetProduct(ctx context.Context, tenantID int, productID int) (*response.ProductResponse, error)
	UpdateProduct(ctx context.Context, tenantID int, productID int, req *request.UpdateProductRequest) (*response.ProductResponse, error)
	DeleteProduct(ctx context.Context, tenantID int, productID int) error
	ListProducts(ctx context.Context, tenantID int, req *request.ListProductRequest) (*response.ProductListResponse, error)
	SearchProducts(ctx context.Context, tenantID int, req *request.SearchProductRequest) (*response.ProductListResponse, error)
	GetAllProducts(ctx context.Context, tenantID int) ([]*response.ProductResponse, error)
	GetProductCategories(ctx context.Context, tenantID int) ([]map[string]interface{}, error)
}

// productService implements the ProductService interface
type productService struct {
	productRepo               repository.ProductRepository
	variantService            ProductVariantService
	variantAttrValueService   ProductVariantAttributeValueService
	attributeService          *ProductAttributeService
	configurableAttributeRepo repository.ProductConfigurableAttributeRepository
	attributeOptionService    *ProductAttributeOptionService
}

// NewProductService creates a new product service instance
func NewProductService(
	productRepo repository.ProductRepository,
	variantService ProductVariantService,
	variantAttrValueService ProductVariantAttributeValueService,
	attributeService *ProductAttributeService,
	configurableAttributeRepo repository.ProductConfigurableAttributeRepository,
	attributeOptionService *ProductAttributeOptionService,
) ProductService {
	return &productService{
		productRepo:               productRepo,
		variantService:            variantService,
		variantAttrValueService:   variantAttrValueService,
		attributeService:          attributeService,
		configurableAttributeRepo: configurableAttributeRepo,
		attributeOptionService:    attributeOptionService,
	}
}

// CreateProduct creates a new product
func (s *productService) CreateProduct(ctx context.Context, tenantID int, req *request.CreateProductRequest) (*response.ProductResponse, error) {
	// Generate slug if not provided
	productSlug := req.Slug
	if productSlug == "" {
		productSlug = slug.Make(req.Name)
	}

	// Create product model
	product := &models.Product{
		TenantID:       uint(tenantID),
		Name:           req.Name,
		Description:    req.Description,
		Content:        req.Content,
		Slug:           productSlug,
		ImageURL:       req.ImageURL,
		BasePrice:      req.BasePrice,
		ProductType:    req.ProductType,
		Status:         req.Status,
		ProductCode:    req.ProductCode,
		IsTaxable:      req.IsTaxable,
		IsVirtual:      req.IsVirtual,
		IsDownloadable: req.IsDownloadable,
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
		CreatedBy:      uintPtr(uint(getUserIDFromContext(ctx))),
	}

	// Set category ID if provided
	if req.CategoryID != nil {
		categoryID := uint(*req.CategoryID)
		product.CategoryID = &categoryID
	}

	// Set cost price if provided
	if req.CostPrice != nil {
		product.CostPrice = req.CostPrice
	}

	// Set tax class ID if provided
	if req.TaxClassID != nil {
		taxClassID := uint(*req.TaxClassID)
		product.TaxClassID = &taxClassID
	}

	// Save to repository
	if err := s.productRepo.Create(ctx, product); err != nil {
		return nil, fmt.Errorf("failed to create product: %w", err)
	}

	// Xử lý lưu thông tin attributes nếu là sản phẩm CONFIGURABLE
	if product.ProductType == "CONFIGURABLE" && len(req.Attributes) > 0 {
		// Lưu configurable attributes
		if s.configurableAttributeRepo != nil && s.attributeService != nil {
			configurableAttrs := make([]*models.ProductConfigurableAttribute, 0, len(req.Attributes))

			for i, attr := range req.Attributes {
				// Tìm attribute dựa trên tên
				// Vì không có phương thức GetByName, chúng ta dùng GetAll và tìm thủ công
				var attributeID uint
				attributes, err := s.attributeService.GetAll(uint(tenantID))
				if err != nil {
					fmt.Printf("Warning: failed to get attributes: %v\n", err)
					continue
				}

				// Debug để kiểm tra danh sách thuộc tính
				fmt.Printf("DEBUG: Found %d attributes in database\n", len(attributes))
				for _, a := range attributes {
					fmt.Printf("DEBUG: Attribute in DB - ID: %d, Name: %s, Code: %s\n", a.AttributeID, a.Name, a.Code)
				}

				// Tìm attribute có tên phù hợp
				attributeFound := false
				for _, a := range attributes {
					if a.Name == attr.Name {
						attributeID = a.AttributeID
						attributeFound = true
						fmt.Printf("DEBUG: Found attribute with name '%s', ID: %d\n", attr.Name, attributeID)
						break
					}
				}

				// Nếu không tìm thấy, thử tìm theo code
				if !attributeFound {
					attrCode := slug.Make(attr.Name)
					for _, a := range attributes {
						if a.Code == attrCode {
							attributeID = a.AttributeID
							attributeFound = true
							fmt.Printf("DEBUG: Found attribute with code '%s', ID: %d\n", attrCode, attributeID)
							break
						}
					}
				}

				// Nếu không tìm thấy, tạo attribute mới
				if !attributeFound {
					// Tạo attribute mới
					var groupID uint = 1
					frontendInput := "select"
					attrReq := &request.CreateProductAttributeRequest{
						GroupID:        &groupID, // Sử dụng GroupID mặc định
						Name:           attr.Name,
						Code:           slug.Make(attr.Name), // Tạo code từ tên
						Type:           "SELECT",             // Kiểu SELECT cho thuộc tính có các giá trị lựa chọn
						IsConfigurable: true,
						IsFilterable:   attr.IsFilterable,
						IsSearchable:   true,
						IsComparable:   false,
						IsRequired:     false,
						FrontendInput:  &frontendInput,
						DisplayOrder:   uint(i),
					}

					newAttr, attrErr := s.attributeService.Create(uint(tenantID), attrReq)
					if attrErr != nil {
						fmt.Printf("Warning: failed to create attribute '%s': %v\n", attr.Name, attrErr)
						continue
					}

					attributeID = newAttr.AttributeID
					attributeFound = true

					// Tạo attribute options cho các giá trị
					if s.attributeOptionService != nil && len(attr.Values) > 0 {
						for j, value := range attr.Values {
							optionReq := &request.CreateProductAttributeOptionRequest{
								AttributeID:  newAttr.AttributeID,
								Value:        value,
								DisplayOrder: uint(j),
							}

							_, optErr := s.attributeOptionService.Create(uint(tenantID), optionReq)
							if optErr != nil {
								fmt.Printf("Warning: failed to create option '%s' for attribute '%s': %v\n", value, attr.Name, optErr)
								// Tiếp tục, không dừng quá trình
							} else {
								fmt.Printf("Info: created option '%s' for attribute '%s'\n", value, attr.Name)
							}
						}
					} else {
						// Không có attributeOptionService hoặc không có values
						fmt.Printf("Info: should create options for attribute '%s'\n", attr.Name)
					}
				}

				if !attributeFound {
					// Không thể tạo attribute, bỏ qua và tiếp tục
					fmt.Printf("Warning: attribute with name '%s' not found and could not be created, skipping\n", attr.Name)
					continue
				}

				// Tạo configurable attribute link
				configurableAttr := &models.ProductConfigurableAttribute{
					TenantID:    uint(tenantID),
					ProductID:   product.ProductID,
					AttributeID: attributeID,
					Position:    uint(i), // Vị trí dựa trên thứ tự trong mảng
					CreatedAt:   time.Now(),
					UpdatedAt:   time.Now(),
				}

				configurableAttrs = append(configurableAttrs, configurableAttr)
			}

			// Lưu tất cả configurable attributes mới nếu có
			if len(configurableAttrs) > 0 {
				if err := s.configurableAttributeRepo.CreateBatch(ctx, configurableAttrs); err != nil {
					fmt.Printf("Warning: failed to create configurable attributes: %v\n", err)
				}
			}
		} else {
			fmt.Println("Warning: configurableAttributeRepo or attributeService is nil, skipping attribute saving")
		}
	}

	// Lưu variants nếu có
	if len(req.Variants) > 0 {
		// Tạo batch request cho variants
		variantCreateRequest := &request.BatchCreateProductVariantRequest{
			ProductID: product.ProductID,
			Variants:  make([]request.ProductVariantCreateData, 0, len(req.Variants)),
		}

		// Thêm các variants vào request
		for _, variantDTO := range req.Variants {
			// Tính giá variant dựa trên base price và price adjustment
			price := product.BasePrice
			if req.ManageVariantPricing {
				price += variantDTO.PriceAdjustment
			}

			// Tạo variant data
			imageURL := ""
			variantCreateRequest.Variants = append(variantCreateRequest.Variants, request.ProductVariantCreateData{
				SKU:       variantDTO.SKU,
				Price:     &price,
				CostPrice: &variantDTO.CostPrice,
				ImageURL:  &imageURL,
				IsActive:  variantDTO.IsVisible,
			})
		}

		// Gọi service để tạo variants
		createdVariants, err := s.variantService.BatchCreateVariants(ctx, tenantID, variantCreateRequest)
		if err != nil {
			// Log lỗi nhưng vẫn tiếp tục
			fmt.Printf("Warning: failed to create variants: %v\n", err)
		} else {
			// Lưu attribute values cho variants
			// Với mỗi variant, lưu các attribute values
			for i, variantResp := range createdVariants {
				variantDTO := req.Variants[i]

				// Tạo batch request cho variant attribute values
				attributeValuesRequest := &request.BatchCreateVariantAttributeValueRequest{
					VariantID:       uint(variantResp.ID),
					ReplaceExisting: true,
					Values:          make([]request.VariantAttributeValueItem, 0, len(variantDTO.Attributes)),
				}

				// Thêm các attribute values vào request
				for attrName, attrValue := range variantDTO.Attributes {
					// Tìm attribute ID theo tên
					// Tìm option ID theo giá trị
					var attributeID uint
					// Đoạn code dưới đây là giả định, cần thực hiện thực tế
					// attributeObj, err := s.attributeService.GetByName(uint(tenantID), attrName)
					// if err == nil && attributeObj != nil {
					//    attributeID = attributeObj.AttributeID
					// }

					// Thêm vào request
					attributeValuesRequest.Values = append(attributeValuesRequest.Values, request.VariantAttributeValueItem{
						AttributeID: attributeID,
						Value:       attrValue,
					})

					_ = attrName // Tạm thời bỏ qua để tránh lỗi biến không sử dụng
				}

				// Gọi service để tạo variant attribute values
				_, err := s.variantAttrValueService.BatchCreateVariantAttributeValues(ctx, tenantID, attributeValuesRequest)
				if err != nil {
					// Log lỗi nhưng vẫn tiếp tục
					fmt.Printf("Warning: failed to create variant attribute values for variant %d: %v\n", variantResp.ID, err)
				}
			}
		}
	}

	// Get the created product
	createdProduct, err := s.productRepo.GetByID(ctx, tenantID, int(product.ProductID))
	if err != nil {
		return nil, fmt.Errorf("failed to get created product: %w", err)
	}

	// Map to response
	response := mapProductToResponse(createdProduct)

	// Lấy attributes và variants từ DB
	attributes, variants, manageVariantPricing, err := s.productRepo.GetProductAttributesAndVariants(ctx, tenantID, int(product.ProductID))
	if err != nil {
		// Log lỗi nhưng vẫn tiếp tục
		fmt.Printf("Warning: failed to get attributes and variants: %v\n", err)

		// Sử dụng attributes và variants từ request nếu không lấy được từ DB
		if len(req.Attributes) > 0 {
			response.Attributes = req.Attributes
		}

		if len(req.Variants) > 0 {
			response.Variants = req.Variants
		}

		response.ManageVariantPricing = req.ManageVariantPricing
	} else {
		// Sử dụng attributes và variants từ DB
		response.Attributes = attributes
		response.Variants = variants
		response.ManageVariantPricing = manageVariantPricing
	}

	return response, nil
}

// GetProduct retrieves a product by ID
func (s *productService) GetProduct(ctx context.Context, tenantID int, productID int) (*response.ProductResponse, error) {
	product, err := s.productRepo.GetByID(ctx, tenantID, productID)
	if err != nil {
		return nil, fmt.Errorf("failed to get product: %w", err)
	}

	// Map product to response
	productResponse := mapProductToResponse(product)

	// Lấy thông tin về attributes và variants từ repository
	attributes, variants, manageVariantPricing, err := s.productRepo.GetProductAttributesAndVariants(ctx, tenantID, productID)
	if err != nil {
		// Log lỗi nhưng vẫn trả về thông tin sản phẩm cơ bản
		fmt.Printf("Warning: failed to get attributes and variants: %v\n", err)
	} else {
		// Gán thông tin attributes và variants vào response
		productResponse.Attributes = attributes
		productResponse.Variants = variants
		productResponse.ManageVariantPricing = manageVariantPricing
	}

	return productResponse, nil
}

// UpdateProduct updates an existing product
func (s *productService) UpdateProduct(ctx context.Context, tenantID int, productID int, req *request.UpdateProductRequest) (*response.ProductResponse, error) {
	// Get existing product
	product, err := s.productRepo.GetByID(ctx, tenantID, productID)
	if err != nil {
		return nil, fmt.Errorf("failed to get product for update: %w", err)
	}

	// Update fields if provided
	if req.Name != "" {
		product.Name = req.Name
	}

	if req.Description != "" {
		product.Description = req.Description
	}

	if req.Content != "" {
		product.Content = req.Content
	}

	if req.Slug != "" {
		product.Slug = req.Slug
	} else if req.Name != "" && product.Name != req.Name {
		// Generate new slug if name changed and slug not provided
		product.Slug = slug.Make(req.Name)
	}

	if req.ImageURL != "" {
		product.ImageURL = req.ImageURL
	}

	if req.BasePrice > 0 {
		product.BasePrice = req.BasePrice
	}

	if req.CostPrice != nil {
		product.CostPrice = req.CostPrice
	}

	if req.ProductType != "" {
		product.ProductType = req.ProductType
	}

	if req.Status != "" {
		product.Status = req.Status
	}

	if req.ProductCode != "" {
		product.ProductCode = req.ProductCode
	}

	// Update boolean fields
	product.IsTaxable = req.IsTaxable
	product.IsVirtual = req.IsVirtual
	product.IsDownloadable = req.IsDownloadable

	// Update category ID if provided
	if req.CategoryID != nil {
		categoryID := uint(*req.CategoryID)
		product.CategoryID = &categoryID
	}

	// Update tax class ID if provided
	if req.TaxClassID != nil {
		taxClassID := uint(*req.TaxClassID)
		product.TaxClassID = &taxClassID
	}

	// Update timestamps and user
	product.UpdatedAt = time.Now()
	product.UpdatedBy = uintPtr(uint(getUserIDFromContext(ctx)))

	// Save to repository
	if err := s.productRepo.Update(ctx, product); err != nil {
		return nil, fmt.Errorf("failed to update product: %w", err)
	}

	// Cập nhật thông tin attributes nếu là sản phẩm CONFIGURABLE
	if product.ProductType == "CONFIGURABLE" && len(req.Attributes) > 0 {
		// Xóa configurable attributes cũ
		if s.configurableAttributeRepo != nil {
			if err := s.configurableAttributeRepo.DeleteByProductID(ctx, tenantID, int(product.ProductID)); err != nil {
				// Chỉ ghi log lỗi, không dừng quá trình cập nhật
				fmt.Printf("Warning: failed to delete configurable attributes: %v\n", err)
			}
		}

		// Lưu configurable attributes mới
		if s.configurableAttributeRepo != nil && s.attributeService != nil {
			configurableAttrs := make([]*models.ProductConfigurableAttribute, 0, len(req.Attributes))

			for i, attr := range req.Attributes {
				// Tìm attribute dựa trên tên
				// Vì không có phương thức GetByName, chúng ta dùng GetAll và tìm thủ công
				var attributeID uint
				attributes, err := s.attributeService.GetAll(uint(tenantID))
				if err != nil {
					fmt.Printf("Warning: failed to get attributes: %v\n", err)
					continue
				}

				// Debug để kiểm tra danh sách thuộc tính
				fmt.Printf("DEBUG: Found %d attributes in database\n", len(attributes))
				for _, a := range attributes {
					fmt.Printf("DEBUG: Attribute in DB - ID: %d, Name: %s, Code: %s\n", a.AttributeID, a.Name, a.Code)
				}

				// Tìm attribute có tên phù hợp
				attributeFound := false
				for _, a := range attributes {
					if a.Name == attr.Name {
						attributeID = a.AttributeID
						attributeFound = true
						fmt.Printf("DEBUG: Found attribute with name '%s', ID: %d\n", attr.Name, attributeID)
						break
					}
				}

				// Nếu không tìm thấy, thử tìm theo code
				if !attributeFound {
					attrCode := slug.Make(attr.Name)
					for _, a := range attributes {
						if a.Code == attrCode {
							attributeID = a.AttributeID
							attributeFound = true
							fmt.Printf("DEBUG: Found attribute with code '%s', ID: %d\n", attrCode, attributeID)
							break
						}
					}
				}

				// Nếu không tìm thấy, tạo attribute mới
				if !attributeFound {
					// Tạo attribute mới
					var groupID uint = 1
					frontendInput := "select"
					attrReq := &request.CreateProductAttributeRequest{
						GroupID:        &groupID, // Sử dụng GroupID mặc định
						Name:           attr.Name,
						Code:           slug.Make(attr.Name), // Tạo code từ tên
						Type:           "SELECT",             // Kiểu SELECT cho thuộc tính có các giá trị lựa chọn
						IsConfigurable: true,
						IsFilterable:   attr.IsFilterable,
						IsSearchable:   true,
						IsComparable:   false,
						IsRequired:     false,
						FrontendInput:  &frontendInput,
						DisplayOrder:   uint(i),
					}

					newAttr, attrErr := s.attributeService.Create(uint(tenantID), attrReq)
					if attrErr != nil {
						fmt.Printf("Warning: failed to create attribute '%s': %v\n", attr.Name, attrErr)
						continue
					}

					attributeID = newAttr.AttributeID
					attributeFound = true

					// Tạo attribute options cho các giá trị
					if s.attributeOptionService != nil && len(attr.Values) > 0 {
						for j, value := range attr.Values {
							optionReq := &request.CreateProductAttributeOptionRequest{
								AttributeID:  newAttr.AttributeID,
								Value:        value,
								DisplayOrder: uint(j),
							}

							_, optErr := s.attributeOptionService.Create(uint(tenantID), optionReq)
							if optErr != nil {
								fmt.Printf("Warning: failed to create option '%s' for attribute '%s': %v\n", value, attr.Name, optErr)
								// Tiếp tục, không dừng quá trình
							} else {
								fmt.Printf("Info: created option '%s' for attribute '%s'\n", value, attr.Name)
							}
						}
					} else {
						// Không có attributeOptionService hoặc không có values
						fmt.Printf("Info: should create options for attribute '%s'\n", attr.Name)
					}
				}

				if !attributeFound {
					// Không thể tạo attribute, bỏ qua và tiếp tục
					fmt.Printf("Warning: attribute with name '%s' not found and could not be created, skipping\n", attr.Name)
					continue
				}

				// Tạo configurable attribute link
				configurableAttr := &models.ProductConfigurableAttribute{
					TenantID:    uint(tenantID),
					ProductID:   product.ProductID,
					AttributeID: attributeID,
					Position:    uint(i), // Vị trí dựa trên thứ tự trong mảng
					CreatedAt:   time.Now(),
					UpdatedAt:   time.Now(),
				}

				configurableAttrs = append(configurableAttrs, configurableAttr)
			}

			// Lưu tất cả configurable attributes mới nếu có
			if len(configurableAttrs) > 0 {
				if err := s.configurableAttributeRepo.CreateBatch(ctx, configurableAttrs); err != nil {
					fmt.Printf("Warning: failed to create configurable attributes: %v\n", err)
				}
			}
		} else {
			fmt.Println("Warning: configurableAttributeRepo or attributeService is nil, skipping attribute saving")
		}
	}

	// Cập nhật variants nếu có
	if len(req.Variants) > 0 {
		// Xóa variants cũ nếu cần
		if err := s.variantService.DeleteVariantsByProductID(ctx, tenantID, int(product.ProductID)); err != nil {
			// Log lỗi nhưng vẫn tiếp tục
			fmt.Printf("Warning: failed to delete existing variants: %v\n", err)
		}

		// Tạo variants mới
		variantCreateRequest := &request.BatchCreateProductVariantRequest{
			ProductID: product.ProductID,
			Variants:  make([]request.ProductVariantCreateData, 0, len(req.Variants)),
		}

		// Thêm các variants vào request
		for _, variantDTO := range req.Variants {
			// Tính giá variant dựa trên base price và price adjustment
			price := product.BasePrice
			if req.ManageVariantPricing {
				price += variantDTO.PriceAdjustment
			}

			// Tạo variant data
			imageURL := ""
			variantCreateRequest.Variants = append(variantCreateRequest.Variants, request.ProductVariantCreateData{
				SKU:       variantDTO.SKU,
				Price:     &price,
				CostPrice: &variantDTO.CostPrice,
				ImageURL:  &imageURL,
				IsActive:  variantDTO.IsVisible,
			})
		}

		// Gọi service để tạo variants
		createdVariants, err := s.variantService.BatchCreateVariants(ctx, tenantID, variantCreateRequest)
		if err != nil {
			// Log lỗi nhưng vẫn tiếp tục
			fmt.Printf("Warning: failed to create variants: %v\n", err)
		} else {
			// Lưu attribute values cho variants
			variantAttrValueService := s.variantAttrValueService

			// Với mỗi variant, lưu các attribute values
			for i, variantResp := range createdVariants {
				variantDTO := req.Variants[i]

				// Tạo batch request cho variant attribute values
				attributeValuesRequest := &request.BatchCreateVariantAttributeValueRequest{
					VariantID:       uint(variantResp.ID),
					ReplaceExisting: true,
					Values:          make([]request.VariantAttributeValueItem, 0, len(variantDTO.Attributes)),
				}

				// Thêm các attribute values vào request
				for attrName, attrValue := range variantDTO.Attributes {
					// Tìm attribute ID theo tên
					// Tìm option ID theo giá trị
					var attributeID uint
					// Đoạn code dưới đây là giả định, cần thực hiện thực tế
					// attributeObj, err := s.attributeService.GetByName(uint(tenantID), attrName)
					// if err == nil && attributeObj != nil {
					//    attributeID = attributeObj.AttributeID
					// }

					// Thêm vào request
					attributeValuesRequest.Values = append(attributeValuesRequest.Values, request.VariantAttributeValueItem{
						AttributeID: attributeID,
						Value:       attrValue,
					})

					_ = attrName // Tạm thời bỏ qua để tránh lỗi biến không sử dụng
				}

				// Gọi service để tạo variant attribute values
				_, err := variantAttrValueService.BatchCreateVariantAttributeValues(ctx, tenantID, attributeValuesRequest)
				if err != nil {
					// Log lỗi nhưng vẫn tiếp tục
					fmt.Printf("Warning: failed to create variant attribute values for variant %d: %v\n", variantResp.ID, err)
				}
			}
		}
	}

	// Map to response
	response := mapProductToResponse(product)

	// Lấy attributes và variants từ DB
	attributes, variants, manageVariantPricing, err := s.productRepo.GetProductAttributesAndVariants(ctx, tenantID, productID)
	if err != nil {
		// Log lỗi nhưng vẫn tiếp tục
		fmt.Printf("Warning: failed to get attributes and variants: %v\n", err)

		// Sử dụng attributes và variants từ request nếu không lấy được từ DB
		if len(req.Attributes) > 0 {
			response.Attributes = req.Attributes
		}

		if len(req.Variants) > 0 {
			response.Variants = req.Variants
		}

		response.ManageVariantPricing = req.ManageVariantPricing
	} else {
		// Sử dụng attributes và variants từ DB
		response.Attributes = attributes
		response.Variants = variants
		response.ManageVariantPricing = manageVariantPricing
	}

	return response, nil
}

// DeleteProduct removes a product
func (s *productService) DeleteProduct(ctx context.Context, tenantID int, productID int) error {
	if err := s.productRepo.Delete(ctx, tenantID, productID); err != nil {
		return fmt.Errorf("failed to delete product: %w", err)
	}

	return nil
}

// ListProducts retrieves a list of products with pagination
func (s *productService) ListProducts(ctx context.Context, tenantID int, req *request.ListProductRequest) (*response.ProductListResponse, error) {
	// Set default limit if not provided
	limit := req.Limit
	if limit <= 0 {
		limit = 10
	}

	// Prepare filters
	filters := make(map[string]interface{})
	if req.CategoryID != nil {
		filters["category_id"] = *req.CategoryID
	}

	if req.Status != "" {
		filters["status"] = req.Status
	}

	// Get products from repository
	products, nextCursor, hasMore, err := s.productRepo.List(ctx, tenantID, req.Cursor, limit, filters)
	if err != nil {
		return nil, fmt.Errorf("failed to list products: %w", err)
	}

	// Map to response
	productResponses := make([]*response.ProductResponse, len(products))
	for i, product := range products {
		productResponses[i] = mapProductToResponse(product)
	}

	return &response.ProductListResponse{
		Products: productResponses,
		Meta: response.PaginationMeta{
			NextCursor: nextCursor,
			HasMore:    hasMore,
		},
	}, nil
}

// SearchProducts searches for products by keyword
func (s *productService) SearchProducts(ctx context.Context, tenantID int, req *request.SearchProductRequest) (*response.ProductListResponse, error) {
	// Set default limit if not provided
	limit := req.Limit
	if limit <= 0 {
		limit = 10
	}

	// Prepare filters
	filters := make(map[string]interface{})
	if req.CategoryID != nil {
		filters["category_id"] = *req.CategoryID
	}

	if req.Status != "" {
		filters["status"] = req.Status
	}

	// Get products from repository
	products, nextCursor, hasMore, err := s.productRepo.Search(ctx, tenantID, req.Keyword, req.Cursor, limit, filters)
	if err != nil {
		return nil, fmt.Errorf("failed to search products: %w", err)
	}

	// Map to response
	productResponses := make([]*response.ProductResponse, len(products))
	for i, product := range products {
		productResponses[i] = mapProductToResponse(product)
	}

	return &response.ProductListResponse{
		Products: productResponses,
		Meta: response.PaginationMeta{
			NextCursor: nextCursor,
			HasMore:    hasMore,
		},
	}, nil
}

// GetAllProducts retrieves all products for a tenant
func (s *productService) GetAllProducts(ctx context.Context, tenantID int) ([]*response.ProductResponse, error) {
	products, err := s.productRepo.GetAll(ctx, tenantID)
	if err != nil {
		return nil, fmt.Errorf("failed to get all products: %w", err)
	}

	// Map to response
	productResponses := make([]*response.ProductResponse, len(products))
	for i, product := range products {
		productResponses[i] = mapProductToResponse(product)
	}

	return productResponses, nil
}

// GetProductCategories retrieves all categories that have products
func (s *productService) GetProductCategories(ctx context.Context, tenantID int) ([]map[string]interface{}, error) {
	return s.productRepo.GetProductCategories(ctx, tenantID)
}

// mapProductToResponse maps a product model to a response DTO
func mapProductToResponse(product *models.Product) *response.ProductResponse {
	var categoryID *int
	if product.CategoryID != nil {
		id := int(*product.CategoryID)
		categoryID = &id
	}

	var taxClassID *int
	if product.TaxClassID != nil {
		id := int(*product.TaxClassID)
		taxClassID = &id
	}

	var createdBy *int
	if product.CreatedBy != nil {
		id := int(*product.CreatedBy)
		createdBy = &id
	}

	var updatedBy *int
	if product.UpdatedBy != nil {
		id := int(*product.UpdatedBy)
		updatedBy = &id
	}

	return &response.ProductResponse{
		ProductID:            int(product.ProductID),
		TenantID:             int(product.TenantID),
		CategoryID:           categoryID,
		Name:                 product.Name,
		Description:          product.Description,
		Content:              product.Content,
		Slug:                 product.Slug,
		ImageURL:             product.ImageURL,
		BasePrice:            product.BasePrice,
		CostPrice:            product.CostPrice,
		ProductType:          product.ProductType,
		Status:               product.Status,
		ProductCode:          product.ProductCode,
		IsTaxable:            product.IsTaxable,
		TaxClassID:           taxClassID,
		IsVirtual:            product.IsVirtual,
		IsDownloadable:       product.IsDownloadable,
		Attributes:           nil,
		Variants:             nil,
		ManageVariantPricing: false,
		CreatedAt:            product.CreatedAt,
		UpdatedAt:            product.UpdatedAt,
		CreatedBy:            createdBy,
		UpdatedBy:            updatedBy,
	}
}

// Helper function to convert int64 to *uint
func uintPtr(u uint) *uint {
	return &u
}
