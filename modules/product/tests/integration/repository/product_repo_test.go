package repository

import (
	"context"
	"database/sql"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"wnapi/modules/product/models"
	"wnapi/modules/product/repository"
	"wnapi/modules/product/tests/integration"
)

func TestFindProducts(t *testing.T) {
	// Khởi tạo repository
	db := integration.GetTestDB(t)
	repo := repository.NewProductRepository(db)

	// Chuẩn bị dữ liệu test
	setupTestProducts(t, db)

	// Thực thi
	ctx := context.Background()
	limit := 10
	filters := repository.ProductFilters{
		Limit: &limit,
	}

	products, nextCursor, err := repo.FindProducts(ctx, filters)

	// Kiểm tra kết quả
	assert.NoError(t, err, "Không nên có lỗi khi tìm kiếm sản phẩm")
	assert.NotEmpty(t, products, "Danh sách sản phẩm không nên trống")
	assert.LessOrEqual(t, len(products), 10, "Số lượng sản phẩm không nên vượt quá giới hạn")

	if len(products) == 10 {
		assert.NotEmpty(t, nextCursor, "nextCursor không nên trống khi có thể có thêm sản phẩm")
	}
}

func TestFindProductByID(t *testing.T) {
	// Khởi tạo repository
	db := integration.GetTestDB(t)
	repo := repository.NewProductRepository(db)

	// Chuẩn bị dữ liệu test
	productID := setupTestProduct(t, db)

	// Thực thi
	ctx := context.Background()
	product, err := repo.FindProductByID(ctx, productID)

	// Kiểm tra kết quả
	assert.NoError(t, err, "Không nên có lỗi khi tìm kiếm sản phẩm theo ID")
	assert.NotNil(t, product, "Sản phẩm không nên là nil")
	assert.Equal(t, productID, product.ID, "ID sản phẩm không khớp")
}

func TestCreateProduct(t *testing.T) {
	// Khởi tạo repository
	db := integration.GetTestDB(t)
	repo := repository.NewProductRepository(db)

	// Chuẩn bị dữ liệu test
	product := &models.Product{
		Name:        "Sản phẩm test repo",
		Price:       150000.00,
		SKU:         "TEST-REPO-" + time.Now().Format("20060102150405"),
		Description: "Sản phẩm test cho repository",
		Stock:       50,
	}

	// Thực thi
	ctx := context.Background()
	err := repo.CreateProduct(ctx, product)

	// Kiểm tra kết quả
	assert.NoError(t, err, "Không nên có lỗi khi tạo sản phẩm")
	assert.NotZero(t, product.ID, "ID sản phẩm không nên là 0 sau khi tạo")

	// Kiểm tra sản phẩm đã được lưu vào DB
	savedProduct, err := repo.FindProductByID(ctx, product.ID)
	assert.NoError(t, err, "Không nên có lỗi khi tìm kiếm sản phẩm vừa tạo")
	assert.Equal(t, product.Name, savedProduct.Name, "Tên sản phẩm không khớp")
	assert.Equal(t, product.Price, savedProduct.Price, "Giá sản phẩm không khớp")
	assert.Equal(t, product.SKU, savedProduct.SKU, "SKU sản phẩm không khớp")
}

func TestUpdateProduct(t *testing.T) {
	// Khởi tạo repository
	db := integration.GetTestDB(t)
	repo := repository.NewProductRepository(db)

	// Tạo sản phẩm test
	product := &models.Product{
		Name:        "Sản phẩm cần cập nhật",
		Price:       200000.00,
		SKU:         "UPDATE-TEST-" + time.Now().Format("20060102150405"),
		Description: "Sản phẩm test cho việc cập nhật",
		Stock:       30,
	}

	ctx := context.Background()
	err := repo.CreateProduct(ctx, product)
	assert.NoError(t, err, "Không nên có lỗi khi tạo sản phẩm")

	// Chuẩn bị dữ liệu cập nhật
	product.Name = "Sản phẩm đã cập nhật"
	product.Price = 250000.00
	product.Description = "Mô tả đã được cập nhật"
	product.Stock = 35

	// Thực thi
	err = repo.UpdateProduct(ctx, product)

	// Kiểm tra kết quả
	assert.NoError(t, err, "Không nên có lỗi khi cập nhật sản phẩm")

	// Kiểm tra sản phẩm đã được cập nhật trong DB
	updatedProduct, err := repo.FindProductByID(ctx, product.ID)
	assert.NoError(t, err, "Không nên có lỗi khi tìm kiếm sản phẩm đã cập nhật")
	assert.Equal(t, "Sản phẩm đã cập nhật", updatedProduct.Name, "Tên sản phẩm không được cập nhật")
	assert.Equal(t, 250000.00, updatedProduct.Price, "Giá sản phẩm không được cập nhật")
	assert.Equal(t, "Mô tả đã được cập nhật", updatedProduct.Description, "Mô tả sản phẩm không được cập nhật")
	assert.Equal(t, 35, updatedProduct.Stock, "Tồn kho sản phẩm không được cập nhật")
}

func TestDeleteProduct(t *testing.T) {
	// Khởi tạo repository
	db := integration.GetTestDB(t)
	repo := repository.NewProductRepository(db)

	// Tạo sản phẩm test để xóa
	product := &models.Product{
		Name:        "Sản phẩm để xóa",
		Price:       50000.00,
		SKU:         "DELETE-TEST-" + time.Now().Format("20060102150405"),
		Description: "Sản phẩm test cho việc xóa",
		Stock:       10,
	}

	ctx := context.Background()
	err := repo.CreateProduct(ctx, product)
	assert.NoError(t, err, "Không nên có lỗi khi tạo sản phẩm")

	// Thực thi
	err = repo.DeleteProduct(ctx, product.ID)

	// Kiểm tra kết quả
	assert.NoError(t, err, "Không nên có lỗi khi xóa sản phẩm")

	// Kiểm tra sản phẩm đã bị xóa
	_, err = repo.FindProductByID(ctx, product.ID)
	assert.Error(t, err, "Nên có lỗi khi tìm kiếm sản phẩm đã xóa")
}

// Hàm helper để setup dữ liệu test
func setupTestProducts(t *testing.T, db *sql.DB) {
	// Xóa dữ liệu cũ (nếu có)
	_, err := db.Exec("DELETE FROM ecom_products")
	assert.NoError(t, err, "Không nên có lỗi khi xóa dữ liệu sản phẩm cũ")

	// Thêm dữ liệu test
	products := []struct {
		name        string
		price       float64
		sku         string
		description string
		stock       int
	}{
		{"Điện thoại Xiaomi Redmi Note 12", 4990000.00, "XM-RN12-64GB", "Điện thoại Xiaomi Redmi Note 12 64GB, RAM 4GB, Màn hình 6.67 inch", 100},
		{"Laptop Acer Nitro 5", 19990000.00, "AC-NT5-I5", "Laptop Acer Nitro 5 Gaming, Intel Core i5-11400H, RAM 8GB, SSD 512GB", 30},
		{"Tai nghe Bluetooth JBL T500BT", 1290000.00, "JBL-T500BT", "Tai nghe Bluetooth chụp tai JBL T500BT, thời gian sử dụng 16 giờ", 50},
		{"Bàn phím cơ Logitech G Pro", 2490000.00, "LG-GPRO-KB", "Bàn phím cơ Logitech G Pro, switch GX Blue, RGB", 45},
		{"Chuột gaming Razer DeathAdder", 990000.00, "RZ-DA-V2", "Chuột gaming Razer DeathAdder V2, 20000 DPI, RGB", 60},
	}

	for _, p := range products {
		_, err := db.Exec(
			"INSERT INTO ecom_products (name, price, sku, description, stock) VALUES (?, ?, ?, ?, ?)",
			p.name, p.price, p.sku, p.description, p.stock,
		)
		assert.NoError(t, err, "Không nên có lỗi khi thêm sản phẩm test")
	}
}

// Hàm helper để setup một sản phẩm test
func setupTestProduct(t *testing.T, db *sql.DB) uint {
	result, err := db.Exec(
		"INSERT INTO ecom_products (name, price, sku, description, stock) VALUES (?, ?, ?, ?, ?)",
		"Sản phẩm Test", 100000.00, "TEST-SKU-"+time.Now().Format("20060102150405"), "Sản phẩm dùng cho test", 20,
	)
	assert.NoError(t, err, "Không nên có lỗi khi thêm sản phẩm test")

	id, err := result.LastInsertId()
	assert.NoError(t, err, "Không nên có lỗi khi lấy ID sản phẩm vừa thêm")

	return uint(id)
}
