package service

import (
	"context"
	"testing"
	"time"

	"wnapi/modules/product/repository"
	"wnapi/modules/product/service"
	"wnapi/modules/product/tests/integration"
)

type ProductInput struct {
	Name        string  `json:"name"`
	Price       float64 `json:"price"`
	SKU         string  `json:"sku"`
	Description string  `json:"description"`
	Stock       int     `json:"stock"`
}

func TestGetProducts(t *testing.T) {
	// Khởi tạo service
	svc := getProductService(t)

	// Thực thi
	ctx := context.Background()
	limit := 10
	products, nextCursor, err := svc.GetProducts(ctx, &limit, nil)

	// Kiểm tra kết quả
	if err != nil {
		t.Fatalf("Lỗi khi lấy danh sách sản phẩm: %v", err)
	}

	if len(products) == 0 {
		t.Log("Danh sách sản phẩm trống")
	} else {
		t.Logf("Tìm thấy %d sản phẩm", len(products))
	}

	if len(products) == limit {
		if nextCursor == "" {
			t.<PERSON>("nextCursor nên có giá trị khi đạt giới hạn limit")
		}
	}
}

func TestGetProductByID(t *testing.T) {
	// Khởi tạo service
	svc := getProductService(t)

	// Tạo sản phẩm mới để lấy
	ctx := context.Background()
	input := ProductInput{
		Name:        "Sản phẩm test service",
		Price:       299000.00,
		SKU:         "TEST-SVC-" + time.Now().Format("20060102150405"),
		Description: "Sản phẩm test cho service layer",
		Stock:       25,
	}

	createdProduct, err := svc.CreateProduct(ctx, input)
	if err != nil {
		t.Fatalf("Lỗi khi tạo sản phẩm test: %v", err)
	}

	// Thực thi
	product, err := svc.GetProductByID(ctx, createdProduct.ProductID)

	// Kiểm tra kết quả
	if err != nil {
		t.Fatalf("Lỗi khi lấy sản phẩm theo ID: %v", err)
	}

	if product == nil {
		t.Fatal("Sản phẩm không được tìm thấy")
	}

	if product.ProductID != createdProduct.ProductID {
		t.Errorf("ID sản phẩm không khớp: mong đợi %v, nhận được %v", createdProduct.ProductID, product.ProductID)
	}

	if product.Name != input.Name {
		t.Errorf("Tên sản phẩm không khớp: mong đợi %s, nhận được %s", input.Name, product.Name)
	}

	if product.Price != input.Price {
		t.Errorf("Giá sản phẩm không khớp: mong đợi %.2f, nhận được %.2f", input.Price, product.Price)
	}
}

func TestCreateProduct(t *testing.T) {
	// Khởi tạo service
	svc := getProductService(t)

	// Chuẩn bị input
	input := ProductInput{
		Name:        "Sản phẩm mới tạo",
		Price:       399000.00,
		SKU:         "CREATE-TEST-" + time.Now().Format("20060102150405"),
		Description: "Sản phẩm mới tạo thông qua service",
		Stock:       15,
	}

	// Thực thi
	ctx := context.Background()
	product, err := svc.CreateProduct(ctx, input)

	// Kiểm tra kết quả
	if err != nil {
		t.Fatalf("Lỗi khi tạo sản phẩm: %v", err)
	}

	if product.ProductID == 0 {
		t.Error("ID sản phẩm không được tạo")
	}

	if product.Name != input.Name {
		t.Errorf("Tên sản phẩm không khớp: mong đợi %s, nhận được %s", input.Name, product.Name)
	}

	if product.Price != input.Price {
		t.Errorf("Giá sản phẩm không khớp: mong đợi %.2f, nhận được %.2f", input.Price, product.Price)
	}

	if product.SKU != input.SKU {
		t.Errorf("SKU sản phẩm không khớp: mong đợi %s, nhận được %s", input.SKU, product.SKU)
	}

	// Kiểm tra sản phẩm đã được lưu trong database
	savedProduct, err := svc.GetProductByID(ctx, product.ProductID)
	if err != nil {
		t.Fatalf("Lỗi khi lấy sản phẩm vừa tạo: %v", err)
	}

	if savedProduct.ProductID != product.ProductID {
		t.Errorf("ID sản phẩm không khớp sau khi lưu: mong đợi %v, nhận được %v", product.ProductID, savedProduct.ProductID)
	}
}

func TestUpdateProduct(t *testing.T) {
	// Khởi tạo service
	svc := getProductService(t)

	// Tạo sản phẩm để cập nhật
	ctx := context.Background()
	createInput := ProductInput{
		Name:        "Sản phẩm cần cập nhật",
		Price:       450000.00,
		SKU:         "UPDATE-SVC-" + time.Now().Format("20060102150405"),
		Description: "Sản phẩm test cho cập nhật service",
		Stock:       30,
	}

	product, err := svc.CreateProduct(ctx, createInput)
	if err != nil {
		t.Fatalf("Lỗi khi tạo sản phẩm test: %v", err)
	}

	// Chuẩn bị dữ liệu cập nhật
	updateInput := ProductInput{
		Name:        "Sản phẩm đã cập nhật",
		Price:       499000.00,
		SKU:         product.SKU,
		Description: "Mô tả đã được cập nhật thông qua service",
		Stock:       25,
	}

	// Thực thi
	updatedProduct, err := svc.UpdateProduct(ctx, product.ProductID, updateInput)

	// Kiểm tra kết quả
	if err != nil {
		t.Fatalf("Lỗi khi cập nhật sản phẩm: %v", err)
	}

	if updatedProduct.ProductID != product.ProductID {
		t.Errorf("ID sản phẩm thay đổi sau khi cập nhật: mong đợi %v, nhận được %v", product.ProductID, updatedProduct.ProductID)
	}

	if updatedProduct.Name != updateInput.Name {
		t.Errorf("Tên sản phẩm không được cập nhật: mong đợi %s, nhận được %s", updateInput.Name, updatedProduct.Name)
	}

	if updatedProduct.Price != updateInput.Price {
		t.Errorf("Giá sản phẩm không được cập nhật: mong đợi %.2f, nhận được %.2f", updateInput.Price, updatedProduct.Price)
	}

	if updatedProduct.Description != updateInput.Description {
		t.Errorf("Mô tả sản phẩm không được cập nhật: mong đợi %s, nhận được %s", updateInput.Description, updatedProduct.Description)
	}

	if updatedProduct.Stock != updateInput.Stock {
		t.Errorf("Tồn kho sản phẩm không được cập nhật: mong đợi %d, nhận được %d", updateInput.Stock, updatedProduct.Stock)
	}
}

func TestDeleteProduct(t *testing.T) {
	// Khởi tạo service
	svc := getProductService(t)

	// Tạo sản phẩm để xóa
	ctx := context.Background()
	input := ProductInput{
		Name:        "Sản phẩm để xóa",
		Price:       199000.00,
		SKU:         "DELETE-SVC-" + time.Now().Format("20060102150405"),
		Description: "Sản phẩm test để xóa qua service",
		Stock:       10,
	}

	product, err := svc.CreateProduct(ctx, input)
	if err != nil {
		t.Fatalf("Lỗi khi tạo sản phẩm test: %v", err)
	}

	// Thực thi
	err = svc.DeleteProduct(ctx, product.ProductID)

	// Kiểm tra kết quả
	if err != nil {
		t.Fatalf("Lỗi khi xóa sản phẩm: %v", err)
	}

	// Kiểm tra sản phẩm đã bị xóa
	_, err = svc.GetProductByID(ctx, product.ProductID)
	if err == nil {
		t.Error("Sản phẩm vẫn được tìm thấy sau khi xóa")
	}
}

// Helper function để khởi tạo product service
func getProductService(t *testing.T) service.ProductService {
	db := integration.GetTestDB(t)
	productRepo := repository.NewProductRepository(db)
	return service.NewProductService(productRepo)
}
