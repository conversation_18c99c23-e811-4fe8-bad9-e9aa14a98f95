package internal

import (
	"context"
	"net/http"
	"time"
)

// NotificationConfig chứa cấu hình notification service
type NotificationConfig struct {
	EmailHost     string        `yaml:"email_host" env:"EMAIL_HOST"`
	EmailPort     int           `yaml:"email_port" env:"EMAIL_PORT" envDefault:"587"`
	EmailUsername string        `yaml:"email_username" env:"EMAIL_USERNAME"`
	EmailPassword string        `yaml:"email_password" env:"EMAIL_PASSWORD"`
	EmailFrom     string        `yaml:"email_from" env:"EMAIL_FROM"`
	SMSProvider   string        `yaml:"sms_provider" env:"SMS_PROVIDER" envDefault:"twilio"`
	SMSEnabled    bool          `yaml:"sms_enabled" env:"SMS_ENABLED" envDefault:"false"`
	PushEnabled   bool          `yaml:"push_enabled" env:"PUSH_ENABLED" envDefault:"false"`
	QueueEnabled  bool          `yaml:"queue_enabled" env:"QUEUE_ENABLED" envDefault:"false"`
	CacheEnabled  bool          `yaml:"cache_enabled" env:"CACHE_ENABLED" envDefault:"false"`
	RedisAddr     string        `yaml:"redis_addr" env:"REDIS_ADDR" envDefault:"localhost:6379"`
	CacheTTL      int           `yaml:"cache_ttl" env:"CACHE_TTL" envDefault:"300"`
	RetryAttempts int           `yaml:"retry_attempts" env:"RETRY_ATTEMPTS" envDefault:"3"`
	RetryDelay    time.Duration `yaml:"retry_delay" env:"RETRY_DELAY" envDefault:"5s"`
	Message       string        `env:"MESSAGE" envDefault:"Xin chào từ module Notification!"`
}

// Notification đại diện cho thông báo
type Notification struct {
	ID        int       `db:"id" json:"id"`
	TenantID  int       `db:"tenant_id" json:"tenant_id"`
	UserID    int       `db:"user_id" json:"user_id"`
	Title     string    `db:"title" json:"title"`
	Content   string    `db:"content" json:"content"`
	Type      string    `db:"type" json:"type"`
	Status    string    `db:"status" json:"status"`
	IsRead    bool      `db:"is_read" json:"is_read"`
	SentAt    time.Time `db:"sent_at" json:"sent_at"`
	CreatedAt time.Time `db:"created_at" json:"created_at"`
	UpdatedAt time.Time `db:"updated_at" json:"updated_at"`
}

// Template đại diện cho mẫu thông báo
type Template struct {
	ID          int       `db:"id" json:"id"`
	TenantID    int       `db:"tenant_id" json:"tenant_id"`
	Code        string    `db:"code" json:"code"`
	Name        string    `db:"name" json:"name"`
	Description string    `db:"description" json:"description"`
	Content     string    `db:"content" json:"content"`
	Type        string    `db:"type" json:"type"`
	Status      string    `db:"status" json:"status"`
	CreatedAt   time.Time `db:"created_at" json:"created_at"`
	UpdatedAt   time.Time `db:"updated_at" json:"updated_at"`
}

// Channel đại diện cho kênh thông báo
type Channel struct {
	ID          int       `db:"id" json:"id"`
	TenantID    int       `db:"tenant_id" json:"tenant_id"`
	Code        string    `db:"code" json:"code"`
	Name        string    `db:"name" json:"name"`
	Description string    `db:"description" json:"description"`
	Type        string    `db:"type" json:"type"`
	Config      string    `db:"config" json:"config"`
	Status      string    `db:"status" json:"status"`
	CreatedAt   time.Time `db:"created_at" json:"created_at"`
	UpdatedAt   time.Time `db:"updated_at" json:"updated_at"`
}

// Preference đại diện cho tùy chọn thông báo của người dùng
type Preference struct {
	ID         int       `db:"id" json:"id"`
	TenantID   int       `db:"tenant_id" json:"tenant_id"`
	UserID     int       `db:"user_id" json:"user_id"`
	ChannelID  int       `db:"channel_id" json:"channel_id"`
	Type       string    `db:"type" json:"type"`
	IsEnabled  bool      `db:"is_enabled" json:"is_enabled"`
	CustomData string    `db:"custom_data" json:"custom_data"`
	CreatedAt  time.Time `db:"created_at" json:"created_at"`
	UpdatedAt  time.Time `db:"updated_at" json:"updated_at"`
}

// Delivery đại diện cho việc gửi thông báo
type Delivery struct {
	ID             int       `db:"id" json:"id"`
	TenantID       int       `db:"tenant_id" json:"tenant_id"`
	NotificationID int       `db:"notification_id" json:"notification_id"`
	ChannelID      int       `db:"channel_id" json:"channel_id"`
	UserID         int       `db:"user_id" json:"user_id"`
	Status         string    `db:"status" json:"status"`
	ErrorMessage   string    `db:"error_message" json:"error_message"`
	SentAt         time.Time `db:"sent_at" json:"sent_at"`
	CreatedAt      time.Time `db:"created_at" json:"created_at"`
	UpdatedAt      time.Time `db:"updated_at" json:"updated_at"`
}

// Telegram đại diện cho cấu hình Telegram của người dùng
type Telegram struct {
	ID         int       `db:"id" json:"id"`
	TenantID   int       `db:"tenant_id" json:"tenant_id"`
	UserID     int       `db:"user_id" json:"user_id"`
	ChatID     string    `db:"chat_id" json:"chat_id"`
	Username   string    `db:"username" json:"username"`
	FirstName  string    `db:"first_name" json:"first_name"`
	LastName   string    `db:"last_name" json:"last_name"`
	IsVerified bool      `db:"is_verified" json:"is_verified"`
	CreatedAt  time.Time `db:"created_at" json:"created_at"`
	UpdatedAt  time.Time `db:"updated_at" json:"updated_at"`
}

// Websocket đại diện cho kết nối websocket
type Websocket struct {
	ID          string    `db:"id" json:"id"`
	TenantID    int       `db:"tenant_id" json:"tenant_id"`
	UserID      int       `db:"user_id" json:"user_id"`
	DeviceID    string    `db:"device_id" json:"device_id"`
	IPAddress   string    `db:"ip_address" json:"ip_address"`
	UserAgent   string    `db:"user_agent" json:"user_agent"`
	LastActive  time.Time `db:"last_active" json:"last_active"`
	IsConnected bool      `db:"is_connected" json:"is_connected"`
	CreatedAt   time.Time `db:"created_at" json:"created_at"`
	UpdatedAt   time.Time `db:"updated_at" json:"updated_at"`
}

// ServiceError định nghĩa các lỗi service
type ServiceError string

const (
	// ErrInvalidEmail là lỗi khi email không hợp lệ
	ErrInvalidEmail ServiceError = "invalid_email"
	// ErrInvalidPhone là lỗi khi số điện thoại không hợp lệ
	ErrInvalidPhone ServiceError = "invalid_phone"
	// ErrSendFailed là lỗi khi gửi thông báo thất bại
	ErrSendFailed ServiceError = "send_failed"
	// ErrInvalidTemplate là lỗi khi template không hợp lệ
	ErrInvalidTemplate ServiceError = "invalid_template"
	// ErrInvalidRecipient là lỗi khi người nhận không hợp lệ
	ErrInvalidRecipient ServiceError = "invalid_recipient"
	// ErrNotificationNotFound là lỗi khi không tìm thấy thông báo
	ErrNotificationNotFound ServiceError = "notification_not_found"
	// ErrTemplateNotFound là lỗi khi không tìm thấy mẫu thông báo
	ErrTemplateNotFound ServiceError = "template_not_found"
	// ErrChannelNotFound là lỗi khi không tìm thấy kênh thông báo
	ErrChannelNotFound ServiceError = "channel_not_found"
	// ErrPreferenceNotFound là lỗi khi không tìm thấy tùy chọn thông báo
	ErrPreferenceNotFound ServiceError = "preference_not_found"
	// ErrDeliveryNotFound là lỗi khi không tìm thấy gửi thông báo
	ErrDeliveryNotFound ServiceError = "delivery_not_found"
	// ErrTelegramNotFound là lỗi khi không tìm thấy cấu hình Telegram
	ErrTelegramNotFound ServiceError = "telegram_not_found"
	// ErrWebsocketNotFound là lỗi khi không tìm thấy kết nối websocket
	ErrWebsocketNotFound ServiceError = "websocket_not_found"
	// ErrUserNotFound là lỗi khi không tìm thấy người dùng
	ErrUserNotFound ServiceError = "user_not_found"
	// ErrInvalidRequest là lỗi khi yêu cầu không hợp lệ
	ErrInvalidRequest ServiceError = "invalid_request"
	// ErrInvalidChannel là lỗi khi kênh thông báo không hợp lệ
	ErrInvalidChannel ServiceError = "invalid_channel"
)

func (e ServiceError) Error() string {
	return string(e)
}

// ErrorResponse định nghĩa cấu trúc phản hồi lỗi
type ErrorResponse struct {
	StatusCode int    // HTTP status code
	Message    string // Thông báo lỗi hiển thị cho người dùng
	ErrorCode  string // Mã lỗi nội bộ
}

// ErrorMap ánh xạ ServiceError với thông tin phản hồi lỗi tương ứng
var ErrorMap = map[ServiceError]ErrorResponse{
	ErrNotificationNotFound: {
		StatusCode: http.StatusNotFound,
		Message:    "Không tìm thấy thông báo",
		ErrorCode:  "NOTIFICATION_NOT_FOUND",
	},
	ErrTemplateNotFound: {
		StatusCode: http.StatusNotFound,
		Message:    "Không tìm thấy mẫu thông báo",
		ErrorCode:  "TEMPLATE_NOT_FOUND",
	},
	ErrChannelNotFound: {
		StatusCode: http.StatusNotFound,
		Message:    "Không tìm thấy kênh thông báo",
		ErrorCode:  "CHANNEL_NOT_FOUND",
	},
	// Các lỗi khác...
}

// GetErrorResponse trả về thông tin phản hồi lỗi dựa trên ServiceError
func GetErrorResponse(err error) ErrorResponse {
	if serviceErr, ok := err.(ServiceError); ok {
		if resp, exists := ErrorMap[serviceErr]; exists {
			return resp
		}
	}

	// Mặc định trả về lỗi hệ thống nếu không tìm thấy lỗi trong map
	return ErrorResponse{
		StatusCode: http.StatusInternalServerError,
		Message:    "Lỗi hệ thống",
		ErrorCode:  "INTERNAL_ERROR",
	}
}

// Repository định nghĩa interface chính cho repository
type Repository interface {
	Notification() NotificationRepository
	Template() TemplateRepository
	Channel() ChannelRepository
	Preference() PreferenceRepository
	Delivery() DeliveryRepository
	Telegram() TelegramRepository
	Websocket() WebsocketRepository
}

// NotificationRepository xử lý thao tác với bảng notifications
type NotificationRepository interface {
	GetNotificationByID(ctx context.Context, id int) (*Notification, error)
	CreateNotification(ctx context.Context, notification *Notification) (int, error)
	UpdateNotification(ctx context.Context, notification *Notification) error
	DeleteNotification(ctx context.Context, id int) error
	ListNotifications(ctx context.Context, filter interface{}) ([]*Notification, error)
	MarkAsRead(ctx context.Context, id int) error
}

// TemplateRepository xử lý thao tác với bảng notification_templates
type TemplateRepository interface {
	GetTemplateByID(ctx context.Context, id int) (*Template, error)
	CreateTemplate(ctx context.Context, template *Template) (int, error)
	UpdateTemplate(ctx context.Context, template *Template) error
	DeleteTemplate(ctx context.Context, id int) error
	ListTemplates(ctx context.Context, filter interface{}) ([]*Template, error)
}

// ChannelRepository xử lý thao tác với bảng notification_channels
type ChannelRepository interface {
	GetChannelByID(ctx context.Context, id int) (*Channel, error)
	CreateChannel(ctx context.Context, channel *Channel) (int, error)
	UpdateChannel(ctx context.Context, channel *Channel) error
	DeleteChannel(ctx context.Context, id int) error
	ListChannels(ctx context.Context, filter interface{}) ([]*Channel, error)
}

// PreferenceRepository xử lý thao tác với bảng notification_preferences
type PreferenceRepository interface {
	GetPreferenceByID(ctx context.Context, id int) (*Preference, error)
	GetPreferenceByUserID(ctx context.Context, userID int) (*Preference, error)
	CreatePreference(ctx context.Context, preference *Preference) (int, error)
	UpdatePreference(ctx context.Context, preference *Preference) error
	DeletePreference(ctx context.Context, id int) error
	ListPreferences(ctx context.Context, filter interface{}) ([]*Preference, error)
}

// DeliveryRepository xử lý thao tác với bảng notification_deliveries
type DeliveryRepository interface {
	GetDeliveryByID(ctx context.Context, id int) (*Delivery, error)
	CreateDelivery(ctx context.Context, delivery *Delivery) (int, error)
	UpdateDelivery(ctx context.Context, delivery *Delivery) error
	DeleteDelivery(ctx context.Context, id int) error
	ListDeliveries(ctx context.Context, filter interface{}) ([]*Delivery, error)
}

// TelegramRepository xử lý thao tác với bảng notification_telegram
type TelegramRepository interface {
	GetTelegramByID(ctx context.Context, id int) (*Telegram, error)
	GetTelegramByUserID(ctx context.Context, userID int) (*Telegram, error)
	CreateTelegram(ctx context.Context, telegram *Telegram) (int, error)
	UpdateTelegram(ctx context.Context, telegram *Telegram) error
	DeleteTelegram(ctx context.Context, id int) error
	ListTelegrams(ctx context.Context, filter interface{}) ([]*Telegram, error)
}

// WebsocketRepository xử lý thao tác với bảng notification_websockets
type WebsocketRepository interface {
	GetWebsocketByID(ctx context.Context, id string) (*Websocket, error)
	CreateWebsocket(ctx context.Context, websocket *Websocket) (string, error)
	UpdateWebsocket(ctx context.Context, websocket *Websocket) error
	DeleteWebsocket(ctx context.Context, id string) error
	ListWebsockets(ctx context.Context, filter interface{}) ([]*Websocket, error)
}

// NotificationService định nghĩa interface cho notification service
type NotificationService interface {
	CreateNotification(ctx context.Context, notification interface{}) (int, error)
	GetNotification(ctx context.Context, notificationID int) (interface{}, error)
	GetUserNotifications(ctx context.Context, userID int, cursor string, limit int) ([]interface{}, string, error)
	MarkAsRead(ctx context.Context, notificationID int) error
	MarkAllAsRead(ctx context.Context, userID int) error
	GetUnreadCount(ctx context.Context, userID int) (int, error)
	DeleteNotification(ctx context.Context, notificationID int) error
}

// NotificationInfo chứa thông tin về thông báo đã gửi
type NotificationInfo struct {
	ID            string                 `json:"id"`
	Type          string                 `json:"type"`
	Recipient     string                 `json:"recipient"`
	Subject       string                 `json:"subject,omitempty"`
	Content       string                 `json:"content"`
	Status        string                 `json:"status"`
	SentAt        time.Time              `json:"sent_at"`
	DeliveredAt   *time.Time             `json:"delivered_at,omitempty"`
	FailureReason string                 `json:"failure_reason,omitempty"`
	Metadata      map[string]interface{} `json:"metadata,omitempty"`
}
