package internal

import (
	"fmt"
	"log"
	"os"
	"time"

	"github.com/caarlos0/env/v11"
	"github.com/joho/godotenv"
)

// LoadNotificationConfig đọc cấu hình notification từ biến môi trường
func LoadNotificationConfig() (*NotificationConfig, error) {
	// Tải file .env nếu có
	if _, err := os.Stat(".env"); err == nil {
		if err := godotenv.Load(); err != nil {
			log.Printf("Cảnh báo: không thể tải file .env: %v", err)
		}
	}

	// Khởi tạo config mặc định
	cfg := &NotificationConfig{
		EmailHost:     "smtp.gmail.com",
		EmailPort:     587,
		EmailUsername: "",
		EmailPassword: "",
		EmailFrom:     "<EMAIL>",
		SMSProvider:   "twilio",
		SMSEnabled:    false,
		PushEnabled:   false,
		QueueEnabled:  false,
		CacheEnabled:  false,
		RedisAddr:     "localhost:6379",
		CacheTTL:      300,
		RetryAttempts: 3,
		RetryDelay:    5 * time.Second,
		Message:       "Xin chào từ module Notification!",
	}

	// Đọc cấu hình từ biến môi trường với prefix NOTIFICATION_
	opts := env.Options{
		Prefix: "NOTIFICATION_",
	}
	if err := env.ParseWithOptions(cfg, opts); err != nil {
		return nil, fmt.Errorf("lỗi đọc cấu hình notification từ biến môi trường: %w", err)
	}

	return cfg, nil
}
