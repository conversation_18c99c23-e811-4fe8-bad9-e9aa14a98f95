package api

import (
	"wnapi/internal/core"
	"wnapi/modules/notification/api/handlers"
	"wnapi/modules/notification/internal"

	"github.com/gin-gonic/gin"
)

// Handler là đối tượng chính xử lý API cho module notification
type Handler struct {
	notificationHandler *handlers.SimpleNotificationHandler
}

// NewHandler tạo một handler mới
func NewHandler(notificationService internal.NotificationService) *Handler {
	return &Handler{
		notificationHandler: handlers.NewSimpleNotificationHandler(notificationService),
	}
}

// RegisterRoutes đăng ký tất cả routes cho module notification
func (h *Handler) RegisterRoutes(server *core.Server) error {
	// API Group
	apiGroup := server.Group("/api/v1/notifications")

	// Health check endpoint
	apiGroup.GET("/health", h.healthCheck)

	// Protected routes (có thể thêm JWT middleware sau)
	protectedRoutes := apiGroup.Group("")
	protectedRoutes.Use(func(c *gin.Context) {
		// TODO: Sử dụng middleware JWT từ server khi nó được định nghĩa
		// Tạm thời cho phép mọi request đi qua
		c.Next()
	})

	// Notification API
	protectedRoutes.GET("", h.notificationHandler.GetNotifications)
	protectedRoutes.GET("/:id", h.notificationHandler.GetNotification)
	protectedRoutes.POST("", h.notificationHandler.CreateNotification)
	protectedRoutes.DELETE("/:id", h.notificationHandler.DeleteNotification)

	// Read status API
	protectedRoutes.PUT("/:id/read", h.notificationHandler.MarkAsRead)
	protectedRoutes.PUT("/users/:user_id/read-all", h.notificationHandler.MarkAllAsRead)

	// Count API
	protectedRoutes.GET("/users/:user_id/unread-count", h.notificationHandler.GetUnreadCount)

	return nil
}

// healthCheck là endpoint kiểm tra trạng thái hoạt động của module
func (h *Handler) healthCheck(c *gin.Context) {
	c.JSON(200, gin.H{
		"status": "ok",
		"module": "notification",
	})
}
