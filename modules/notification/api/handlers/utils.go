package handlers

import (
	"net/http"

	"wnapi/internal/pkg/response"

	"github.com/gin-gonic/gin"
)

// Constants for error codes
const (
	ErrCodeUnauthorized      = "UNAUTHORIZED"
	ErrCodeInvalidUserID     = "INVALID_USER_ID"
	ErrCodeInvalidRequest    = "INVALID_REQUEST"
	ErrCodeNotFound          = "NOTIFICATION_NOT_FOUND"
	ErrCodeResourceNotFound  = "RESOURCE_NOT_FOUND"
	ErrCodeForbidden         = "FORBIDDEN"
	ErrCodeServerError       = "INTERNAL_SERVER_ERROR"
	ErrCodeRateLimitExceeded = "RATE_LIMIT_EXCEEDED"
)

// apiSuccess sends a successful API response
func apiSuccess(c *gin.Context, statusCode int, message string, data interface{}) {
	response.Success(c, data, nil)
}

// apiSuccessWithMeta sends a successful API response with metadata
func apiSuccessWithMeta(c *gin.Context, statusCode int, message string, data interface{}, meta map[string]interface{}) {
	response.Success(c, data, meta)
}

// apiError sends an error API response
func apiError(c *gin.Context, statusCode int, errorCode string, message string) {
	response.Error(c, statusCode, message, errorCode)
}

// apiErrorWithDetails sends an error API response with detailed information
func apiErrorWithDetails(c *gin.Context, statusCode int, errorCode string, message string, details interface{}) {
	response.ErrorWithDetails(c, statusCode, message, errorCode, details)
}

// handleUnauthorized handles unauthorized errors
func handleUnauthorized(c *gin.Context) {
	apiError(c, http.StatusUnauthorized, ErrCodeUnauthorized, "Unauthorized")
}

// handleInvalidUserID handles invalid user ID errors
func handleInvalidUserID(c *gin.Context) {
	details := map[string]string{"message": "User ID không hợp lệ"}
	response.ErrorWithDetails(c, http.StatusBadRequest, "User ID không hợp lệ", ErrCodeInvalidUserID, details)
}

// handleInvalidNotificationID handles invalid notification ID errors
func handleInvalidNotificationID(c *gin.Context) {
	details := map[string]string{"message": "Notification ID không hợp lệ"}
	response.ErrorWithDetails(c, http.StatusBadRequest, "Notification ID không hợp lệ", ErrCodeInvalidRequest, details)
}

// handleNotificationNotFound handles notification not found errors
func handleNotificationNotFound(c *gin.Context) {
	details := map[string]string{"message": "Không tìm thấy thông báo"}
	response.ErrorWithDetails(c, http.StatusNotFound, "Không tìm thấy thông báo", ErrCodeNotFound, details)
}

// handleServerError handles server errors
func handleServerError(c *gin.Context, message string) {
	details := map[string]string{"message": message}
	response.ErrorWithDetails(c, http.StatusInternalServerError, message, ErrCodeServerError, details)
}
