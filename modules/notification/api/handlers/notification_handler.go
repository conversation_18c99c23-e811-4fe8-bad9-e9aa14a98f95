package handlers

import (
	"fmt"
	"net/http"
	"strconv"
	"time"

	"wnapi/modules/notification/dto"
	"wnapi/modules/notification/dto/notification"
	"wnapi/modules/notification/models"
	"wnapi/modules/notification/service"

	"github.com/gin-gonic/gin"
)

// NotificationHandler handles HTTP requests for notifications
type NotificationHandler struct {
	service service.NotificationService
}

// NewNotificationHandler creates a new notification handler
func NewNotificationHandler(service service.NotificationService) *NotificationHandler {
	return &NotificationHandler{
		service: service,
	}
}

// SendEmail handles email sending requests
func (h *NotificationHandler) SendEmail(c *gin.Context) {
	var req dto.EmailRequest
	if err := c.Should<PERSON>ind<PERSON>(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Invalid request body",
			"error":   err.Error(),
		})
		return
	}

	// Validate required fields
	if req.To == "" || req.Subject == "" || req.Body == "" {
		c.J<PERSON>(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Missing required fields: to, subject, and body are required",
		})
		return
	}

	response, err := h.service.SendEmail(c.Request.Context(), req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to send email",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Email sent successfully",
		"data":    response,
	})
}

// SendSMS handles SMS sending requests
func (h *NotificationHandler) SendSMS(c *gin.Context) {
	var req dto.SMSRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Invalid request body",
			"error":   err.Error(),
		})
		return
	}

	// Validate required fields
	if req.To == "" || req.Message == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Missing required fields: to and message are required",
		})
		return
	}

	response, err := h.service.SendSMS(c.Request.Context(), req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to send SMS",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "SMS sent successfully",
		"data":    response,
	})
}

// SendPushNotification handles push notification requests
func (h *NotificationHandler) SendPushNotification(c *gin.Context) {
	var req dto.PushRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Invalid request body",
			"error":   err.Error(),
		})
		return
	}

	// Validate required fields
	if req.UserID == "" || req.Title == "" || req.Body == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Missing required fields: userID, title, and body are required",
		})
		return
	}

	response, err := h.service.SendPushNotification(c.Request.Context(), req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to send push notification",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Push notification sent successfully",
		"data":    response,
	})
}

// GetNotificationHistory retrieves notification history for a user
func (h *NotificationHandler) GetNotificationHistory(c *gin.Context) {
	userID := c.Param("userID")
	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "User ID is required",
		})
		return
	}

	history, err := h.service.GetNotificationHistory(c.Request.Context(), userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to retrieve notification history",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Notification history retrieved successfully",
		"data":    history,
	})
	filters := make(map[string]interface{})
	if isReadStr := c.Query("is_read"); isReadStr != "" {
		isRead := isReadStr == "true"
		filters["is_read"] = isRead
	}
	if notificationType := c.Query("type"); notificationType != "" {
		filters["notification_type"] = notificationType
	}

	// Get notifications from service
	notifications, nextCursor, err := h.service.GetUserNotifications(c.Request.Context(), userID, cursor, limit, filters)
	if err != nil {
		handleServerError(c, "Failed to retrieve notifications")
		return
	}

	// Convert to DTOs
	var notificationDTOs []notification.NotificationResponse
	for _, n := range notifications {
		notificationDTOs = append(notificationDTOs, notification.NotificationResponse{
			ID:               n.NotificationID,
			Title:            n.Title,
			Content:          n.Content,
			NotificationType: n.NotificationType,
			ReferenceType:    n.ReferenceType,
			ReferenceID:      n.ReferenceID,
			IsRead:           n.IsRead,
			CreatedAt:        n.CreatedAt,
		})
	}

	// Return response
	meta := map[string]interface{}{
		"next_cursor": nextCursor,
		"has_more":    nextCursor != "",
	}
	apiSuccessWithMeta(c, http.StatusOK, "Notifications retrieved successfully", notificationDTOs, meta)
}

// Get returns a single notification
func (h *NotificationHandler) Get(c *gin.Context) {
	// Get user ID from context (set by auth middleware)
	userIDStr, exists := c.Get("userID")
	if !exists {
		handleUnauthorized(c)
		return
	}

	userID, err := strconv.Atoi(userIDStr.(string))
	if err != nil {
		handleInvalidUserID(c)
		return
	}

	// Parse notification ID from path
	notificationIDStr := c.Param("id")
	notificationID, err := strconv.Atoi(notificationIDStr)
	if err != nil {
		handleInvalidNotificationID(c)
		return
	}

	// Get notification from service
	n, err := h.service.GetNotification(c.Request.Context(), notificationID, userID)
	if err != nil {
		handleServerError(c, "Failed to retrieve notification")
		return
	}

	if n == nil {
		handleNotificationNotFound(c)
		return
	}

	// Convert to DTO
	notificationDTO := notification.NotificationResponse{
		ID:               n.NotificationID,
		Title:            n.Title,
		Content:          n.Content,
		NotificationType: n.NotificationType,
		ReferenceType:    n.ReferenceType,
		ReferenceID:      n.ReferenceID,
		IsRead:           n.IsRead,
		CreatedAt:        n.CreatedAt,
	}

	// Return response
	apiSuccess(c, http.StatusOK, "Notification retrieved successfully", notificationDTO)
}

// Create adds a new notification
func (h *NotificationHandler) Create(c *gin.Context) {
	// Check if user is admin (set by admin auth middleware)
	_, exists := c.Get("userID")
	if !exists {
		handleUnauthorized(c)
		return
	}

	// Parse request body
	var req notification.CreateNotificationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		apiErrorWithDetails(c, http.StatusBadRequest, ErrCodeInvalidRequest, "Invalid request data", details)
		return
	}

	// Validate request
	if req.UserID <= 0 {
		details := []interface{}{map[string]string{"field": "user_id", "message": "User ID is required"}}
		apiErrorWithDetails(c, http.StatusBadRequest, ErrCodeInvalidRequest, "Invalid request data", details)
		return
	}

	if req.Title == "" {
		details := []interface{}{map[string]string{"field": "title", "message": "Title is required"}}
		apiErrorWithDetails(c, http.StatusBadRequest, ErrCodeInvalidRequest, "Invalid request data", details)
		return
	}

	// Create notification model
	newNotification := &models.Notification{
		UserID:           req.UserID,
		Title:            req.Title,
		Content:          req.Content,
		NotificationType: req.NotificationType,
		ReferenceType:    req.ReferenceType,
		ReferenceID:      req.ReferenceID,
		IsRead:           false,
		CreatedAt:        time.Now(),
	}

	// Create notification in service
	notificationID, err := h.service.CreateNotification(c.Request.Context(), newNotification)
	if err != nil {
		handleServerError(c, "Failed to create notification")
		return
	}

	// Get the created notification
	createdNotification, err := h.service.GetNotification(c.Request.Context(), notificationID, int(req.UserID))
	if err != nil {
		// If we can't get the full notification, just return the ID
		response := notification.NotificationCreatedResponse{
			NotificationID: uint(notificationID),
		}
		apiSuccess(c, http.StatusCreated, "Notification created successfully", response)
		return
	}

	// Return response with full notification details
	notificationDTO := notification.NotificationResponse{
		ID:               createdNotification.NotificationID,
		UserID:           createdNotification.UserID,
		Title:            createdNotification.Title,
		Content:          createdNotification.Content,
		NotificationType: createdNotification.NotificationType,
		ReferenceType:    createdNotification.ReferenceType,
		ReferenceID:      createdNotification.ReferenceID,
		IsRead:           createdNotification.IsRead,
		IsSent:           createdNotification.IsSent,
		CreatedAt:        createdNotification.CreatedAt,
		UpdatedAt:        createdNotification.UpdatedAt,
	}

	apiSuccess(c, http.StatusCreated, "Notification created successfully", notificationDTO)
}

// Update updates an existing notification, primarily marking as read
func (h *NotificationHandler) Update(c *gin.Context) {
	// Get notification ID from path
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		details := []interface{}{map[string]string{"field": "id", "message": "Invalid notification ID format"}}
		apiErrorWithDetails(c, http.StatusBadRequest, ErrCodeInvalidRequest, "Invalid notification ID", details)
		return
	}

	// Get user ID from context (set by auth middleware)
	userIDVal, exists := c.Get("userID")
	if !exists {
		handleUnauthorized(c)
		return
	}
	userIDStr, ok := userIDVal.(string)
	if !ok {
		handleUnauthorized(c)
		return
	}

	userID, err := strconv.Atoi(userIDStr)
	if err != nil {
		handleInvalidUserID(c)
		return
	}

	// Parse request body
	var req notification.UpdateNotificationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		apiErrorWithDetails(c, http.StatusBadRequest, ErrCodeInvalidRequest, "Invalid request data", details)
		return
	}

	// Get existing notification
	existingNotification, err := h.service.GetNotification(c.Request.Context(), id, userID)
	if err != nil {
		handleServerError(c, "Failed to retrieve notification")
		return
	}

	if existingNotification == nil {
		apiError(c, http.StatusNotFound, ErrCodeResourceNotFound, "Notification not found")
		return
	}

	// Check if user owns this notification
	if int(existingNotification.UserID) != userID {
		handleUnauthorized(c)
		return
	}

	// Update notification fields
	if req.Title != "" {
		existingNotification.Title = req.Title
	}
	if req.Content != "" {
		existingNotification.Content = req.Content
	}
	if req.NotificationType != "" {
		existingNotification.NotificationType = req.NotificationType
	}
	if req.ReferenceType != "" {
		existingNotification.ReferenceType = req.ReferenceType
	}
	if req.ReferenceID != "" {
		existingNotification.ReferenceID = req.ReferenceID
	}

	existingNotification.UpdatedAt = time.Now()

	// Update notification in service
	err = h.service.UpdateNotification(c.Request.Context(), existingNotification)
	if err != nil {
		handleServerError(c, "Failed to update notification")
		return
	}

	// Return response with updated notification details
	notificationDTO := notification.NotificationResponse{
		ID:               existingNotification.NotificationID,
		UserID:           existingNotification.UserID,
		Title:            existingNotification.Title,
		Content:          existingNotification.Content,
		NotificationType: existingNotification.NotificationType,
		ReferenceType:    existingNotification.ReferenceType,
		ReferenceID:      existingNotification.ReferenceID,
		IsRead:           existingNotification.IsRead,
		IsSent:           existingNotification.IsSent,
		CreatedAt:        existingNotification.CreatedAt,
		UpdatedAt:        existingNotification.UpdatedAt,
	}

	apiSuccess(c, http.StatusOK, "Notification updated successfully", notificationDTO)
}

// Delete deletes a notification
func (h *NotificationHandler) Delete(c *gin.Context) {
	// Get user ID from context (set by auth middleware)
	userIDStr, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"status": gin.H{
				"code":       http.StatusUnauthorized,
				"message":    "Unauthorized",
				"success":    false,
				"error_code": "UNAUTHORIZED",
				"path":       c.Request.URL.Path,
				"timestamp":  time.Now().Format(time.RFC3339),
			},
		})
		return
	}

	userID, err := strconv.Atoi(userIDStr.(string))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"status": gin.H{
				"code":       http.StatusBadRequest,
				"message":    "Invalid user ID",
				"success":    false,
				"error_code": "INVALID_USER_ID",
				"path":       c.Request.URL.Path,
				"timestamp":  time.Now().Format(time.RFC3339),
			},
		})
		return
	}

	// Parse notification ID from path
	notificationIDStr := c.Param("id")
	notificationID, err := strconv.Atoi(notificationIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"status": gin.H{
				"code":       http.StatusBadRequest,
				"message":    "Invalid notification ID",
				"success":    false,
				"error_code": "INVALID_NOTIFICATION_ID",
				"path":       c.Request.URL.Path,
				"timestamp":  time.Now().Format(time.RFC3339),
			},
		})
		return
	}

	// Check if notification exists and belongs to the user
	notification, err := h.service.GetNotification(c.Request.Context(), notificationID, userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"status": gin.H{
				"code":       http.StatusInternalServerError,
				"message":    "Failed to retrieve notification",
				"success":    false,
				"error_code": "INTERNAL_SERVER_ERROR",
				"path":       c.Request.URL.Path,
				"timestamp":  time.Now().Format(time.RFC3339),
			},
		})
		return
	}

	if notification == nil {
		c.JSON(http.StatusNotFound, gin.H{
			"status": gin.H{
				"code":       http.StatusNotFound,
				"message":    "Notification not found",
				"success":    false,
				"error_code": "NOTIFICATION_NOT_FOUND",
				"path":       c.Request.URL.Path,
				"timestamp":  time.Now().Format(time.RFC3339),
			},
		})
		return
	}

	// Delete notification
	err = h.service.DeleteNotification(c.Request.Context(), notificationID, userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"status": gin.H{
				"code":       http.StatusInternalServerError,
				"message":    "Failed to delete notification",
				"success":    false,
				"error_code": "INTERNAL_SERVER_ERROR",
				"path":       c.Request.URL.Path,
				"timestamp":  time.Now().Format(time.RFC3339),
			},
		})
		return
	}

	// Return response
	c.JSON(http.StatusOK, gin.H{
		"status": gin.H{
			"code":      http.StatusOK,
			"message":   "Notification deleted successfully",
			"success":   true,
			"path":      c.Request.URL.Path,
			"timestamp": time.Now().Format(time.RFC3339),
		},
	})
}

// MarkAsRead marks a notification as read
func (h *NotificationHandler) MarkAsRead(c *gin.Context) {
	// Get user ID from context (set by auth middleware)
	userIDStr, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"status": gin.H{
				"code":       http.StatusUnauthorized,
				"message":    "Unauthorized",
				"success":    false,
				"error_code": "UNAUTHORIZED",
				"path":       c.Request.URL.Path,
				"timestamp":  time.Now().Format(time.RFC3339),
			},
		})
		return
	}

	userID, err := strconv.Atoi(userIDStr.(string))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"status": gin.H{
				"code":       http.StatusBadRequest,
				"message":    "Invalid user ID",
				"success":    false,
				"error_code": "INVALID_USER_ID",
				"path":       c.Request.URL.Path,
				"timestamp":  time.Now().Format(time.RFC3339),
			},
		})
		return
	}

	// Parse notification ID from path
	notificationIDStr := c.Param("id")
	notificationID, err := strconv.Atoi(notificationIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"status": gin.H{
				"code":       http.StatusBadRequest,
				"message":    "Invalid notification ID",
				"success":    false,
				"error_code": "INVALID_NOTIFICATION_ID",
				"path":       c.Request.URL.Path,
				"timestamp":  time.Now().Format(time.RFC3339),
			},
		})
		return
	}

	// Mark notification as read
	err = h.service.MarkAsRead(c.Request.Context(), notificationID, userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"status": gin.H{
				"code":       http.StatusInternalServerError,
				"message":    "Failed to mark notification as read",
				"success":    false,
				"error_code": "INTERNAL_SERVER_ERROR",
				"path":       c.Request.URL.Path,
				"timestamp":  time.Now().Format(time.RFC3339),
			},
		})
		return
	}

	// Return response
	c.JSON(http.StatusOK, gin.H{
		"status": gin.H{
			"code":      http.StatusOK,
			"message":   "Notification marked as read",
			"success":   true,
			"path":      c.Request.URL.Path,
			"timestamp": time.Now().Format(time.RFC3339),
		},
	})
}

// MarkAllAsRead marks all notifications for a user as read
func (h *NotificationHandler) MarkAllAsRead(c *gin.Context) {
	// Get user ID from context (set by auth middleware)
	userIDStr, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"status": gin.H{
				"code":       http.StatusUnauthorized,
				"message":    "Unauthorized",
				"success":    false,
				"error_code": "UNAUTHORIZED",
				"path":       c.Request.URL.Path,
				"timestamp":  time.Now().Format(time.RFC3339),
			},
		})
		return
	}

	userID, err := strconv.Atoi(userIDStr.(string))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"status": gin.H{
				"code":       http.StatusBadRequest,
				"message":    "Invalid user ID",
				"success":    false,
				"error_code": "INVALID_USER_ID",
				"path":       c.Request.URL.Path,
				"timestamp":  time.Now().Format(time.RFC3339),
			},
		})
		return
	}

	// Mark all notifications as read
	err = h.service.MarkAllAsRead(c.Request.Context(), userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"status": gin.H{
				"code":       http.StatusInternalServerError,
				"message":    "Failed to mark all notifications as read",
				"success":    false,
				"error_code": "INTERNAL_SERVER_ERROR",
				"path":       c.Request.URL.Path,
				"timestamp":  time.Now().Format(time.RFC3339),
			},
		})
		return
	}

	// Return response
	c.JSON(http.StatusOK, gin.H{
		"status": gin.H{
			"code":      http.StatusOK,
			"message":   "All notifications marked as read",
			"success":   true,
			"path":      c.Request.URL.Path,
			"timestamp": time.Now().Format(time.RFC3339),
		},
	})
}

// GetUnreadCount gets the number of unread notifications for a user
func (h *NotificationHandler) GetUnreadCount(c *gin.Context) {
	// Get user ID from context (set by auth middleware)
	userIDStr, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"status": gin.H{
				"code":       http.StatusUnauthorized,
				"message":    "Unauthorized",
				"success":    false,
				"error_code": "UNAUTHORIZED",
				"path":       c.Request.URL.Path,
				"timestamp":  time.Now().Format(time.RFC3339),
			},
		})
		return
	}

	userID, err := strconv.Atoi(userIDStr.(string))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"status": gin.H{
				"code":       http.StatusBadRequest,
				"message":    "Invalid user ID",
				"success":    false,
				"error_code": "INVALID_USER_ID",
				"path":       c.Request.URL.Path,
				"timestamp":  time.Now().Format(time.RFC3339),
			},
		})
		return
	}

	// Get unread count
	count, err := h.service.GetUnreadCount(c.Request.Context(), userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"status": gin.H{
				"code":       http.StatusInternalServerError,
				"message":    "Failed to get unread count",
				"success":    false,
				"error_code": "INTERNAL_SERVER_ERROR",
				"path":       c.Request.URL.Path,
				"timestamp":  time.Now().Format(time.RFC3339),
			},
		})
		return
	}

	// Return response
	c.JSON(http.StatusOK, gin.H{
		"status": gin.H{
			"code":      http.StatusOK,
			"message":   "Unread count retrieved successfully",
			"success":   true,
			"path":      c.Request.URL.Path,
			"timestamp": time.Now().Format(time.RFC3339),
		},
		"data": gin.H{
			"count": count,
		},
	})
}

// SendTestEmail gửi một email test để kiểm tra Jaeger tracing
func (h *NotificationHandler) SendTestEmail(c *gin.Context) {
	// Parse request body
	var req struct {
		To        string            `json:"to"`
		Subject   string            `json:"subject"`
		Content   string            `json:"content"`
		TenantID  int               `json:"tenant_id"`
		UserID    int               `json:"user_id"`
		Variables map[string]string `json:"variables"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		apiErrorWithDetails(c, http.StatusBadRequest, ErrCodeInvalidRequest, "Invalid request data", details)
		return
	}

	// Gửi email thông qua service
	templateID := "test_email"
	if req.Variables == nil {
		req.Variables = make(map[string]string)
	}
	req.Variables["content"] = req.Content

	err := h.service.SendEmail(c.Request.Context(), req.To, req.Subject, templateID, req.Variables, req.TenantID, req.UserID)

	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		apiErrorWithDetails(c, http.StatusInternalServerError, "EMAIL_SEND_FAILED", "Failed to send test email", details)
		return
	}

	apiSuccess(c, http.StatusOK, "Test email sent successfully", nil)
}

// SendBulkEmail handles bulk email sending requests
func (h *NotificationHandler) SendBulkEmail(c *gin.Context) {
	var req struct {
		Emails []dto.EmailRequest `json:"emails" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Invalid request body",
			"error":   err.Error(),
		})
		return
	}

	var responses []dto.NotificationResponse
	var errors []string

	for _, email := range req.Emails {
		response, err := h.service.SendEmail(c.Request.Context(), email)
		if err != nil {
			errors = append(errors, fmt.Sprintf("Failed to send email to %v: %s", email.To, err.Error()))
			continue
		}
		responses = append(responses, *response)
	}

	if len(errors) > 0 {
		c.JSON(http.StatusPartialContent, gin.H{
			"success": false,
			"message": "Some emails failed to send",
			"data":    responses,
			"errors":  errors,
			"sent":    len(responses),
			"failed":  len(errors),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "All emails sent successfully",
		"data":    responses,
		"sent":    len(responses),
	})
}

// SendBulkSMS handles bulk SMS sending requests
func (h *NotificationHandler) SendBulkSMS(c *gin.Context) {
	var req struct {
		SMSList []dto.SMSRequest `json:"sms_list" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Invalid request body",
			"error":   err.Error(),
		})
		return
	}

	var responses []dto.NotificationResponse
	var errors []string

	for _, sms := range req.SMSList {
		response, err := h.service.SendSMS(c.Request.Context(), sms)
		if err != nil {
			errors = append(errors, fmt.Sprintf("Failed to send SMS to %v: %s", sms.To, err.Error()))
			continue
		}
		responses = append(responses, *response)
	}

	if len(errors) > 0 {
		c.JSON(http.StatusPartialContent, gin.H{
			"success": false,
			"message": "Some SMS failed to send",
			"data":    responses,
			"errors":  errors,
			"sent":    len(responses),
			"failed":  len(errors),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "All SMS sent successfully",
		"data":    responses,
		"sent":    len(responses),
	})
}

// SendPush handles push notification requests (alias for SendPushNotification)
func (h *NotificationHandler) SendPush(c *gin.Context) {
	h.SendPushNotification(c)
}

// SendBulkPush handles bulk push notification requests
func (h *NotificationHandler) SendBulkPush(c *gin.Context) {
	var req struct {
		PushList []dto.PushRequest `json:"push_list" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Invalid request body",
			"error":   err.Error(),
		})
		return
	}

	var responses []dto.NotificationResponse
	var errors []string

	for _, push := range req.PushList {
		response, err := h.service.SendPushNotification(c.Request.Context(), push)
		if err != nil {
			errors = append(errors, fmt.Sprintf("Failed to send push notification to %v: %s", push.To, err.Error()))
			continue
		}
		responses = append(responses, *response)
	}

	if len(errors) > 0 {
		c.JSON(http.StatusPartialContent, gin.H{
			"success": false,
			"message": "Some push notifications failed to send",
			"data":    responses,
			"errors":  errors,
			"sent":    len(responses),
			"failed":  len(errors),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "All push notifications sent successfully",
		"data":    responses,
		"sent":    len(responses),
	})
}

// GetHistory retrieves notification history for a user
func (h *NotificationHandler) GetHistory(c *gin.Context) {
	userID := c.Param("user_id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "User ID is required",
		})
		return
	}

	history, err := h.service.GetNotificationHistory(c.Request.Context(), userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to retrieve notification history",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Notification history retrieved successfully",
		"data":    history,
		"count":   len(history),
	})
}

// GetStats retrieves notification statistics for a user
func (h *NotificationHandler) GetStats(c *gin.Context) {
	userID := c.Param("user_id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "User ID is required",
		})
		return
	}

	// Get notification history to calculate stats
	history, err := h.service.GetNotificationHistory(c.Request.Context(), userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to retrieve notification statistics",
			"error":   err.Error(),
		})
		return
	}

	// Calculate statistics
	stats := map[string]interface{}{
		"total": len(history),
		"by_type": map[string]int{
			"email": 0,
			"sms":   0,
			"push":  0,
		},
		"by_status": map[string]int{
			"sent":    0,
			"pending": 0,
			"failed":  0,
		},
	}

	typeStats := stats["by_type"].(map[string]int)
	statusStats := stats["by_status"].(map[string]int)

	for _, notification := range history {
		// Count by type
		if count, exists := typeStats[notification.Type]; exists {
			typeStats[notification.Type] = count + 1
		} else {
			typeStats[notification.Type] = 1
		}

		// Count by status
		if count, exists := statusStats[notification.Status]; exists {
			statusStats[notification.Status] = count + 1
		} else {
			statusStats[notification.Status] = 1
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Notification statistics retrieved successfully",
		"data":    stats,
	})
}
