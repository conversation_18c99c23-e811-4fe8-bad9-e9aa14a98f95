package middleware

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
)

// AuthMiddleware xác thực token JWT đơn giản
func AuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Đọc token từ header
		authHeader := c.<PERSON>("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"status": gin.H{
					"code":       http.StatusUnauthorized,
					"message":    "Yêu cầu xác thực",
					"success":    false,
					"error_code": "UNAUTHORIZED",
					"path":       c.Request.URL.Path,
				},
			})
			c.Abort()
			return
		}

		// Kiểm tra xem header có bắt đầu bằng Bearer không
		if !strings.HasPrefix(authHeader, "Bearer ") {
			c.<PERSON>(http.StatusUnauthorized, gin.H{
				"status": gin.H{
					"code":       http.StatusUnauthorized,
					"message":    "Token không hợp lệ",
					"success":    false,
					"error_code": "INVALID_TOKEN_FORMAT",
					"path":       c.Request.URL.Path,
				},
			})
			c.Abort()
			return
		}

		// Trong môi trường phát triển, chúng ta giả định mọi token đều hợp lệ
		// và chỉ đặt vài thông tin cơ bản vào context
		c.Set("userID", uint(1))
		c.Set("tenantID", uint(1))
		c.Set("role", "admin")

		c.Next()
	}
}

// AdminAuthMiddleware authenticates admin users for admin operations
func AdminAuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// In a real implementation, this would check the JWT token
		// and verify that the user has admin privileges

		// For now, we'll use a simple header check for testing
		userID := c.GetHeader("X-User-ID")
		isAdmin := c.GetHeader("X-Is-Admin")

		if userID == "" || isAdmin != "true" {
			c.JSON(http.StatusForbidden, gin.H{
				"status": gin.H{
					"code":       http.StatusForbidden,
					"message":    "Forbidden",
					"success":    false,
					"error_code": "FORBIDDEN",
					"path":       c.Request.URL.Path,
				},
			})
			c.Abort()
			return
		}

		// Store the user ID in the context for handlers to use
		c.Set("userID", userID)
		c.Next()
	}
}

// WebSocketAuthMiddleware authenticates users for WebSocket connections
func WebSocketAuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// In a real implementation, this would validate the token
		// from the query parameters

		// For now, we'll use a simple token check for testing
		token := c.Query("token")
		if token == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"status": gin.H{
					"code":       http.StatusUnauthorized,
					"message":    "Unauthorized",
					"success":    false,
					"error_code": "UNAUTHORIZED",
					"path":       c.Request.URL.Path,
				},
			})
			c.Abort()
			return
		}

		// Extract user ID from token (in a real implementation)
		// For testing, we'll use a simple query parameter
		userID := c.Query("user_id")
		if userID == "" {
			c.JSON(http.StatusBadRequest, gin.H{
				"status": gin.H{
					"code":       http.StatusBadRequest,
					"message":    "Missing user_id parameter",
					"success":    false,
					"error_code": "INVALID_REQUEST",
					"path":       c.Request.URL.Path,
				},
				"details": []interface{}{map[string]string{"message": "Missing user_id parameter"}},
			})
			c.Abort()
			return
		}

		// Store the user ID in the context for handlers to use
		c.Set("userID", userID)
		c.Next()
	}
}
