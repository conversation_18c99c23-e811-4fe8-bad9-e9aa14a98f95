openapi: 3.1.0
info:
  title: Notification API
  description: API cho module thông báo
  version: 1.0.0
  contact:
    name: WebNew Team
servers:
  - url: http://wn-api.local
    description: Server phát triển

tags:
  - name: Notifications
    description: <PERSON><PERSON><PERSON><PERSON> lý thông báo
  - name: Templates
    description: Qu<PERSON>n lý mẫu thông báo

paths:
  /api/v1/notifications:
    get:
      summary: <PERSON><PERSON><PERSON> danh sách thông báo của người dùng
      tags:
        - Notifications
      security:
        - bearerAuth: []
      parameters:
        - name: cursor
          in: query
          schema:
            type: string
          description: Cursor để phân trang
        - name: limit
          in: query
          schema:
            type: integer
            default: 20
          description: Số lượng thông báo tối đa trả về
        - name: is_read
          in: query
          schema:
            type: boolean
          description: Lọc theo trạng thái đã đọc
        - name: type
          in: query
          schema:
            type: string
          description: Lọc theo loại thông báo
      responses:
        '200':
          description: Thành công
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotificationListResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/ServerError'
    post:
      summary: Tạo thông báo mới
      tags:
        - Notifications
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateNotificationRequest'
      responses:
        '201':
          description: Thông báo đã được tạo
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotificationResponse'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '500':
          $ref: '#/components/responses/ServerError'

  /api/v1/notifications/{id}:
    get:
      summary: Lấy thông tin chi tiết một thông báo
      tags:
        - Notifications
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: ID của thông báo
      responses:
        '200':
          description: Thành công
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotificationDetailResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '500':
          $ref: '#/components/responses/ServerError'
    put:
      summary: Cập nhật thông báo
      tags:
        - Notifications
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: ID của thông báo
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateNotificationRequest'
      responses:
        '200':
          description: Thông báo đã được cập nhật
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotificationResponse'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '500':
          $ref: '#/components/responses/ServerError'
    delete:
      summary: Xóa thông báo
      tags:
        - Notifications
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: ID của thông báo
      responses:
        '200':
          description: Thông báo đã được xóa
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '500':
          $ref: '#/components/responses/ServerError'

  /api/v1/notifications/{id}/read:
    put:
      summary: Đánh dấu thông báo đã đọc
      tags:
        - Notifications
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: ID của thông báo
      responses:
        '200':
          description: Thông báo đã được đánh dấu là đã đọc
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '500':
          $ref: '#/components/responses/ServerError'

  /api/v1/notifications/read-all:
    put:
      summary: Đánh dấu tất cả thông báo đã đọc
      tags:
        - Notifications
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Tất cả thông báo đã được đánh dấu là đã đọc
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/ServerError'

  /api/v1/notifications/unread-count:
    get:
      summary: Lấy số lượng thông báo chưa đọc
      tags:
        - Notifications
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Số lượng thông báo chưa đọc
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    $ref: '#/components/schemas/Status'
                  data:
                    type: object
                    properties:
                      count:
                        type: integer
                        description: Số lượng thông báo chưa đọc
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/ServerError'

  /api/v1/notification-templates:
    get:
      summary: Lấy danh sách mẫu thông báo
      tags:
        - Templates
      security:
        - bearerAuth: []
      parameters:
        - name: cursor
          in: query
          schema:
            type: string
          description: Cursor để phân trang
        - name: limit
          in: query
          schema:
            type: integer
            default: 20
          description: Số lượng mẫu tối đa trả về
        - name: notification_type
          in: query
          schema:
            type: string
          description: Lọc theo loại thông báo
        - name: is_active
          in: query
          schema:
            type: boolean
          description: Lọc theo trạng thái hoạt động
      responses:
        '200':
          description: Thành công
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TemplateListResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '500':
          $ref: '#/components/responses/ServerError'
    post:
      summary: Tạo mẫu thông báo mới
      tags:
        - Templates
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateTemplateRequest'
      responses:
        '201':
          description: Mẫu thông báo đã được tạo
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TemplateCreatedResponse'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '500':
          $ref: '#/components/responses/ServerError'

  /api/v1/notification-templates/{id}:
    get:
      summary: Lấy thông tin chi tiết một mẫu thông báo
      tags:
        - Templates
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: ID của mẫu thông báo
      responses:
        '200':
          description: Thành công
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TemplateResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '500':
          $ref: '#/components/responses/ServerError'
    put:
      summary: Cập nhật mẫu thông báo
      tags:
        - Templates
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: ID của mẫu thông báo
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateTemplateRequest'
      responses:
        '200':
          description: Mẫu thông báo đã được cập nhật
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TemplateUpdatedResponse'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '500':
          $ref: '#/components/responses/ServerError'
    delete:
      summary: Xóa mẫu thông báo
      tags:
        - Templates
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: ID của mẫu thông báo
      responses:
        '200':
          description: Mẫu thông báo đã được xóa
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '500':
          $ref: '#/components/responses/ServerError'

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    Status:
      type: object
      properties:
        code:
          type: integer
          description: HTTP status code
        message:
          type: string
          description: Thông báo từ server
        success:
          type: boolean
          description: Trạng thái yêu cầu
        error_code:
          type: string
          description: Mã lỗi nếu có
          nullable: true
        path:
          type: string
          description: Đường dẫn API
        timestamp:
          type: string
          format: date-time
          description: Thời gian phản hồi
        details:
          type: array
          items:
            type: object
            properties:
              field:
                type: string
                description: Trường dữ liệu lỗi
              message:
                type: string
                description: Thông báo lỗi
          nullable: true
      required:
        - code
        - message
        - success
        - path
        - timestamp

    PaginationMeta:
      type: object
      properties:
        next_cursor:
          type: string
          description: Cursor cho trang tiếp theo
        has_more:
          type: boolean
          description: Có còn dữ liệu tiếp theo không

    NotificationResponse:
      type: object
      properties:
        id:
          type: integer
          description: ID của thông báo
        title:
          type: string
          description: Tiêu đề thông báo
        content:
          type: string
          description: Nội dung thông báo
        notification_type:
          type: string
          description: Loại thông báo
        reference_type:
          type: string
          description: Loại tham chiếu
          nullable: true
        reference_id:
          type: string
          description: ID tham chiếu
          nullable: true
        is_read:
          type: boolean
          description: Đã đọc hay chưa
        created_at:
          type: string
          format: date-time
          description: Thời gian tạo

    NotificationListResponse:
      type: object
      properties:
        status:
          $ref: '#/components/schemas/Status'
        data:
          type: array
          items:
            $ref: '#/components/schemas/NotificationResponse'
        meta:
          $ref: '#/components/schemas/PaginationMeta'

    NotificationDetailResponse:
      type: object
      properties:
        status:
          $ref: '#/components/schemas/Status'
        data:
          $ref: '#/components/schemas/NotificationResponse'

    CreateNotificationRequest:
      type: object
      required:
        - user_id
        - title
        - content
        - notification_type
      properties:
        user_id:
          type: integer
          description: ID người dùng
        title:
          type: string
          description: Tiêu đề thông báo
        content:
          type: string
          description: Nội dung thông báo
        notification_type:
          type: string
          description: Loại thông báo
        reference_type:
          type: string
          description: Loại tham chiếu
        reference_id:
          type: string
          description: ID tham chiếu

    CreateNotificationFromTemplateRequest:
      type: object
      required:
        - user_id
        - code
      properties:
        user_id:
          type: integer
          description: ID người dùng
        code:
          type: string
          description: Mã của mẫu thông báo
        placeholders:
          type: object
          description: Các giá trị thay thế trong mẫu
          additionalProperties: true
        reference_type:
          type: string
          description: Loại tham chiếu
        reference_id:
          type: string
          description: ID tham chiếu

    UpdateNotificationRequest:
      type: object
      properties:
        title:
          type: string
          description: Tiêu đề thông báo
        content:
          type: string
          description: Nội dung thông báo
        notification_type:
          type: string
          description: Loại thông báo
        reference_type:
          type: string
          description: Loại tham chiếu
        reference_id:
          type: string
          description: ID tham chiếu

    TemplateResponse:
      type: object
      properties:
        template_id:
          type: integer
          description: ID của mẫu
        code:
          type: string
          description: Mã của mẫu
        title_template:
          type: string
          description: Mẫu tiêu đề
        content_template:
          type: string
          description: Mẫu nội dung
        notification_type:
          type: string
          description: Loại thông báo
        is_active:
          type: boolean
          description: Mẫu có đang hoạt động không
        created_at:
          type: string
          format: date-time
          description: Thời gian tạo
        updated_at:
          type: string
          format: date-time
          description: Thời gian cập nhật cuối

    TemplateListResponse:
      type: object
      properties:
        status:
          $ref: '#/components/schemas/Status'
        data:
          type: array
          items:
            $ref: '#/components/schemas/TemplateResponse'
        meta:
          $ref: '#/components/schemas/PaginationMeta'

    TemplateDetailResponse:
      type: object
      properties:
        status:
          $ref: '#/components/schemas/Status'
        data:
          $ref: '#/components/schemas/TemplateResponse'

    CreateTemplateRequest:
      type: object
      required:
        - code
        - title_template
        - content_template
        - notification_type
      properties:
        code:
          type: string
          description: Mã của mẫu
        title_template:
          type: string
          description: Mẫu tiêu đề
        content_template:
          type: string
          description: Mẫu nội dung
        notification_type:
          type: string
          description: Loại thông báo
        is_active:
          type: boolean
          description: Mẫu có đang hoạt động không
          default: true

    UpdateTemplateRequest:
      type: object
      required:
        - code
        - title_template
        - content_template
        - notification_type
      properties:
        code:
          type: string
          description: Mã của mẫu
        title_template:
          type: string
          description: Mẫu tiêu đề
        content_template:
          type: string
          description: Mẫu nội dung
        notification_type:
          type: string
          description: Loại thông báo
        is_active:
          type: boolean
          description: Mẫu có đang hoạt động không

    TemplateCreatedResponse:
      type: object
      properties:
        status:
          $ref: '#/components/schemas/Status'
        data:
          type: object
          properties:
            template_id:
              type: integer
              description: ID của mẫu đã tạo

    TemplateUpdatedResponse:
      type: object
      properties:
        status:
          $ref: '#/components/schemas/Status'
        data:
          type: object
          properties:
            success:
              type: boolean
              description: Trạng thái cập nhật

    SuccessResponse:
      type: object
      properties:
        status:
          $ref: '#/components/schemas/Status'

  responses:
    BadRequestError:
      description: Yêu cầu không hợp lệ
      content:
        application/json:
          schema:
            type: object
            properties:
              status:
                allOf:
                  - $ref: '#/components/schemas/Status'
                  - type: object
                    properties:
                      code:
                        example: 400
                      message:
                        example: "Dữ liệu yêu cầu không hợp lệ"
                      success:
                        example: false
                      error_code:
                        example: "INVALID_REQUEST"

    UnauthorizedError:
      description: Chưa xác thực
      content:
        application/json:
          schema:
            type: object
            properties:
              status:
                allOf:
                  - $ref: '#/components/schemas/Status'
                  - type: object
                    properties:
                      code:
                        example: 401
                      message:
                        example: "Không được phép truy cập"
                      success:
                        example: false
                      error_code:
                        example: "UNAUTHORIZED"

    ForbiddenError:
      description: Không có quyền truy cập
      content:
        application/json:
          schema:
            type: object
            properties:
              status:
                allOf:
                  - $ref: '#/components/schemas/Status'
                  - type: object
                    properties:
                      code:
                        example: 403
                      message:
                        example: "Không đủ quyền để thực hiện hành động này"
                      success:
                        example: false
                      error_code:
                        example: "FORBIDDEN"

    NotFoundError:
      description: Không tìm thấy
      content:
        application/json:
          schema:
            type: object
            properties:
              status:
                allOf:
                  - $ref: '#/components/schemas/Status'
                  - type: object
                    properties:
                      code:
                        example: 404
                      message:
                        example: "Không tìm thấy tài nguyên"
                      success:
                        example: false
                      error_code:
                        example: "NOT_FOUND"

    ServerError:
      description: Lỗi máy chủ
      content:
        application/json:
          schema:
            type: object
            properties:
              status:
                allOf:
                  - $ref: '#/components/schemas/Status'
                  - type: object
                    properties:
                      code:
                        example: 500
                      message:
                        example: "Lỗi máy chủ nội bộ"
                      success:
                        example: false
                      error_code:
                        example: "INTERNAL_SERVER_ERROR"
