package service

import (
	"context"
	"wnapi/modules/notification/dto"
)

// NotificationService defines the interface for notification operations
// This interface is expected by the API handlers
type NotificationService interface {
	SendEmail(ctx context.Context, req dto.EmailRequest) (*dto.NotificationResponse, error)
	SendSMS(ctx context.Context, req dto.SMSRequest) (*dto.NotificationResponse, error)
	SendPushNotification(ctx context.Context, req dto.PushRequest) (*dto.NotificationResponse, error)
	GetNotificationHistory(ctx context.Context, userID string) ([]dto.NotificationHistory, error)
}
