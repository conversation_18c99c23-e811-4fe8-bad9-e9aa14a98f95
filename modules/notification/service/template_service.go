// Package service implements the business logic for the notification module
package service

import (
	"context"
	"fmt"
	"time"

	"wnapi/modules/notification/models"
	"wnapi/modules/notification/repository"
	"wnapi/modules/notification/repository/redis"
)

// TemplateService handles notification template business logic
type TemplateService struct {
	templateRepo  repository.TemplateRepository
	templateCache *redis.TemplateCache
}

// NewTemplateService creates a new template service
func NewTemplateService(
	templateRepo repository.TemplateRepository,
	templateCache *redis.TemplateCache,
) *TemplateService {
	return &TemplateService{
		templateRepo:  templateRepo,
		templateCache: templateCache,
	}
}

// CreateTemplate creates a new notification template
func (s *TemplateService) CreateTemplate(
	ctx context.Context,
	templateCode, titleTemplate, contentTemplate, notificationType string,
	isActive bool,
) (int, error) {
	// Check if template with same code already exists
	existingTemplate, err := s.templateRepo.GetByCode(ctx, templateCode)
	if err == nil && existingTemplate != nil {
		return 0, fmt.Errorf("template with code %s already exists", templateCode)
	}

	template := &models.Template{
		TemplateCode:     templateCode,
		TitleTemplate:    titleTemplate,
		ContentTemplate:  contentTemplate,
		NotificationType: notificationType,
		IsActive:         isActive,
		CreatedAt:        time.Now(),
		UpdatedAt:        time.Now(),
	}

	templateID, err := s.templateRepo.Create(ctx, template)
	if err != nil {
		return 0, fmt.Errorf("failed to create template: %w", err)
	}

	// Invalidate cache
	if s.templateCache != nil {
		s.templateCache.InvalidateTemplateSimple(templateCode)
	}

	return templateID, nil
}

// GetTemplate gets a template by ID
func (s *TemplateService) GetTemplate(ctx context.Context, templateID int) (*models.Template, error) {
	template, err := s.templateRepo.GetByID(ctx, templateID)
	if err != nil {
		return nil, fmt.Errorf("failed to get template: %w", err)
	}
	return template, nil
}

// GetTemplateByCode gets a template by its code
func (s *TemplateService) GetTemplateByCode(ctx context.Context, templateCode string) (*models.Template, error) {
	// Try to get from cache first
	if s.templateCache != nil {
		if template, found := s.templateCache.GetTemplate(templateCode); found {
			return template, nil
		}
	}

	// If not in cache, get from database
	template, err := s.templateRepo.GetByCode(ctx, templateCode)
	if err != nil {
		return nil, fmt.Errorf("failed to get template by code: %w", err)
	}

	// Cache the template
	if s.templateCache != nil {
		s.templateCache.SetTemplateSimple(template)
	}

	return template, nil
}

// ListTemplates lists templates with filtering and pagination
func (s *TemplateService) ListTemplates(
	ctx context.Context,
	notificationType string,
	isActive *bool,
	cursor string,
	limit int,
) ([]*models.Template, string, error) {
	filters := make(map[string]interface{})

	if notificationType != "" {
		filters["notification_type"] = notificationType
	}

	if isActive != nil {
		filters["is_active"] = *isActive
	}

	templates, nextCursor, err := s.templateRepo.List(ctx, cursor, limit, filters)
	if err != nil {
		return nil, "", fmt.Errorf("failed to list templates: %w", err)
	}

	return templates, nextCursor, nil
}

// UpdateTemplate updates an existing template
func (s *TemplateService) UpdateTemplate(
	ctx context.Context,
	templateID int,
	titleTemplate, contentTemplate, notificationType string,
	isActive bool,
) error {
	// Get existing template
	template, err := s.templateRepo.GetByID(ctx, templateID)
	if err != nil {
		return fmt.Errorf("failed to get template: %w", err)
	}

	// Update fields
	template.TitleTemplate = titleTemplate
	template.ContentTemplate = contentTemplate
	template.NotificationType = notificationType
	template.IsActive = isActive
	template.UpdatedAt = time.Now()

	if err := s.templateRepo.Update(ctx, template); err != nil {
		return fmt.Errorf("failed to update template: %w", err)
	}

	// Invalidate cache
	if s.templateCache != nil {
		s.templateCache.InvalidateTemplateSimple(template.TemplateCode)
	}

	return nil
}

// DeleteTemplate deletes a template by ID
func (s *TemplateService) DeleteTemplate(ctx context.Context, templateID int) error {
	// Get the template first to get the template code for cache invalidation
	template, err := s.templateRepo.GetByID(ctx, templateID)
	if err != nil {
		return fmt.Errorf("failed to get template: %w", err)
	}

	if err := s.templateRepo.Delete(ctx, templateID); err != nil {
		return fmt.Errorf("failed to delete template: %w", err)
	}

	// Invalidate cache
	if s.templateCache != nil {
		s.templateCache.InvalidateTemplateSimple(template.TemplateCode)
	}

	return nil
}
