// Repository interfaces for the notification module
package repository

import (
	"context"
	"wnapi/modules/notification/internal"
)

// Repository là interface ch<PERSON>h cho tất cả repository
type Repository interface {
	Notification() NotificationRepository
	Template() TemplateRepository
	Channel() ChannelRepository
	Preference() PreferenceRepository
	Delivery() DeliveryRepository
	Telegram() TelegramRepository
	Websocket() WebsocketRepository
}

// NotificationRepository xử lý thao tác với bảng notifications
type NotificationRepository interface {
	GetNotificationByID(ctx context.Context, id int) (*internal.Notification, error)
	CreateNotification(ctx context.Context, notification *internal.Notification) (int, error)
	UpdateNotification(ctx context.Context, notification *internal.Notification) error
	DeleteNotification(ctx context.Context, id int) error
	ListNotifications(ctx context.Context, filter interface{}) ([]*internal.Notification, error)
	MarkAsRead(ctx context.Context, id int) error
}

// TemplateRepository xử lý thao tác với bảng notification_templates
type TemplateRepository interface {
	GetTemplateByID(ctx context.Context, id int) (*internal.Template, error)
	CreateTemplate(ctx context.Context, template *internal.Template) (int, error)
	UpdateTemplate(ctx context.Context, template *internal.Template) error
	DeleteTemplate(ctx context.Context, id int) error
	ListTemplates(ctx context.Context, filter interface{}) ([]*internal.Template, error)
}

// ChannelRepository xử lý thao tác với bảng notification_channels
type ChannelRepository interface {
	GetChannelByID(ctx context.Context, id int) (*internal.Channel, error)
	CreateChannel(ctx context.Context, channel *internal.Channel) (int, error)
	UpdateChannel(ctx context.Context, channel *internal.Channel) error
	DeleteChannel(ctx context.Context, id int) error
	ListChannels(ctx context.Context, filter interface{}) ([]*internal.Channel, error)
}

// PreferenceRepository xử lý thao tác với bảng notification_preferences
type PreferenceRepository interface {
	GetPreferenceByID(ctx context.Context, id int) (*internal.Preference, error)
	GetPreferenceByUserID(ctx context.Context, userID int) (*internal.Preference, error)
	CreatePreference(ctx context.Context, preference *internal.Preference) (int, error)
	UpdatePreference(ctx context.Context, preference *internal.Preference) error
	DeletePreference(ctx context.Context, id int) error
	ListPreferences(ctx context.Context, filter interface{}) ([]*internal.Preference, error)
}

// DeliveryRepository xử lý thao tác với bảng notification_deliveries
type DeliveryRepository interface {
	GetDeliveryByID(ctx context.Context, id int) (*internal.Delivery, error)
	CreateDelivery(ctx context.Context, delivery *internal.Delivery) (int, error)
	UpdateDelivery(ctx context.Context, delivery *internal.Delivery) error
	DeleteDelivery(ctx context.Context, id int) error
	ListDeliveries(ctx context.Context, filter interface{}) ([]*internal.Delivery, error)
}

// TelegramRepository xử lý thao tác với bảng notification_telegram
type TelegramRepository interface {
	GetTelegramByID(ctx context.Context, id int) (*internal.Telegram, error)
	GetTelegramByUserID(ctx context.Context, userID int) (*internal.Telegram, error)
	CreateTelegram(ctx context.Context, telegram *internal.Telegram) (int, error)
	UpdateTelegram(ctx context.Context, telegram *internal.Telegram) error
	DeleteTelegram(ctx context.Context, id int) error
	ListTelegrams(ctx context.Context, filter interface{}) ([]*internal.Telegram, error)
}

// WebsocketRepository xử lý thao tác với bảng notification_websockets
type WebsocketRepository interface {
	GetWebsocketByID(ctx context.Context, id string) (*internal.Websocket, error)
	CreateWebsocket(ctx context.Context, websocket *internal.Websocket) (string, error)
	UpdateWebsocket(ctx context.Context, websocket *internal.Websocket) error
	DeleteWebsocket(ctx context.Context, id string) error
	ListWebsockets(ctx context.Context, filter interface{}) ([]*internal.Websocket, error)
}
