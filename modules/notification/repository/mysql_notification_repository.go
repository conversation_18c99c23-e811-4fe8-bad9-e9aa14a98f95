package repository

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"strconv"
	"time"

	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/tracing"
	"wnapi/modules/notification/models"

	"github.com/jmoiron/sqlx"
	"go.opentelemetry.io/otel/attribute"
)

// notificationRepository triển khai NotificationRepository interface sử dụng MySQL
type notificationRepository struct {
	db     *sqlx.DB
	logger logger.Logger
}

// NewNotificationRepository tạo repository mới
func NewNotificationRepository(db *sqlx.DB, logger logger.Logger) NotificationRepository {
	return &notificationRepository{
		db:     db,
		logger: logger,
	}
}

// Create tạo thông báo mới
func (r *notificationRepository) Create(ctx context.Context, notification *models.Notification) (int, error) {
	// Tạo span cho truy vấn database
	ctx, span := tracing.DatabaseMiddleware(ctx, "INSERT", "notification_notifications")
	defer span.End()

	query := `
		INSERT INTO notification_notifications (
			user_id, title, content, notification_type, reference_type, reference_id, 
			is_read, is_sent, created_at, updated_at
		) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`

	tracing.AddSpanAttributes(ctx, tracing.DBStatement(query))

	result, err := r.db.ExecContext(
		ctx, query,
		notification.UserID, notification.Title, notification.Content,
		notification.NotificationType, notification.ReferenceType, notification.ReferenceID,
		notification.IsRead, notification.IsSent, notification.CreatedAt, notification.UpdatedAt,
	)

	if err != nil {
		r.logger.Error("Failed to create notification", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return 0, err
	}

	id, err := result.LastInsertId()
	if err != nil {
		r.logger.Error("Failed to get last insert id", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return 0, err
	}

	return int(id), nil
}

// GetByID lấy thông báo theo ID
func (r *notificationRepository) GetByID(ctx context.Context, notificationID int) (*models.Notification, error) {
	ctx, span := tracing.DatabaseMiddleware(ctx, "SELECT", "notification_notifications")
	defer span.End()

	query := `
		SELECT * FROM notification_notifications WHERE notification_id = ?
	`

	tracing.AddSpanAttributes(ctx, tracing.DBStatement(query))

	var notification models.Notification
	err := r.db.GetContext(ctx, &notification, query, notificationID)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			tracing.AddSpanAttributes(ctx, attribute.Bool("db.record_found", false))
			return nil, nil
		}
		r.logger.Error("Failed to get notification", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return nil, err
	}

	tracing.AddSpanAttributes(ctx, attribute.Bool("db.record_found", true))
	return &notification, nil
}

// GetByUserID lấy danh sách thông báo của người dùng
func (r *notificationRepository) GetByUserID(
	ctx context.Context,
	userID int,
	cursor string,
	limit int,
	filters map[string]interface{},
) ([]*models.Notification, string, error) {
	ctx, span := tracing.DatabaseMiddleware(ctx, "SELECT", "notification_notifications")
	defer span.End()

	// Xây dựng câu query cơ bản
	baseQuery := `
		SELECT * FROM notification_notifications
		WHERE user_id = ?
	`

	// Thêm điều kiện từ filters
	var conditions []string
	var args []interface{}
	args = append(args, userID)

	if filters != nil {
		if isRead, ok := filters["is_read"].(bool); ok {
			conditions = append(conditions, "is_read = ?")
			args = append(args, isRead)
		}
		if notificationType, ok := filters["notification_type"].(string); ok {
			conditions = append(conditions, "notification_type = ?")
			args = append(args, notificationType)
		}
	}

	// Thêm điều kiện cursor nếu có
	if cursor != "" {
		cursorID, err := strconv.Atoi(cursor)
		if err == nil {
			conditions = append(conditions, "notification_id < ?")
			args = append(args, cursorID)
		}
	}

	// Ghép các điều kiện
	query := baseQuery
	for i, cond := range conditions {
		if i == 0 {
			query += " AND " + cond
		} else {
			query += " AND " + cond
		}
	}

	// Thêm ORDER BY và LIMIT
	query += " ORDER BY notification_id DESC LIMIT ?"
	args = append(args, limit+1) // Lấy thêm 1 bản ghi để tính next_cursor

	tracing.AddSpanAttributes(ctx, tracing.DBStatement(query))

	var notifications []*models.Notification
	err := r.db.SelectContext(ctx, &notifications, query, args...)
	if err != nil {
		r.logger.Error("Failed to get notifications", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return nil, "", err
	}

	// Xử lý cursor
	var nextCursor string
	if len(notifications) > limit {
		// Lấy ID của bản ghi cuối cùng làm next_cursor
		nextCursor = fmt.Sprintf("%d", notifications[limit].NotificationID)
		// Loại bỏ bản ghi thừa
		notifications = notifications[:limit]
	}

	return notifications, nextCursor, nil
}

// MarkAsRead đánh dấu thông báo đã đọc
func (r *notificationRepository) MarkAsRead(ctx context.Context, notificationID int) error {
	ctx, span := tracing.DatabaseMiddleware(ctx, "UPDATE", "notification_notifications")
	defer span.End()

	query := `
		UPDATE notification_notifications
		SET is_read = 1, updated_at = ?
		WHERE notification_id = ?
	`

	tracing.AddSpanAttributes(ctx, tracing.DBStatement(query))

	_, err := r.db.ExecContext(ctx, query, time.Now(), notificationID)
	if err != nil {
		r.logger.Error("Failed to mark notification as read", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return err
	}

	return nil
}

// MarkAllAsRead đánh dấu tất cả thông báo của người dùng đã đọc
func (r *notificationRepository) MarkAllAsRead(ctx context.Context, userID int) error {
	ctx, span := tracing.DatabaseMiddleware(ctx, "UPDATE", "notification_notifications")
	defer span.End()

	query := `
		UPDATE notification_notifications
		SET is_read = 1, updated_at = ?
		WHERE user_id = ? AND is_read = 0
	`

	tracing.AddSpanAttributes(ctx, tracing.DBStatement(query))

	_, err := r.db.ExecContext(ctx, query, time.Now(), userID)
	if err != nil {
		r.logger.Error("Failed to mark all notifications as read", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return err
	}

	return nil
}

// GetUnreadCount đếm số lượng thông báo chưa đọc
func (r *notificationRepository) GetUnreadCount(ctx context.Context, userID int) (int, error) {
	ctx, span := tracing.DatabaseMiddleware(ctx, "SELECT", "notification_notifications")
	defer span.End()

	query := `
		SELECT COUNT(*) FROM notification_notifications
		WHERE user_id = ? AND is_read = 0
	`

	tracing.AddSpanAttributes(ctx, tracing.DBStatement(query))

	var count int
	err := r.db.GetContext(ctx, &count, query, userID)
	if err != nil {
		r.logger.Error("Failed to get unread count", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return 0, err
	}

	return count, nil
}

// Update cập nhật thông báo
func (r *notificationRepository) Update(ctx context.Context, notification *models.Notification) error {
	ctx, span := tracing.DatabaseMiddleware(ctx, "UPDATE", "notification_notifications")
	defer span.End()

	query := `
		UPDATE notification_notifications
		SET title = ?, content = ?, notification_type = ?, reference_type = ?,
			reference_id = ?, is_read = ?, is_sent = ?, updated_at = ?
		WHERE notification_id = ?
	`

	tracing.AddSpanAttributes(ctx, tracing.DBStatement(query))

	_, err := r.db.ExecContext(
		ctx, query,
		notification.Title, notification.Content, notification.NotificationType,
		notification.ReferenceType, notification.ReferenceID, notification.IsRead,
		notification.IsSent, time.Now(), notification.NotificationID,
	)

	if err != nil {
		r.logger.Error("Failed to update notification", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return err
	}

	return nil
}

// Delete xóa thông báo
func (r *notificationRepository) Delete(ctx context.Context, notificationID int) error {
	ctx, span := tracing.DatabaseMiddleware(ctx, "DELETE", "notification_notifications")
	defer span.End()

	query := `
		DELETE FROM notification_notifications
		WHERE notification_id = ?
	`

	tracing.AddSpanAttributes(ctx, tracing.DBStatement(query))

	_, err := r.db.ExecContext(ctx, query, notificationID)
	if err != nil {
		r.logger.Error("Failed to delete notification", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return err
	}

	return nil
}
