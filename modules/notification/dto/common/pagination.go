package common

// PaginationRequest represents a common pagination request
type PaginationRequest struct {
	Cursor string `json:"cursor" form:"cursor"`
	Limit  int    `json:"limit" form:"limit" binding:"required,min=1,max=100"`
}

// PaginationMeta represents pagination metadata
type PaginationMeta struct {
	NextCursor string `json:"next_cursor,omitempty"`
	<PERSON><PERSON><PERSON>    bool   `json:"has_more"`
}
