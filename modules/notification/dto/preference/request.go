package preference

// CreatePreferenceRequest represents a request to create a new notification preference
type CreatePreferenceRequest struct {
	UserID           uint    `json:"user_id" binding:"required"`
	NotificationType string `json:"notification_type" binding:"required"`
	ChannelCode      string `json:"channel_code" binding:"required"`
	IsEnabled        bool   `json:"is_enabled"`
}

// UpdatePreferenceRequest represents a request to update an existing notification preference
type UpdatePreferenceRequest struct {
	IsEnabled bool `json:"is_enabled" binding:"required"`
}

// GetPreferenceRequest represents a request to get a specific notification preference
type GetPreferenceRequest struct {
	PreferenceID uint `json:"preference_id" binding:"required"`
}

// GetUserPreferencesRequest represents a request to list user notification preferences
type GetUserPreferencesRequest struct {
	UserID           uint    `json:"user_id" binding:"required"`
	NotificationType string `json:"notification_type"`
	ChannelCode      string `json:"channel_code"`
	Cursor           string `json:"cursor"`
	Limit            int    `json:"limit" binding:"required,min=1,max=100"`
}

// SetDefaultPreferencesRequest represents a request to set default preferences for a user
type SetDefaultPreferencesRequest struct {
	UserID uint `json:"user_id" binding:"required"`
}
