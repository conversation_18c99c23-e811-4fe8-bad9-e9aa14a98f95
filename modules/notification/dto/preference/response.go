package preference

import (
	"time"
)

// PreferenceResponse represents a notification preference response
type PreferenceResponse struct {
	PreferenceID     uint       `json:"preference_id"`
	UserID           uint       `json:"user_id"`
	NotificationType string    `json:"notification_type"`
	ChannelCode      string    `json:"channel_code"`
	IsEnabled        bool      `json:"is_enabled"`
	CreatedAt        time.Time `json:"created_at"`
	UpdatedAt        time.Time `json:"updated_at"`
}

// PreferenceListResponse represents a list of notification preferences with pagination metadata
type PreferenceListResponse struct {
	Preferences []PreferenceResponse `json:"preferences"`
	Meta        PaginationMeta       `json:"meta"`
}

// PaginationMeta represents pagination metadata
type PaginationMeta struct {
	NextCursor string `json:"next_cursor,omitempty"`
	HasMore    bool   `json:"has_more"`
}

// PreferenceCreatedResponse represents a response after creating a preference
type PreferenceCreatedResponse struct {
	PreferenceID uint `json:"preference_id"`
}

// PreferenceUpdatedResponse represents a response after updating a preference
type PreferenceUpdatedResponse struct {
	Success bool `json:"success"`
}

// DefaultPreferencesSetResponse represents a response after setting default preferences
type DefaultPreferencesSetResponse struct {
	Success bool `json:"success"`
}
