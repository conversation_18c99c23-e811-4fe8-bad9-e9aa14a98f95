package notification

// CreateNotificationRequest represents the data needed to create a notification
type CreateNotificationRequest struct {
	UserID           uint    `json:"user_id" binding:"required"`
	Title            string `json:"title" binding:"required"`
	Content          string `json:"content" binding:"required"`
	NotificationType string `json:"notification_type" binding:"required"`
	ReferenceType    string `json:"reference_type"`
	ReferenceID      string `json:"reference_id"`
}

// UpdateNotificationRequest represents the data needed to update a notification
type UpdateNotificationRequest struct {
	Title            string `json:"title"`
	Content          string `json:"content"`
	NotificationType string `json:"notification_type"`
	ReferenceType    string `json:"reference_type"`
	ReferenceID      string `json:"reference_id"`
}

// NotificationFilterRequest represents the filters for notification queries
type NotificationFilterRequest struct {
	IsRead           *bool  `form:"is_read"`
	NotificationType string `form:"notification_type"`
	ReferenceType    string `form:"reference_type"`
	ReferenceID      string `form:"reference_id"`
	Cursor           string `form:"cursor"`
	Limit            int    `form:"limit,default=20"`
}

// CreateNotificationFromTemplateRequest represents a request to create a notification from a template
type CreateNotificationFromTemplateRequest struct {
	UserID        uint                    `json:"user_id" binding:"required"`
	Code          string                 `json:"code" binding:"required"`
	Placeholders  map[string]interface{} `json:"placeholders"`
	ReferenceType string                 `json:"reference_type"`
	ReferenceID   string                 `json:"reference_id"`
}

// MarkAsReadRequest represents a request to mark a notification as read
type MarkAsReadRequest struct {
	NotificationID uint `json:"notification_id" binding:"required"`
}

// MarkAllAsReadRequest represents a request to mark all notifications as read
type MarkAllAsReadRequest struct {
	UserID uint `json:"user_id" binding:"required"`
}

// GetUserNotificationsRequest represents a request to get user notifications
type GetUserNotificationsRequest struct {
	UserID    uint    `json:"user_id" binding:"required"`
	Cursor    string `json:"cursor"`
	Limit     int    `json:"limit" binding:"required,min=1,max=100"`
	IsRead    *bool  `json:"is_read"`
	Type      string `json:"type"`
	SortBy    string `json:"sort_by" binding:"omitempty,oneof=created_at notification_id"`
	SortOrder string `json:"sort_order" binding:"omitempty,oneof=asc desc"`
}
