package notification

import (
	"time"
)

// NotificationResponse represents a notification in API responses
type NotificationResponse struct {
	ID               uint        `json:"id"`
	UserID           uint        `json:"user_id"`
	Title            string     `json:"title"`
	Content          string     `json:"content"`
	NotificationType string     `json:"notification_type"`
	ReferenceType    string     `json:"reference_type,omitempty"`
	ReferenceID      string     `json:"reference_id,omitempty"`
	IsRead           bool       `json:"is_read"`
	IsSent           bool       `json:"is_sent"`
	SentAt           *time.Time `json:"sent_at,omitempty"`
	CreatedAt        time.Time  `json:"created_at"`
	UpdatedAt        time.Time  `json:"updated_at"`
}

// NotificationListResponse represents a list of notifications with pagination metadata
type NotificationListResponse struct {
	Notifications []NotificationResponse `json:"notifications"`
	Meta          PaginationMeta         `json:"meta"`
}

// PaginationMeta represents pagination metadata
type PaginationMeta struct {
	NextCursor string `json:"next_cursor,omitempty"`
	HasMore    bool   `json:"has_more"`
}

// NotificationCountResponse represents a count of unread notifications
type NotificationCountResponse struct {
	UnreadCount uint `json:"unread_count"`
}

// NotificationCreatedResponse represents a response after creating a notification
type NotificationCreatedResponse struct {
	NotificationID uint `json:"notification_id"`
}

// NotificationReadResponse represents a response after marking a notification as read
type NotificationReadResponse struct {
	Success bool `json:"success"`
}
