package channel

// CreateChannelRequest represents a request to create a new notification channel
type CreateChannelRequest struct {
	ChannelCode string `json:"channel_code" binding:"required"`
	ChannelName string `json:"channel_name" binding:"required"`
	IsActive    bool   `json:"is_active"`
}

// UpdateChannelRequest represents a request to update an existing notification channel
type UpdateChannelRequest struct {
	ChannelName string `json:"channel_name" binding:"required"`
	IsActive    bool   `json:"is_active"`
}

// GetChannelRequest represents a request to get a specific notification channel
type GetChannelRequest struct {
	ChannelID uint `json:"channel_id" binding:"required"`
}

// GetChannelsRequest represents a request to list notification channels
type GetChannelsRequest struct {
	IsActive *bool  `json:"is_active"`
	Cursor   string `json:"cursor"`
	Limit    int    `json:"limit" binding:"required,min=1,max=100"`
}
