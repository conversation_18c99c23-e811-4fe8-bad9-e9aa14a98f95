package dto

import "time"

// EmailRequest đại diện cho yêu cầu gửi email
type EmailRequest struct {
	To          []string               `json:"to" binding:"required"`
	Subject     string                 `json:"subject" binding:"required"`
	Content     string                 `json:"content" binding:"required"`
	Body        string                 `json:"body,omitempty"` // Alternative to Content
	HTMLContent string                 `json:"html_content,omitempty"`
	From        string                 `json:"from,omitempty"`
	CC          []string               `json:"cc,omitempty"`
	BCC         []string               `json:"bcc,omitempty"`
	Template    string                 `json:"template,omitempty"`
	Variables   map[string]interface{} `json:"variables,omitempty"`
	UserID      string                 `json:"user_id,omitempty"` // For tracking purposes
}

// SMSRequest đại diện cho yêu cầu gửi SMS
type SMSRequest struct {
	To        []string               `json:"to" binding:"required"`
	Message   string                 `json:"message" binding:"required"`
	Template  string                 `json:"template,omitempty"`
	Variables map[string]interface{} `json:"variables,omitempty"`
	UserID    string                 `json:"user_id,omitempty"` // For tracking purposes
}

// PushRequest đại diện cho yêu cầu gửi push notification
type PushRequest struct {
	To        []string               `json:"to" binding:"required"`
	UserID    string                 `json:"user_id,omitempty"` // For user-specific push notifications
	Title     string                 `json:"title" binding:"required"`
	Body      string                 `json:"body" binding:"required"`
	Icon      string                 `json:"icon,omitempty"`
	Badge     int                    `json:"badge,omitempty"`
	Sound     string                 `json:"sound,omitempty"`
	Data      map[string]interface{} `json:"data,omitempty"`
	Template  string                 `json:"template,omitempty"`
	Variables map[string]interface{} `json:"variables,omitempty"`
}

// NotificationResponse đại diện cho phản hồi khi gửi thông báo
type NotificationResponse struct {
	ID         string    `json:"id"`
	Type       string    `json:"type"`
	Status     string    `json:"status"`
	Message    string    `json:"message,omitempty"`
	SentAt     time.Time `json:"sent_at"`
	Recipients int       `json:"recipients"`
}

// NotificationHistory đại diện cho lịch sử thông báo
type NotificationHistory struct {
	ID            string                 `json:"id"`
	UserID        string                 `json:"user_id"`
	Type          string                 `json:"type"`
	Title         string                 `json:"title,omitempty"`
	Message       string                 `json:"message"`
	Recipient     string                 `json:"recipient"`
	Subject       string                 `json:"subject,omitempty"`
	Content       string                 `json:"content"`
	Status        string                 `json:"status"`
	SentAt        time.Time              `json:"sent_at"`
	CreatedAt     time.Time              `json:"created_at"`
	UpdatedAt     time.Time              `json:"updated_at"`
	DeliveredAt   *time.Time             `json:"delivered_at,omitempty"`
	FailureReason string                 `json:"failure_reason,omitempty"`
	Metadata      map[string]interface{} `json:"metadata,omitempty"`
}

// NotificationFilter chứa các tham số lọc thông báo
type NotificationFilter struct {
	TenantID int    `json:"tenant_id"`
	UserID   int    `json:"user_id"`
	Type     string `json:"type"`
	Status   string `json:"status"`
	IsRead   *bool  `json:"is_read"`
	Limit    int    `json:"limit"`
	Offset   int    `json:"offset"`
}

// TemplateFilter chứa các tham số lọc mẫu thông báo
type TemplateFilter struct {
	TenantID int    `json:"tenant_id"`
	Code     string `json:"code"`
	Type     string `json:"type"`
	Status   string `json:"status"`
	Limit    int    `json:"limit"`
	Offset   int    `json:"offset"`
}

// ChannelFilter chứa các tham số lọc kênh thông báo
type ChannelFilter struct {
	TenantID int    `json:"tenant_id"`
	Code     string `json:"code"`
	Type     string `json:"type"`
	Status   string `json:"status"`
	Limit    int    `json:"limit"`
	Offset   int    `json:"offset"`
}

// PreferenceFilter chứa các tham số lọc tùy chọn thông báo
type PreferenceFilter struct {
	TenantID  int    `json:"tenant_id"`
	UserID    int    `json:"user_id"`
	ChannelID int    `json:"channel_id"`
	Type      string `json:"type"`
	IsEnabled *bool  `json:"is_enabled"`
	Limit     int    `json:"limit"`
	Offset    int    `json:"offset"`
}

// DeliveryFilter chứa các tham số lọc gửi thông báo
type DeliveryFilter struct {
	TenantID       int    `json:"tenant_id"`
	NotificationID int    `json:"notification_id"`
	ChannelID      int    `json:"channel_id"`
	UserID         int    `json:"user_id"`
	Status         string `json:"status"`
	Limit          int    `json:"limit"`
	Offset         int    `json:"offset"`
}

// TelegramFilter chứa các tham số lọc cấu hình Telegram
type TelegramFilter struct {
	TenantID   int   `json:"tenant_id"`
	UserID     int   `json:"user_id"`
	IsVerified *bool `json:"is_verified"`
	Limit      int   `json:"limit"`
	Offset     int   `json:"offset"`
}

// WebsocketFilter chứa các tham số lọc kết nối websocket
type WebsocketFilter struct {
	TenantID    int    `json:"tenant_id"`
	UserID      int    `json:"user_id"`
	DeviceID    string `json:"device_id"`
	IsConnected *bool  `json:"is_connected"`
	Limit       int    `json:"limit"`
	Offset      int    `json:"offset"`
}

// CreateNotificationRequest chứa thông tin yêu cầu tạo thông báo
type CreateNotificationRequest struct {
	TenantID int    `json:"tenant_id" binding:"required"`
	UserID   int    `json:"user_id" binding:"required"`
	Title    string `json:"title" binding:"required"`
	Content  string `json:"content" binding:"required"`
	Type     string `json:"type" binding:"required"`
}

// UpdateNotificationRequest chứa thông tin yêu cầu cập nhật thông báo
type UpdateNotificationRequest struct {
	Title   string `json:"title"`
	Content string `json:"content"`
	Type    string `json:"type"`
	Status  string `json:"status"`
}

// ListNotificationsResponse chứa danh sách thông báo
type ListNotificationsResponse struct {
	Items       []*NotificationResponse `json:"items"`
	TotalCount  int                     `json:"total_count"`
	NextCursor  *string                 `json:"next_cursor"`
	HasMoreData bool                    `json:"has_more_data"`
}

// CreateTemplateRequest chứa thông tin yêu cầu tạo mẫu thông báo
type CreateTemplateRequest struct {
	TenantID    int    `json:"tenant_id" binding:"required"`
	Code        string `json:"code" binding:"required"`
	Name        string `json:"name" binding:"required"`
	Description string `json:"description"`
	Content     string `json:"content" binding:"required"`
	Type        string `json:"type" binding:"required"`
}

// TemplateResponse chứa thông tin phản hồi mẫu thông báo
type TemplateResponse struct {
	ID          int       `json:"id"`
	TenantID    int       `json:"tenant_id"`
	Code        string    `json:"code"`
	Name        string    `json:"name"`
	Description string    `json:"description"`
	Content     string    `json:"content"`
	Type        string    `json:"type"`
	Status      string    `json:"status"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// UpdateTemplateRequest chứa thông tin yêu cầu cập nhật mẫu thông báo
type UpdateTemplateRequest struct {
	Name        string `json:"name"`
	Description string `json:"description"`
	Content     string `json:"content"`
	Type        string `json:"type"`
	Status      string `json:"status"`
}

// ListTemplatesResponse chứa danh sách mẫu thông báo
type ListTemplatesResponse struct {
	Items       []*TemplateResponse `json:"items"`
	TotalCount  int                 `json:"total_count"`
	NextCursor  *string             `json:"next_cursor"`
	HasMoreData bool                `json:"has_more_data"`
}
