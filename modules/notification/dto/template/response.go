package template

import (
	"time"
)

// TemplateResponse represents a notification template response
type TemplateResponse struct {
	TemplateID       uint       `json:"template_id"`
	Code             string    `json:"code"`
	TitleTemplate    string    `json:"title_template"`
	ContentTemplate  string    `json:"content_template"`
	NotificationType string    `json:"notification_type"`
	IsActive         bool      `json:"is_active"`
	CreatedAt        time.Time `json:"created_at"`
	UpdatedAt        time.Time `json:"updated_at"`
}

// TemplateListResponse represents a list of notification templates with pagination metadata
type TemplateListResponse struct {
	Templates []TemplateResponse `json:"templates"`
	Meta      PaginationMeta     `json:"meta"`
}

// PaginationMeta represents pagination metadata
type PaginationMeta struct {
	NextCursor string `json:"next_cursor,omitempty"`
	HasMore    bool   `json:"has_more"`
}

// TemplateCreatedResponse represents a response after creating a template
type TemplateCreatedResponse struct {
	TemplateID uint `json:"template_id"`
}

// TemplateUpdatedResponse represents a response after updating a template
type TemplateUpdatedResponse struct {
	Success bool `json:"success"`
}
