package template

// CreateTemplateRequest represents a request to create a notification template
type CreateTemplateRequest struct {
	Code             string `json:"code" binding:"required"`
	TitleTemplate    string `json:"title_template" binding:"required"`
	ContentTemplate  string `json:"content_template" binding:"required"`
	NotificationType string `json:"notification_type" binding:"required"`
	IsActive         bool   `json:"is_active"`
}

// UpdateTemplateRequest represents a request to update a notification template
type UpdateTemplateRequest struct {
	Code             string `json:"code" binding:"required"`
	TitleTemplate    string `json:"title_template" binding:"required"`
	ContentTemplate  string `json:"content_template" binding:"required"`
	NotificationType string `json:"notification_type" binding:"required"`
	IsActive         bool   `json:"is_active"`
}

// GetTemplateRequest represents a request to get a specific notification template
type GetTemplateRequest struct {
	TemplateID int `json:"template_id" binding:"required"`
}

// GetTemplateByCodeRequest represents a request to get a template by its code
type GetTemplateByCodeRequest struct {
	TemplateCode string `json:"template_code" binding:"required"`
}

// GetTemplatesRequest represents a request to list notification templates
type GetTemplatesRequest struct {
	NotificationType string `json:"notification_type"`
	IsActive         *bool  `json:"is_active"`
	Cursor           string `json:"cursor"`
	Limit            int    `json:"limit" binding:"required,min=1,max=100"`
}
