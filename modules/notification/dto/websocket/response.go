package websocket

import (
	"time"
)

// ConnectionResponse represents a WebSocket connection response
type ConnectionResponse struct {
	ConnectionID   string    `json:"connection_id"`
	UserID         uint       `json:"user_id"`
	ClientInfo     string    `json:"client_info,omitempty"`
	ConnectedAt    time.Time `json:"connected_at"`
	LastActiveAt   time.Time `json:"last_active_at"`
	DisconnectedAt time.Time `json:"disconnected_at,omitempty"`
	IsActive       bool      `json:"is_active"`
}

// ConnectionListResponse represents a list of WebSocket connections with pagination metadata
type ConnectionListResponse struct {
	Connections []ConnectionResponse `json:"connections"`
	Meta        PaginationMeta       `json:"meta"`
}

// PaginationMeta represents pagination metadata
type PaginationMeta struct {
	NextCursor string `json:"next_cursor,omitempty"`
	HasMore    bool   `json:"has_more"`
}

// ConnectionCreatedResponse represents a response after creating a connection
type ConnectionCreatedResponse struct {
	ConnectionID string `json:"connection_id"`
}

// ConnectionDisconnectedResponse represents a response after disconnecting a connection
type ConnectionDisconnectedResponse struct {
	Success bool `json:"success"`
}

// WebSocketMessage represents a message sent over WebSocket
type WebSocketMessage struct {
	Type    string      `json:"type"`
	Payload interface{} `json:"payload"`
}
