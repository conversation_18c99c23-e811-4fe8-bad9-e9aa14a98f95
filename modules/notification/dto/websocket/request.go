package websocket

// ConnectRequest represents a request to establish a WebSocket connection
type ConnectRequest struct {
	UserID     uint    `json:"user_id" binding:"required"`
	ClientInfo string `json:"client_info"`
}

// DisconnectRequest represents a request to disconnect a WebSocket connection
type DisconnectRequest struct {
	ConnectionID string `json:"connection_id" binding:"required"`
}

// GetConnectionsRequest represents a request to get WebSocket connections
type GetConnectionsRequest struct {
	UserID int    `json:"user_id"`
	Cursor string `json:"cursor"`
	Limit  int    `json:"limit" binding:"required,min=1,max=100"`
}
