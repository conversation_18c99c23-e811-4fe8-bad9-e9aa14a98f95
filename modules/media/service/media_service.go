package service

import (
	"context"
	"fmt"
	"mime/multipart"
	"path/filepath"
	"strings"
	"time"

	"wnapi/internal/pkg/logger"
	"wnapi/modules/media/dto"
	"wnapi/modules/media/internal"
)

// mediaService triển khai MediaService interface
type mediaService struct {
	repo        internal.Repository
	storageRepo internal.StorageRepository
	config      internal.MediaConfig
	logger      logger.Logger
}

// NewMediaService tạo một media service mới
func NewMediaService(repo internal.Repository, storageRepo internal.StorageRepository, config internal.MediaConfig, logger logger.Logger) internal.MediaService {
	return &mediaService{
		repo:        repo,
		storageRepo: storageRepo,
		config:      config,
		logger:      logger,
	}
}

// Upload xử lý upload media file
func (s *mediaService) Upload(ctx context.Context, tenantID uint, userID uint, file *multipart.FileHeader, req dto.UploadMediaRequest) (*dto.UploadMediaResponse, error) {
	// Validate file size
	if file.Size > s.config.MaxFileSize {
		return nil, internal.ErrFileTooLarge
	}

	// Validate file type
	if !s.isAllowedFileType(file.Header.Get("Content-Type")) {
		return nil, internal.ErrInvalidFileType
	}

	// Open file
	src, err := file.Open()
	if err != nil {
		s.logger.Error("Failed to open uploaded file", logger.String("error", err.Error()))
		return nil, internal.ErrUploadFailed
	}
	defer src.Close()

	// Read file data
	fileData := make([]byte, file.Size)
	_, err = src.Read(fileData)
	if err != nil {
		s.logger.Error("Failed to read uploaded file", logger.String("error", err.Error()))
		return nil, internal.ErrUploadFailed
	}

	// Generate object key
	objectKey := s.generateObjectKey(tenantID, file.Filename)

	// Upload to storage
	err = s.storageRepo.Upload(ctx, objectKey, fileData, file.Header.Get("Content-Type"))
	if err != nil {
		s.logger.Error("Failed to upload file to storage", logger.String("error", err.Error()))
		return nil, internal.ErrUploadFailed
	}

	// Create media entity
	media := &internal.Media{
		TenantID:         tenantID,
		MediaType:        s.detectMediaType(file.Header.Get("Content-Type")),
		Filename:         s.generateFilename(file.Filename),
		OriginalFilename: file.Filename,
		ObjectKey:        objectKey,
		ContentType:      file.Header.Get("Content-Type"),
		Size:             file.Size,
		Status:           internal.MediaStatusPending,
		Description:      req.Description,
		UploadedBy:       userID,
		IsPublic:         req.IsPublic,
		FolderID:         req.FolderID,
	}

	// Save to database
	err = s.repo.CreateMedia(ctx, tenantID, media)
	if err != nil {
		s.logger.Error("Failed to save media to database", logger.String("error", err.Error()))
		// Try to cleanup uploaded file
		_ = s.storageRepo.Delete(ctx, objectKey)
		return nil, internal.ErrUploadFailed
	}

	// Get public URL if needed
	if req.IsPublic {
		publicURL, err := s.storageRepo.GetURL(ctx, objectKey)
		if err == nil {
			media.PublicURL = publicURL
			// Update media with public URL
			_ = s.repo.UpdateMedia(ctx, tenantID, media)
		}
	}

	return s.toUploadResponse(media), nil
}

// GetByID lấy thông tin media theo ID
func (s *mediaService) GetByID(ctx context.Context, tenantID uint, id uint) (*dto.MediaResponse, error) {
	media, err := s.repo.GetMediaByID(ctx, tenantID, id)
	if err != nil {
		return nil, err
	}

	return s.toMediaResponse(media), nil
}

// List lấy danh sách media
func (s *mediaService) List(ctx context.Context, tenantID uint, req dto.ListMediaRequest) (*dto.ListMediaResponse, error) {
	params := internal.ListMediaParams{
		MediaType: req.MediaType,
		Status:    req.Status,
		IsPublic:  req.IsPublic,
		FolderID:  req.FolderID,
		Search:    req.Search,
		Cursor:    req.Cursor,
		Limit:     req.Limit,
	}

	mediaList, nextCursor, err := s.repo.ListMedia(ctx, tenantID, params)
	if err != nil {
		return nil, err
	}

	// Convert to response DTOs
	responses := make([]*dto.MediaResponse, len(mediaList))
	for i, media := range mediaList {
		responses[i] = s.toMediaResponse(media)
	}

	return &dto.ListMediaResponse{
		Data:       responses,
		NextCursor: nextCursor,
		HasMore:    nextCursor != "",
	}, nil
}

// Update cập nhật thông tin media
func (s *mediaService) Update(ctx context.Context, tenantID uint, id uint, req dto.UpdateMediaRequest) (*dto.MediaResponse, error) {
	// Get existing media
	media, err := s.repo.GetMediaByID(ctx, tenantID, id)
	if err != nil {
		return nil, err
	}

	// Update fields
	if req.Description != nil {
		media.Description = *req.Description
	}
	if req.IsPublic != nil {
		media.IsPublic = *req.IsPublic
		// Update public URL if needed
		if *req.IsPublic && media.PublicURL == "" {
			publicURL, err := s.storageRepo.GetURL(ctx, media.ObjectKey)
			if err == nil {
				media.PublicURL = publicURL
			}
		} else if !*req.IsPublic {
			media.PublicURL = ""
		}
	}
	if req.FolderID != nil {
		media.FolderID = req.FolderID
	}

	// Save changes
	err = s.repo.UpdateMedia(ctx, tenantID, media)
	if err != nil {
		return nil, err
	}

	return s.toMediaResponse(media), nil
}

// Delete xóa media
func (s *mediaService) Delete(ctx context.Context, tenantID uint, id uint, permanent bool) error {
	// Get media info
	media, err := s.repo.GetMediaByID(ctx, tenantID, id)
	if err != nil {
		return err
	}

	// Delete from database
	err = s.repo.DeleteMedia(ctx, tenantID, id)
	if err != nil {
		return err
	}

	// Delete from storage if permanent
	if permanent {
		err = s.storageRepo.Delete(ctx, media.ObjectKey)
		if err != nil {
			s.logger.Warn("Failed to delete file from storage", logger.String("objectKey", media.ObjectKey), logger.String("error", err.Error()))
		}
	}

	return nil
}

// GetFile tải xuống file media
func (s *mediaService) GetFile(ctx context.Context, tenantID uint, id uint) ([]byte, string, error) {
	// Get media info
	media, err := s.repo.GetMediaByID(ctx, tenantID, id)
	if err != nil {
		return nil, "", err
	}

	// Download from storage
	data, err := s.storageRepo.Download(ctx, media.ObjectKey)
	if err != nil {
		return nil, "", err
	}

	return data, media.ContentType, nil
}

// Helper methods

func (s *mediaService) isAllowedFileType(contentType string) bool {
	for _, allowedType := range s.config.AllowedTypes {
		if allowedType == contentType {
			return true
		}
	}
	return false
}

func (s *mediaService) detectMediaType(contentType string) internal.MediaType {
	switch {
	case strings.HasPrefix(contentType, "image/"):
		return internal.MediaTypeImage
	case strings.HasPrefix(contentType, "video/"):
		return internal.MediaTypeVideo
	case strings.HasPrefix(contentType, "audio/"):
		return internal.MediaTypeAudio
	case strings.Contains(contentType, "pdf") || strings.Contains(contentType, "document") || strings.Contains(contentType, "text"):
		return internal.MediaTypeDocument
	default:
		return internal.MediaTypeOther
	}
}

func (s *mediaService) generateObjectKey(tenantID uint, filename string) string {
	timestamp := time.Now().Unix()
	ext := filepath.Ext(filename)
	return fmt.Sprintf("tenant_%d/%d_%s%s", tenantID, timestamp, strings.ReplaceAll(filename, ext, ""), ext)
}

func (s *mediaService) generateFilename(originalFilename string) string {
	timestamp := time.Now().Unix()
	ext := filepath.Ext(originalFilename)
	name := strings.TrimSuffix(originalFilename, ext)
	return fmt.Sprintf("%s_%d%s", name, timestamp, ext)
}

func (s *mediaService) toUploadResponse(media *internal.Media) *dto.UploadMediaResponse {
	return &dto.UploadMediaResponse{
		ID:               media.ID,
		TenantID:         media.TenantID,
		MediaType:        media.MediaType,
		Filename:         media.Filename,
		OriginalFilename: media.OriginalFilename,
		ContentType:      media.ContentType,
		Size:             media.Size,
		Status:           media.Status,
		PublicURL:        media.PublicURL,
		Description:      media.Description,
		IsPublic:         media.IsPublic,
		FolderID:         media.FolderID,
		CreatedAt:        media.CreatedAt.Format(time.RFC3339),
		UpdatedAt:        media.UpdatedAt.Format(time.RFC3339),
	}
}

func (s *mediaService) toMediaResponse(media *internal.Media) *dto.MediaResponse {
	return &dto.MediaResponse{
		ID:               media.ID,
		TenantID:         media.TenantID,
		MediaType:        media.MediaType,
		Filename:         media.Filename,
		OriginalFilename: media.OriginalFilename,
		ContentType:      media.ContentType,
		Size:             media.Size,
		Status:           media.Status,
		PublicURL:        media.PublicURL,
		Description:      media.Description,
		IsPublic:         media.IsPublic,
		FolderID:         media.FolderID,
		CreatedAt:        media.CreatedAt.Format(time.RFC3339),
		UpdatedAt:        media.UpdatedAt.Format(time.RFC3339),
	}
}
