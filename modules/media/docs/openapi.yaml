openapi: 3.1.0
info:
  title: Media Module API
  description: API specification for the Media module
  version: 1.0.0
  contact:
    name: Web New Team
servers:
  - url: http://wn-api.local
    description: Media API endpoint

security:
  - bearerAuth: []

tags:
  - name: Media
    description: Media resource management endpoints
  - name: MediaFolder
    description: Media folder management endpoints

paths:
  /api/v1/media:
    get:
      summary: Lấy danh sách media
      description: Lấy danh sách media với phân trang theo cursor
      tags:
        - Media
      parameters:
        - $ref: '#/components/parameters/tenantIdParam'
        - name: cursor
          in: query
          description: Cursor để phân trang
          schema:
            type: string
            nullable: true
        - name: limit
          in: query
          description: Số lượng bản ghi mỗi trang
          schema:
            type: integer
            default: 20
        - name: type
          in: query
          description: Lọc theo loại media
          schema:
            type: string
            enum: [image, video, audio, document]
        - name: search
          in: query
          description: Tìm kiếm theo tên file
          schema:
            type: string
      responses:
        '200':
          description: Thành công
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MediaListResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
    post:
      summary: Tải lên media
      description: Tải lên file media mới
      tags:
        - Media
      security:
        - bearerAuth: []
      parameters:
        - $ref: '#/components/parameters/tenantIdParam'
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              required:
                - file
              properties:
                file:
                  type: string
                  format: binary
                  description: File cần tải lên
                alt_text:
                  type: string
                  description: Văn bản thay thế cho hình ảnh
                description:
                  type: string
                  description: Mô tả về file
      responses:
        '201':
          description: Tải lên thành công
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MediaResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '413':
          description: File quá lớn
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '415':
          description: Định dạng file không được hỗ trợ
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /api/v1/media/{mediaId}:
    get:
      summary: Lấy thông tin chi tiết media
      description: Lấy thông tin chi tiết của một media cụ thể
      tags:
        - Media
      parameters:
        - $ref: '#/components/parameters/tenantIdParam'
        - $ref: '#/components/parameters/mediaIdParam'
      responses:
        '200':
          description: Thành công
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MediaResponse'
        '404':
          $ref: '#/components/responses/NotFound'
        '401':
          $ref: '#/components/responses/Unauthorized'
    patch:
      summary: Cập nhật thông tin media
      description: Cập nhật thông tin của media
      tags:
        - Media
      security:
        - bearerAuth: []
      parameters:
        - $ref: '#/components/parameters/tenantIdParam'
        - $ref: '#/components/parameters/mediaIdParam'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MediaUpdate'
      responses:
        '200':
          description: Cập nhật thành công
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MediaResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
    delete:
      summary: Xóa media
      description: Xóa một media
      tags:
        - Media
      security:
        - bearerAuth: []
      parameters:
        - $ref: '#/components/parameters/tenantIdParam'
        - $ref: '#/components/parameters/mediaIdParam'
      responses:
        '204':
          description: Xóa thành công
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'

  /api/v1/media/{mediaId}/url:
    get:
      summary: Lấy URL của media
      description: Lấy URL truy cập của một media cụ thể
      tags:
        - Media
      parameters:
        - $ref: '#/components/parameters/tenantIdParam'
        - $ref: '#/components/parameters/mediaIdParam'
        - name: version
          in: query
          description: Phiên bản của media (original, thumbnail, medium, large)
          schema:
            type: string
            enum: [original, thumbnail, medium, large]
            default: original
        - name: expires
          in: query
          description: Thời gian hết hạn của URL (tính bằng phút)
          schema:
            type: integer
            default: 5
      responses:
        '200':
          description: Thành công
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    $ref: '#/components/schemas/Status'
                  data:
                    type: object
                    properties:
                      url:
                        type: string
                        description: URL truy cập của media
                      expires_at:
                        type: string
                        format: date-time
                        description: Thời gian hết hạn của URL
        '404':
          $ref: '#/components/responses/NotFound'
        '401':
          $ref: '#/components/responses/Unauthorized'

  /api/v1/media-folders:
    get:
      summary: Lấy danh sách thư mục
      description: Lấy danh sách thư mục media với phân trang theo cursor
      tags:
        - MediaFolder
      parameters:
        - $ref: '#/components/parameters/tenantIdParam'
        - name: cursor
          in: query
          description: Cursor để phân trang
          schema:
            type: string
            nullable: true
        - name: limit
          in: query
          description: Số lượng bản ghi mỗi trang
          schema:
            type: integer
            default: 20
        - name: search
          in: query
          description: Tìm kiếm theo tên thư mục
          schema:
            type: string
      responses:
        '200':
          description: Thành công
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MediaFolderListResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
    post:
      summary: Tạo thư mục mới
      description: Tạo thư mục media mới
      tags:
        - MediaFolder
      security:
        - bearerAuth: []
      parameters:
        - $ref: '#/components/parameters/tenantIdParam'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateFolderRequest'
      responses:
        '201':
          description: Tạo thành công
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MediaFolderResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'

  /api/v1/media-folders/{folderId}:
    get:
      summary: Lấy thông tin chi tiết thư mục
      description: Lấy thông tin chi tiết của một thư mục cụ thể
      tags:
        - MediaFolder
      parameters:
        - $ref: '#/components/parameters/tenantIdParam'
        - $ref: '#/components/parameters/folderIdParam'
      responses:
        '200':
          description: Thành công
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MediaFolderResponse'
        '404':
          $ref: '#/components/responses/NotFound'
        '401':
          $ref: '#/components/responses/Unauthorized'
    patch:
      summary: Cập nhật thông tin thư mục
      description: Cập nhật thông tin của thư mục
      tags:
        - MediaFolder
      security:
        - bearerAuth: []
      parameters:
        - $ref: '#/components/parameters/tenantIdParam'
        - $ref: '#/components/parameters/folderIdParam'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateFolderRequest'
      responses:
        '200':
          description: Cập nhật thành công
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MediaFolderResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
    delete:
      summary: Xóa thư mục
      description: Xóa một thư mục
      tags:
        - MediaFolder
      security:
        - bearerAuth: []
      parameters:
        - $ref: '#/components/parameters/tenantIdParam'
        - $ref: '#/components/parameters/folderIdParam'
      responses:
        '200':
          description: Xóa thành công
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  parameters:
    tenantIdParam:
      name: X-Tenant-ID
      in: header
      required: true
      description: ID của tenant
      schema:
        type: integer
        format: int64
    mediaIdParam:
      name: mediaId
      in: path
      required: true
      description: ID của media
      schema:
        type: integer
        format: int64
    folderIdParam:
      name: folderId
      in: path
      required: true
      description: ID của thư mục
      schema:
        type: integer
        format: int64

  responses:
    BadRequest:
      description: Yêu cầu không hợp lệ
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
    Unauthorized:
      description: Chưa xác thực - Token bị thiếu hoặc không hợp lệ
      content:
        application/json:
          schema:
            type: object
            properties:
              status:
                type: object
                properties:
                  code:
                    type: integer
                    example: 401
                  message:
                    type: string
                    example: Unauthorized
                  success:
                    type: boolean
                    example: false
                  error_code:
                    type: string
                    example: UNAUTHORIZED
                  path:
                    type: string
                    example: /api/v1/media
                  timestamp:
                    type: string
                    format: date-time
                  details:
                    type: array
                    items:
                      type: object
                      properties:
                        message:
                          type: string
                          example: Token bị thiếu hoặc không hợp lệ
    Forbidden:
      description: Bị cấm - Người dùng không có quyền truy cập
      content:
        application/json:
          schema:
            type: object
            properties:
              status:
                type: object
                properties:
                  code:
                    type: integer
                    example: 403
                  message:
                    type: string
                    example: Forbidden
                  success:
                    type: boolean
                    example: false
                  error_code:
                    type: string
                    example: FORBIDDEN
                  path:
                    type: string
                    example: /api/v1/media
                  timestamp:
                    type: string
                    format: date-time
                  details:
                    type: array
                    items:
                      type: object
                      properties:
                        message:
                          type: string
                          example: Người dùng không có quyền truy cập
    NotFound:
      description: Không tìm thấy tài nguyên
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'

  schemas:
    Status:
      type: object
      properties:
        code:
          type: integer
          description: Mã HTTP
          example: 200
        message:
          type: string
          description: Thông báo
          example: Operation completed successfully
        success:
          type: boolean
          description: Trạng thái thành công
          example: true
        error_code:
          type: string
          nullable: true
          description: Mã lỗi ứng dụng
          example: null
        path:
          type: string
          description: Đường dẫn API
          example: /api/v1/media
        timestamp:
          type: string
          format: date-time
          description: Thời gian xử lý yêu cầu
        details:
          type: array
          nullable: true
          description: Chi tiết lỗi
          items:
            type: object
          example: null

    Media:
      type: object
      properties:
        id:
          type: integer
          format: int64
          description: ID duy nhất của media
        tenant_id:
          type: integer
          format: int64
          description: ID của tenant sở hữu media
        file_name:
          type: string
          description: Tên file
        original_name:
          type: string
          description: Tên gốc của file khi tải lên
        mime_type:
          type: string
          description: Kiểu MIME của file
        extension:
          type: string
          description: Phần mở rộng của file
        size:
          type: integer
          description: Kích thước file (byte)
        media_type:
          type: string
          enum: [image, video, audio, document]
          description: Loại media
        path:
          type: string
          description: Đường dẫn trong hệ thống lưu trữ
        url:
          type: string
          description: URL tạm thời để truy cập file
        alt_text:
          type: string
          description: Văn bản thay thế cho hình ảnh
        description:
          type: string
          description: Mô tả về file
        width:
          type: integer
          nullable: true
          description: Chiều rộng (nếu là hình ảnh)
        height:
          type: integer
          nullable: true
          description: Chiều cao (nếu là hình ảnh)
        duration:
          type: integer
          nullable: true
          description: Thời lượng (giây, nếu là audio/video)
        is_public:
          type: boolean
          description: Có công khai hay không
        versions:
          type: array
          description: Các phiên bản của media
          items:
            type: object
            properties:
              version:
                type: string
                description: Tên phiên bản
              path:
                type: string
                description: Đường dẫn trong hệ thống lưu trữ
              width:
                type: integer
                nullable: true
                description: Chiều rộng
              height:
                type: integer
                nullable: true
                description: Chiều cao
        tags:
          type: array
          description: Các tag của media
          items:
            type: string
        created_at:
          type: string
          format: date-time
          description: Thời gian tạo
        updated_at:
          type: string
          format: date-time
          description: Thời gian cập nhật gần nhất
        created_by:
          type: integer
          format: int64
          description: ID của người tạo
        updated_by:
          type: integer
          format: int64
          description: ID của người cập nhật gần nhất

    MediaUpdate:
      type: object
      properties:
        alt_text:
          type: string
          description: Văn bản thay thế cho hình ảnh
        description:
          type: string
          description: Mô tả về file
        is_public:
          type: boolean
          description: Có công khai hay không
        tags:
          type: array
          description: Các tag của media
          items:
            type: string

    MediaResponse:
      type: object
      properties:
        status:
          $ref: '#/components/schemas/Status'
        data:
          $ref: '#/components/schemas/Media'

    MediaListResponse:
      type: object
      properties:
        status:
          $ref: '#/components/schemas/Status'
        data:
          type: array
          items:
            $ref: '#/components/schemas/Media'
        meta:
          type: object
          properties:
            next_cursor:
              type: string
              nullable: true
              description: Cursor cho trang tiếp theo
            has_more:
              type: boolean
              description: Còn dữ liệu hay không

    CreateFolderRequest:
      type: object
      required:
        - name
      properties:
        name:
          type: string
          description: Tên thư mục
        description:
          type: string
          description: Mô tả về thư mục
        parent_id:
          type: integer
          format: int64
          nullable: true
          description: ID của thư mục cha

    UpdateFolderRequest:
      type: object
      properties:
        name:
          type: string
          description: Tên thư mục
        description:
          type: string
          description: Mô tả về thư mục
        parent_id:
          type: integer
          format: int64
          nullable: true
          description: ID của thư mục cha

    MediaFolder:
      type: object
      properties:
        id:
          type: integer
          format: int64
          description: ID của thư mục
        name:
          type: string
          description: Tên thư mục
        description:
          type: string
          nullable: true
          description: Mô tả về thư mục
        parent_id:
          type: integer
          format: int64
          nullable: true
          description: ID của thư mục cha
        tenant_id:
          type: integer
          format: int64
          description: ID của tenant
        created_at:
          type: string
          format: date-time
          description: Thời gian tạo
        updated_at:
          type: string
          format: date-time
          description: Thời gian cập nhật gần nhất
        created_by:
          type: integer
          format: int64
          description: ID của người tạo
        updated_by:
          type: integer
          format: int64
          description: ID của người cập nhật gần nhất

    MediaFolderResponse:
      type: object
      properties:
        status:
          $ref: '#/components/schemas/Status'
        data:
          $ref: '#/components/schemas/MediaFolder'

    MediaFolderListResponse:
      type: object
      properties:
        status:
          $ref: '#/components/schemas/Status'
        data:
          type: array
          items:
            $ref: '#/components/schemas/MediaFolder'
        meta:
          type: object
          properties:
            next_cursor:
              type: string
              nullable: true
              description: Cursor cho trang tiếp theo
            has_more:
              type: boolean
              description: Còn dữ liệu hay không

    Error:
      type: object
      properties:
        status:
          type: object
          properties:
            code:
              type: integer
              description: Mã HTTP
              example: 400
            message:
              type: string
              description: Thông báo lỗi
              example: Yêu cầu không hợp lệ
            success:
              type: boolean
              description: Trạng thái thành công
              example: false
            error_code:
              type: string
              description: Mã lỗi ứng dụng
              example: VALIDATION_ERROR
            path:
              type: string
              description: Đường dẫn API
              example: /api/v1/media
            timestamp:
              type: string
              format: date-time
              description: Thời gian lỗi xảy ra
            details:
              type: array
              description: Chi tiết lỗi
              items:
                type: object
                properties:
                  field:
                    type: string
                    description: Trường có lỗi (đối với lỗi validation)
                    example: file
                  message:
                    type: string
                    description: Thông báo lỗi chi tiết
                    example: File không được để trống 