package mysql

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"wnapi/internal/database"
	"wnapi/internal/pkg/logger"
	"wnapi/modules/media/internal"

	"gorm.io/gorm"
)

// mysqlRepository triển khai Repository interface sử dụng GORM
type mysqlRepository struct {
	db     *gorm.DB
	logger logger.Logger
}

// NewMySQLRepository tạo một repository mới
func NewMySQLRepository(dbManager *database.Manager, logger logger.Logger) (internal.Repository, error) {
	if dbManager == nil {
		return nil, errors.New("database manager không được để trống")
	}

	gormDB := dbManager.GetGormDB()
	if gormDB == nil {
		return nil, errors.New("không thể kết nối tới cơ sở dữ liệu")
	}

	return &mysqlRepository{
		db:     gormDB,
		logger: logger,
	}, nil
}

// CreateMedia tạo media mới
func (r *mysqlRepository) CreateMedia(ctx context.Context, tenantID uint, media *internal.Media) error {
	media.TenantID = tenantID
	media.CreatedAt = time.Now()
	media.UpdatedAt = time.Now()

	if media.Status == "" {
		media.Status = internal.MediaStatusPending
	}

	if media.SchemaVersion == 0 {
		media.SchemaVersion = 1
	}

	return r.db.WithContext(ctx).Create(media).Error
}

// GetMediaByID lấy media theo ID
func (r *mysqlRepository) GetMediaByID(ctx context.Context, tenantID uint, id uint) (*internal.Media, error) {
	var media internal.Media
	err := r.db.WithContext(ctx).Where("id = ? AND tenant_id = ?", id, tenantID).First(&media).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, internal.ErrMediaNotFound
		}
		return nil, err
	}
	return &media, nil
}

// UpdateMedia cập nhật thông tin media
func (r *mysqlRepository) UpdateMedia(ctx context.Context, tenantID uint, media *internal.Media) error {
	media.TenantID = tenantID
	media.UpdatedAt = time.Now()

	result := r.db.WithContext(ctx).Where("tenant_id = ?", tenantID).Save(media)
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return internal.ErrMediaNotFound
	}
	return nil
}

// DeleteMedia xóa media (soft delete)
func (r *mysqlRepository) DeleteMedia(ctx context.Context, tenantID uint, id uint) error {
	result := r.db.WithContext(ctx).Where("tenant_id = ?", tenantID).Delete(&internal.Media{}, id)
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return internal.ErrMediaNotFound
	}
	return nil
}

// ListMedia lấy danh sách media với phân trang cursor-based
func (r *mysqlRepository) ListMedia(ctx context.Context, tenantID uint, params internal.ListMediaParams) ([]*internal.Media, string, error) {
	// Sử dụng raw query cho list operations theo pattern
	var conditions []string
	var args []interface{}

	// Base condition
	conditions = append(conditions, "tenant_id = ?")
	args = append(args, tenantID)

	// Filter conditions
	if params.MediaType != nil {
		conditions = append(conditions, "media_type = ?")
		args = append(args, *params.MediaType)
	}

	if params.Status != nil {
		conditions = append(conditions, "status = ?")
		args = append(args, *params.Status)
	}

	if params.IsPublic != nil {
		conditions = append(conditions, "is_public = ?")
		args = append(args, *params.IsPublic)
	}

	if params.FolderID != nil {
		conditions = append(conditions, "folder_id = ?")
		args = append(args, *params.FolderID)
	}

	if params.Search != "" {
		conditions = append(conditions, "(filename LIKE ? OR description LIKE ?)")
		searchTerm := "%" + params.Search + "%"
		args = append(args, searchTerm, searchTerm)
	}

	// Cursor-based pagination
	if params.Cursor != "" {
		conditions = append(conditions, "id > ?")
		args = append(args, params.Cursor)
	}

	// Set default limit
	limit := 20
	if params.Limit > 0 && params.Limit <= 100 {
		limit = params.Limit
	}

	// Build query
	whereClause := strings.Join(conditions, " AND ")
	query := fmt.Sprintf(`
		SELECT id, tenant_id, media_type, filename, original_filename, object_key, 
		       content_type, size, status, public_url, description, uploaded_by, 
		       checksum, is_public, folder_id, schema_version, created_at, updated_at, deleted_at
		FROM media 
		WHERE %s AND deleted_at IS NULL
		ORDER BY id ASC 
		LIMIT ?
	`, whereClause)

	args = append(args, limit)

	var mediaList []*internal.Media
	err := r.db.WithContext(ctx).Raw(query, args...).Scan(&mediaList).Error
	if err != nil {
		return nil, "", err
	}

	// Calculate next cursor
	var nextCursor string
	if len(mediaList) == limit {
		nextCursor = fmt.Sprintf("%d", mediaList[len(mediaList)-1].ID)
	}

	return mediaList, nextCursor, nil
}

// CreateFolder tạo folder mới
func (r *mysqlRepository) CreateFolder(ctx context.Context, tenantID uint, folder *internal.MediaFolder) error {
	folder.TenantID = tenantID
	folder.CreatedAt = time.Now()
	folder.UpdatedAt = time.Now()

	return r.db.WithContext(ctx).Create(folder).Error
}

// GetFolderByID lấy folder theo ID
func (r *mysqlRepository) GetFolderByID(ctx context.Context, tenantID uint, id uint) (*internal.MediaFolder, error) {
	var folder internal.MediaFolder
	err := r.db.WithContext(ctx).Where("id = ? AND tenant_id = ?", id, tenantID).First(&folder).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, internal.ErrFolderNotFound
		}
		return nil, err
	}
	return &folder, nil
}

// GetFolderBySlug lấy folder theo slug
func (r *mysqlRepository) GetFolderBySlug(ctx context.Context, tenantID uint, slug string) (*internal.MediaFolder, error) {
	var folder internal.MediaFolder
	err := r.db.WithContext(ctx).Where("slug = ? AND tenant_id = ?", slug, tenantID).First(&folder).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, internal.ErrFolderNotFound
		}
		return nil, err
	}
	return &folder, nil
}

// UpdateFolder cập nhật thông tin folder
func (r *mysqlRepository) UpdateFolder(ctx context.Context, tenantID uint, folder *internal.MediaFolder) error {
	folder.TenantID = tenantID
	folder.UpdatedAt = time.Now()

	result := r.db.WithContext(ctx).Where("tenant_id = ?", tenantID).Save(folder)
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return internal.ErrFolderNotFound
	}
	return nil
}

// DeleteFolder xóa folder (soft delete)
func (r *mysqlRepository) DeleteFolder(ctx context.Context, tenantID uint, id uint) error {
	result := r.db.WithContext(ctx).Where("tenant_id = ?", tenantID).Delete(&internal.MediaFolder{}, id)
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return internal.ErrFolderNotFound
	}
	return nil
}

// ListFolders lấy danh sách folder với phân trang cursor-based
func (r *mysqlRepository) ListFolders(ctx context.Context, tenantID uint, params internal.ListFolderParams) ([]*internal.MediaFolder, string, error) {
	// Sử dụng raw query cho list operations theo pattern
	var conditions []string
	var args []interface{}

	// Base condition
	conditions = append(conditions, "tenant_id = ?")
	args = append(args, tenantID)

	// Filter conditions
	if params.ParentID != nil {
		conditions = append(conditions, "parent_id = ?")
		args = append(args, *params.ParentID)
	}

	if params.IsPublic != nil {
		conditions = append(conditions, "is_public = ?")
		args = append(args, *params.IsPublic)
	}

	if params.Search != "" {
		conditions = append(conditions, "(name LIKE ? OR description LIKE ?)")
		searchTerm := "%" + params.Search + "%"
		args = append(args, searchTerm, searchTerm)
	}

	// Cursor-based pagination
	if params.Cursor != "" {
		conditions = append(conditions, "id > ?")
		args = append(args, params.Cursor)
	}

	// Set default limit
	limit := 20
	if params.Limit > 0 && params.Limit <= 100 {
		limit = params.Limit
	}

	// Build query
	whereClause := strings.Join(conditions, " AND ")
	query := fmt.Sprintf(`
		SELECT id, tenant_id, name, slug, description, parent_id, is_public, 
		       created_by, created_at, updated_at, deleted_at
		FROM media_folders 
		WHERE %s AND deleted_at IS NULL
		ORDER BY id ASC 
		LIMIT ?
	`, whereClause)

	args = append(args, limit)

	var folderList []*internal.MediaFolder
	err := r.db.WithContext(ctx).Raw(query, args...).Scan(&folderList).Error
	if err != nil {
		return nil, "", err
	}

	// Calculate next cursor
	var nextCursor string
	if len(folderList) == limit {
		nextCursor = fmt.Sprintf("%d", folderList[len(folderList)-1].ID)
	}

	return folderList, nextCursor, nil
}
