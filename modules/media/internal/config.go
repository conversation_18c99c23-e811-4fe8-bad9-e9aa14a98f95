package internal

import (
	"time"

	"github.com/caarlos0/env/v6"
)

// LoadMediaConfig đọc cấu hình từ biến môi trường
func LoadMediaConfig() (*MediaConfig, error) {
	config := &MediaConfig{}
	if err := env.Parse(config); err != nil {
		return nil, err
	}

	// Thiết lập giá trị mặc định cho AllowedTypes nếu không được cấu hình
	if len(config.AllowedTypes) == 0 {
		config.AllowedTypes = []string{
			"image/jpeg", "image/png", "image/gif", "image/webp",
			"video/mp4", "video/avi", "video/mov", "video/wmv",
			"audio/mp3", "audio/wav", "audio/ogg", "audio/m4a",
			"application/pdf", "application/msword", "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
			"application/vnd.ms-excel", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
			"text/plain", "text/csv",
		}
	}

	return config, nil
}

// GetDefaultMediaConfig trả về cấu hình mặc định
func GetDefaultMediaConfig() *MediaConfig {
	return &MediaConfig{
		StorageType:      "s3",
		MaxFileSize:      104857600, // 100MB
		ImageQuality:     85,
		EnableProcessing: true,
		AllowedTypes: []string{
			"image/jpeg", "image/png", "image/gif", "image/webp",
			"video/mp4", "video/avi", "video/mov", "video/wmv",
			"audio/mp3", "audio/wav", "audio/ogg", "audio/m4a",
			"application/pdf", "application/msword", "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
			"application/vnd.ms-excel", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
			"text/plain", "text/csv",
		},
		Message: "Xin chào từ module Media!",
	}
}
