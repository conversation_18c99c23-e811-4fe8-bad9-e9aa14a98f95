package dto

import "wnapi/modules/media/internal"

// ListMediaRequest đị<PERSON> ngh<PERSON> request để lấy danh sách media
type ListMediaRequest struct {
	MediaType *internal.MediaType  `json:"media_type,omitempty" form:"media_type"`
	Status    *internal.MediaStatus `json:"status,omitempty" form:"status"`
	IsPublic  *bool                `json:"is_public,omitempty" form:"is_public"`
	FolderID  *uint                `json:"folder_id,omitempty" form:"folder_id"`
	Search    string               `json:"search,omitempty" form:"search"`
	Cursor    string               `json:"cursor,omitempty" form:"cursor"`
	Limit     int                  `json:"limit,omitempty" form:"limit" validate:"min=1,max=100"`
}

// ListMediaResponse định nghĩa response cho danh sách media
type ListMediaResponse struct {
	Data       []*MediaResponse `json:"data"`
	NextCursor string           `json:"next_cursor,omitempty"`
	Has<PERSON><PERSON>    bool             `json:"has_more"`
	Total      int              `json:"total,omitempty"`
}

// MediaResponse định nghĩa response cho một media item
type MediaResponse struct {
	ID               uint                `json:"id"`
	TenantID         uint                `json:"tenant_id"`
	MediaType        internal.MediaType  `json:"media_type"`
	Filename         string              `json:"filename"`
	OriginalFilename string              `json:"original_filename"`
	ContentType      string              `json:"content_type"`
	Size             int64               `json:"size"`
	Status           internal.MediaStatus `json:"status"`
	PublicURL        string              `json:"public_url,omitempty"`
	Description      string              `json:"description,omitempty"`
	IsPublic         bool                `json:"is_public"`
	FolderID         *uint               `json:"folder_id,omitempty"`
	CreatedAt        string              `json:"created_at"`
	UpdatedAt        string              `json:"updated_at"`
}
