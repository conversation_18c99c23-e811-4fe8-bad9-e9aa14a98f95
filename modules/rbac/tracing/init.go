package tracing

import (
	"log"

	"github.com/opentracing/opentracing-go"
	"go.opentelemetry.io/otel/trace"

	"wnapi/modules/rbac/configs"
)

var (
	// Tracer là tracer của OpenTracing
	Tracer opentracing.Tracer
	// OtelTracer là tracer của OpenTelemetry
	OtelTracer trace.Tracer
)

// InitTracing khởi tạo hệ thống tracing dựa trên cấu hình được cung cấp
func InitTracing(config configs.TracingConfig) error {
	if !config.Enabled {
		log.Println("Tracing is disabled")
		return nil
	}

	if config.ServiceName == "" {
		log.Println("Warning: Service name is not provided for tracing, using default")
		config.ServiceName = "rbac-service"
	}

	log.Printf("Tracing initialized with exporter type: %s\n", config.ExporterType)
	return nil
}

// CloseTracer đóng tracer khi ứng dụng kết thúc
func CloseTracer() {
	// Đóng tracer nếu cần thiết (không làm gì cho đơn giản)
	log.Println("Closing tracer")
}

// GetTracer trả về OpenTracing tracer
func GetTracer() opentracing.Tracer {
	return Tracer
}

// IsTracingEnabled kiểm tra xem tracing có được bật hay không
func IsTracingEnabled(config configs.TracingConfig) bool {
	return config.Enabled
}

// GetExporterType trả về loại exporter được cấu hình
func GetExporterType(config configs.TracingConfig) string {
	return config.ExporterType
}
