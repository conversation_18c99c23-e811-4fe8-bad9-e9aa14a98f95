package tracing

import (
	"context"
	"fmt"

	"github.com/gin-gonic/gin"
	"github.com/opentracing/opentracing-go"
	"github.com/opentracing/opentracing-go/ext"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"

	"wnapi/modules/rbac/configs"
)

// StartSpan tạo một span mới với tên đ<PERSON>c cung cấp
func StartSpan(ctx context.Context, name string, config configs.TracingConfig) (context.Context, interface{}) {
	if !config.Enabled {
		return ctx, nil
	}

	switch config.ExporterType {
	case "jaeger":
		var span opentracing.Span
		if parent := opentracing.SpanFromContext(ctx); parent != nil {
			span = Tracer.StartSpan(name, opentracing.ChildOf(parent.Context()))
		} else {
			span = Tracer.StartSpan(name)
		}
		return opentracing.ContextWithSpan(ctx, span), span
	case "signoz":
		ctx, span := OtelTracer.Start(ctx, name)
		return ctx, span
	default:
		return ctx, nil
	}
}

// FinishSpan kết thúc một span
func FinishSpan(span interface{}) {
	if span == nil {
		return
	}

	switch s := span.(type) {
	case opentracing.Span:
		s.Finish()
	case trace.Span:
		s.End()
	}
}

// AddAttribute thêm thuộc tính vào span
func AddAttribute(span interface{}, key string, value interface{}) {
	if span == nil {
		return
	}

	switch s := span.(type) {
	case opentracing.Span:
		s.SetTag(key, value)
	case trace.Span:
		switch v := value.(type) {
		case string:
			s.SetAttributes(attribute.String(key, v))
		case int:
			s.SetAttributes(attribute.Int(key, v))
		case int64:
			s.SetAttributes(attribute.Int64(key, v))
		case float64:
			s.SetAttributes(attribute.Float64(key, v))
		case bool:
			s.SetAttributes(attribute.Bool(key, v))
		default:
			s.SetAttributes(attribute.String(key, fmt.Sprintf("%v", v)))
		}
	}
}

// RecordError ghi lại lỗi trong span
func RecordError(span interface{}, err error) {
	if span == nil || err == nil {
		return
	}

	switch s := span.(type) {
	case opentracing.Span:
		ext.Error.Set(s, true)
		s.SetTag("error.message", err.Error())
	case trace.Span:
		s.RecordError(err)
		s.SetStatus(codes.Error, err.Error())
	}
}

// ExtractSpanFromGinContext trích xuất span từ Gin context
func ExtractSpanFromGinContext(c *gin.Context, operationName string, config configs.TracingConfig) (context.Context, interface{}) {
	if !config.Enabled {
		return c.Request.Context(), nil
	}

	ctx := c.Request.Context()

	switch config.ExporterType {
	case "jaeger":
		spanCtx, _ := Tracer.Extract(
			opentracing.HTTPHeaders,
			opentracing.HTTPHeadersCarrier(c.Request.Header),
		)

		var span opentracing.Span
		if spanCtx != nil {
			span = Tracer.StartSpan(operationName, ext.RPCServerOption(spanCtx))
		} else {
			span = Tracer.StartSpan(operationName)
		}

		ext.HTTPMethod.Set(span, c.Request.Method)
		ext.HTTPUrl.Set(span, c.Request.URL.String())

		ctx = opentracing.ContextWithSpan(ctx, span)
		c.Set("span", span)

		return ctx, span

	case "signoz":
		ctx, span := OtelTracer.Start(ctx, operationName)
		span.SetAttributes(
			attribute.String("http.method", c.Request.Method),
			attribute.String("http.url", c.Request.URL.String()),
		)

		c.Set("span", span)
		return ctx, span

	default:
		return ctx, nil
	}
}

// AddUserInfoToSpan thêm thông tin người dùng vào span
func AddUserInfoToSpan(span interface{}, userID string, username string) {
	if span == nil {
		return
	}

	AddAttribute(span, "user.id", userID)
	AddAttribute(span, "user.name", username)
}

// AddRoleInfoToSpan thêm thông tin về vai trò vào span
func AddRoleInfoToSpan(span interface{}, roleID string, roleName string) {
	if span == nil {
		return
	}

	AddAttribute(span, "role.id", roleID)
	AddAttribute(span, "role.name", roleName)
}

// AddPermissionInfoToSpan thêm thông tin về quyền vào span
func AddPermissionInfoToSpan(span interface{}, permissionID string, permissionName string) {
	if span == nil {
		return
	}

	AddAttribute(span, "permission.id", permissionID)
	AddAttribute(span, "permission.name", permissionName)
}
