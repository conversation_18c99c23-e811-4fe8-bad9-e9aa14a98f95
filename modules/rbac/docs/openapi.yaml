openapi: 3.1.0
info:
  title: Role-Based Access Control (RBAC) API
  description: |
    API specification for the Role-Based Access Control module.
    This API enables complete management of permissions, roles, permission groups, and user role assignments.
  version: 1.0.0
  contact:
    name: Web New Team
    email: <EMAIL>
servers:
  - url: http://wn-api.local
    description: RBAC API endpoint
  - url: https://api.dev.webnew.com
    description: Development server
  - url: https://api.webnew.com
    description: Production server

tags:
  - name: Permissions
    description: Endpoints for managing permissions in the system
  - name: Roles
    description: Endpoints for managing roles and their associated permissions
  - name: UserRoles
    description: Endpoints for managing user role assignments and viewing user permissions
  - name: PermissionGroups
    description: Endpoints for managing permission groups to categorize permissions
  - name: System
    description: System-level endpoints for monitoring and health checks

paths:
  /api/v1/rbac/user-roles/assign:
    post:
      summary: Assign role to user
      description: |
        Assigns a role to a user.
        This is a legacy endpoint. Use /api/v1/rbac/users/{userId}/roles for new implementations.
      operationId: assignRoleToUser
      tags: [UserRoles]
      parameters:
        - $ref: '#/components/parameters/tenantIdParam'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - user_id
                - role_id
              properties:
                user_id:
                  type: integer
                  format: int64
                  description: ID of the user to assign the role to
                  example: 123
                role_id:
                  type: integer
                  format: int64
                  description: ID of the role to assign
                  example: 5
      responses:
        '200':
          description: Role assigned successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResponseSuccess'
        '400': { $ref: '#/components/responses/BadRequest' }
        '401': { $ref: '#/components/responses/Unauthorized' }
        '403': { $ref: '#/components/responses/Forbidden' }
        '404': { $ref: '#/components/responses/NotFound' }

  /api/v1/rbac/user-roles/users/{user_id}/roles/{role_id}:
    delete:
      summary: Revoke role from user
      description: |
        Revokes a specific role from a user.
        This is a legacy endpoint. Use DELETE /api/v1/rbac/users/{userId}/roles for new implementations.
      operationId: revokeRoleFromUser
      tags: [UserRoles]
      parameters:
        - $ref: '#/components/parameters/tenantIdParam'
        - name: user_id
          in: path
          required: true
          description: ID of the user
          schema:
            type: integer
            format: int64
        - name: role_id
          in: path
          required: true
          description: ID of the role to revoke
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: Role revoked successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResponseSuccess'
        '401': { $ref: '#/components/responses/Unauthorized' }
        '403': { $ref: '#/components/responses/Forbidden' }
        '404': { $ref: '#/components/responses/NotFound' }

  /api/v1/rbac/user-roles/users/{user_id}/roles:
    get:
      summary: Get user roles
      description: |
        Retrieves all roles assigned to a user.
        This is a legacy endpoint. Use /api/v1/rbac/users/{userId}/roles for new implementations.
      operationId: getUserRoles
      tags: [UserRoles]
      parameters:
        - $ref: '#/components/parameters/tenantIdParam'
        - name: user_id
          in: path
          required: true
          description: ID of the user
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: User roles retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    $ref: '#/components/schemas/ResponseStatus'
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Role'
        '401': { $ref: '#/components/responses/Unauthorized' }
        '403': { $ref: '#/components/responses/Forbidden' }
        '404': { $ref: '#/components/responses/NotFound' }

  /api/v1/rbac/user-roles/users/{user_id}/roles/{role_id}/check:
    get:
      summary: Check if user has role
      description: Checks if a user has a specific role assigned
      operationId: checkUserRole
      tags: [UserRoles]
      parameters:
        - $ref: '#/components/parameters/tenantIdParam'
        - name: user_id
          in: path
          required: true
          description: ID of the user
          schema:
            type: integer
            format: int64
        - name: role_id
          in: path
          required: true
          description: ID of the role to check
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: Role check completed successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    $ref: '#/components/schemas/ResponseStatus'
                  data:
                    type: object
                    properties:
                      has_role:
                        type: boolean
                        example: true
        '401': { $ref: '#/components/responses/Unauthorized' }
        '403': { $ref: '#/components/responses/Forbidden' }
        '404': { $ref: '#/components/responses/NotFound' }

  /api/v1/rbac/user-roles/roles/{role_id}/users:
    get:
      summary: Get users with role
      description: Retrieves all users that have a specific role assigned
      operationId: getUsersWithRole
      tags: [UserRoles]
      parameters:
        - $ref: '#/components/parameters/tenantIdParam'
        - name: role_id
          in: path
          required: true
          description: ID of the role
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: Users with role retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    $ref: '#/components/schemas/ResponseStatus'
                  data:
                    type: object
                    properties:
                      user_ids:
                        type: array
                        items:
                          type: integer
                          format: int64
                        example: [1, 5, 42, 123]
        '401': { $ref: '#/components/responses/Unauthorized' }
        '403': { $ref: '#/components/responses/Forbidden' }
        '404': { $ref: '#/components/responses/NotFound' }

  /api/v1/rbac/healthy:
    get:
      summary: Health check
      description: Simple health check endpoint to verify the RBAC module is running
      operationId: healthCheck
      tags: [System]
      responses:
        '200':
          description: RBAC module is healthy
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: "ok"
                  module:
                    type: string
                    example: "rbac"
  /api/v1/rbac/permission-groups:
    get:
      summary: List permission groups
      description: |
        Retrieves a paginated list of permission groups.
        Results can be filtered by tenant ID.
      operationId: listPermissionGroups
      tags: [PermissionGroups]
      parameters:
        - $ref: '#/components/parameters/tenantIdParam'
        - $ref: '#/components/parameters/cursorParam'
        - $ref: '#/components/parameters/limitParam'
        - name: tenant_id
          in: query
          description: Filter by tenant ID
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: Permission groups list retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResponsePermissionGroupList'
        '400': { $ref: '#/components/responses/BadRequest' }
        '401': { $ref: '#/components/responses/Unauthorized' }
        '403': { $ref: '#/components/responses/Forbidden' }
    post:
      summary: Create permission group
      description: |
        Creates a new permission group in the system.
        Requires administrative privileges.
      operationId: createPermissionGroup
      tags: [PermissionGroups]
      parameters:
        - $ref: '#/components/parameters/tenantIdParam'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PermissionGroupCreate'
      responses:
        '200':
          description: Permission group created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResponsePermissionGroup'
        '400': { $ref: '#/components/responses/BadRequest' }
        '401': { $ref: '#/components/responses/Unauthorized' }
        '403': { $ref: '#/components/responses/Forbidden' }
  /api/v1/rbac/permission-groups/{id}:
    get:
      summary: Get permission group details
      description: Retrieves details of a specific permission group
      operationId: getPermissionGroup
      tags: [PermissionGroups]
      parameters:
        - $ref: '#/components/parameters/tenantIdParam'
        - name: id
          in: path
          required: true
          description: ID of the permission group
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: Permission group details retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResponsePermissionGroup'
        '401': { $ref: '#/components/responses/Unauthorized' }
        '403': { $ref: '#/components/responses/Forbidden' }
        '404': { $ref: '#/components/responses/NotFound' }
    put:
      summary: Update permission group
      description: |
        Updates an existing permission group.
        Requires administrative privileges.
      operationId: updatePermissionGroup
      tags: [PermissionGroups]
      parameters:
        - $ref: '#/components/parameters/tenantIdParam'
        - name: id
          in: path
          required: true
          description: ID of the permission group
          schema:
            type: integer
            format: int64
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PermissionGroupUpdate'
      responses:
        '200':
          description: Permission group updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResponsePermissionGroup'
        '400': { $ref: '#/components/responses/BadRequest' }
        '401': { $ref: '#/components/responses/Unauthorized' }
        '403': { $ref: '#/components/responses/Forbidden' }
        '404': { $ref: '#/components/responses/NotFound' }
    delete:
      summary: Delete permission group
      description: |
        Deletes a permission group.
        Requires administrative privileges.
        Permission groups with assigned permissions cannot be deleted.
      operationId: deletePermissionGroup
      tags: [PermissionGroups]
      parameters:
        - $ref: '#/components/parameters/tenantIdParam'
        - name: id
          in: path
          required: true
          description: ID of the permission group
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: Permission group deleted successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResponseSuccess'
        '401': { $ref: '#/components/responses/Unauthorized' }
        '403': { $ref: '#/components/responses/Forbidden' }
        '404': { $ref: '#/components/responses/NotFound' }
        '409': { $ref: '#/components/responses/Conflict' }
  /api/v1/rbac/permissions:
    get:
      summary: List permissions
      description: |
        Retrieves a paginated list of permissions.
        Results can be filtered by resource type and searched by name or code.
      operationId: listPermissions
      tags:
        - Permissions
      parameters:
        - $ref: '#/components/parameters/tenantIdParam'
        - $ref: '#/components/parameters/cursorParam'
        - $ref: '#/components/parameters/limitParam'
        - name: resource_type
          in: query
          description: Filter by resource type
          schema:
            type: string
        - name: search
          in: query
          description: Search permissions by name or code
          schema:
            type: string
      responses:
        '200':
          description: Permissions list retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResponsePermissionList'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
    post:
      summary: Create permission
      description: |
        Creates a new permission in the system.
        Requires administrative privileges.
      operationId: createPermission
      tags:
        - Permissions
      parameters:
        - $ref: '#/components/parameters/tenantIdParam'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PermissionCreate'
      responses:
        '201':
          description: Permission created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResponsePermission'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '409':
          $ref: '#/components/responses/Conflict'

  /api/v1/rbac/permissions/{permissionId}:
    get:
      summary: Get permission details
      description: Retrieves details of a specific permission
      operationId: getPermission
      tags:
        - Permissions
      parameters:
        - $ref: '#/components/parameters/tenantIdParam'
        - $ref: '#/components/parameters/permissionIdParam'
      responses:
        '200':
          description: Permission details retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResponsePermission'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
    put:
      summary: Update permission
      description: |
        Updates an existing permission.
        Requires administrative privileges.
      operationId: updatePermission
      tags:
        - Permissions
      parameters:
        - $ref: '#/components/parameters/tenantIdParam'
        - $ref: '#/components/parameters/permissionIdParam'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PermissionUpdate'
      responses:
        '200':
          description: Permission updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResponsePermission'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
    delete:
      summary: Delete permission
      description: |
        Deletes a permission.
        Requires administrative privileges.
        System permissions cannot be deleted.
      operationId: deletePermission
      tags:
        - Permissions
      parameters:
        - $ref: '#/components/parameters/tenantIdParam'
        - $ref: '#/components/parameters/permissionIdParam'
      responses:
        '204':
          description: Permission deleted successfully
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
        '409':
          $ref: '#/components/responses/Conflict'

  /api/v1/rbac/roles:
    get:
      summary: List roles
      description: |
        Retrieves a paginated list of roles.
        Results can be filtered by system role status and searched by name or code.
      operationId: listRoles
      tags:
        - Roles
      parameters:
        - $ref: '#/components/parameters/tenantIdParam'
        - $ref: '#/components/parameters/cursorParam'
        - $ref: '#/components/parameters/limitParam'
        - name: is_system_role
          in: query
          description: Filter by system role status
          schema:
            type: boolean
        - name: search
          in: query
          description: Search roles by name or code
          schema:
            type: string
      responses:
        '200':
          description: Roles list retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResponseRoleList'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
    post:
      summary: Create role
      description: |
        Creates a new role in the system.
        Optionally assigns permissions to the new role.
      operationId: createRole
      tags:
        - Roles
      parameters:
        - $ref: '#/components/parameters/tenantIdParam'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RoleCreate'
      responses:
        '201':
          description: Role created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResponseRole'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '409':
          $ref: '#/components/responses/Conflict'

  /api/v1/rbac/roles/{roleId}:
    get:
      summary: Get role details
      description: Retrieves details of a specific role
      operationId: getRole
      tags:
        - Roles
      parameters:
        - $ref: '#/components/parameters/tenantIdParam'
        - $ref: '#/components/parameters/roleIdParam'
      responses:
        '200':
          description: Role details retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResponseRole'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
    put:
      summary: Update role
      description: |
        Updates an existing role.
        System roles have restrictions on what can be modified.
      operationId: updateRole
      tags:
        - Roles
      parameters:
        - $ref: '#/components/parameters/tenantIdParam'
        - $ref: '#/components/parameters/roleIdParam'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RoleUpdate'
      responses:
        '200':
          description: Role updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResponseRole'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
    delete:
      summary: Delete role
      description: |
        Deletes a role.
        System roles cannot be deleted.
        Roles with assigned users must be unassigned before deletion.
      operationId: deleteRole
      tags:
        - Roles
      parameters:
        - $ref: '#/components/parameters/tenantIdParam'
        - $ref: '#/components/parameters/roleIdParam'
      responses:
        '204':
          description: Role deleted successfully
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
        '409':
          $ref: '#/components/responses/Conflict'

  /api/v1/rbac/roles/{roleId}/permissions:
    get:
      summary: List role permissions
      description: Retrieves all permissions assigned to a role
      operationId: listRolePermissions
      tags:
        - Roles
      parameters:
        - $ref: '#/components/parameters/tenantIdParam'
        - $ref: '#/components/parameters/roleIdParam'
      responses:
        '200':
          description: Role permissions retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResponsePermissionList'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
    post:
      summary: Assign permissions to role
      description: |
        Assigns multiple permissions to a role.
        Existing permission assignments are preserved.
      operationId: assignRolePermissions
      tags:
        - Roles
      parameters:
        - $ref: '#/components/parameters/tenantIdParam'
        - $ref: '#/components/parameters/roleIdParam'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - permission_ids
              properties:
                permission_ids:
                  type: array
                  description: Array of permission IDs to assign to the role
                  items:
                    type: integer
                    format: int64
      responses:
        '200':
          description: Permissions assigned successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResponseSuccess'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
    delete:
      summary: Remove permissions from role
      description: Removes specified permissions from a role
      operationId: removeRolePermissions
      tags:
        - Roles
      parameters:
        - $ref: '#/components/parameters/tenantIdParam'
        - $ref: '#/components/parameters/roleIdParam'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - permission_ids
              properties:
                permission_ids:
                  type: array
                  description: Array of permission IDs to remove from the role
                  items:
                    type: integer
                    format: int64
      responses:
        '200':
          description: Permissions removed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResponseSuccess'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'

  /api/v1/rbac/users/{userId}/roles:
    get:
      summary: List user roles
      description: Retrieves all roles assigned to a user
      operationId: listUserRoles
      tags:
        - UserRoles
      parameters:
        - $ref: '#/components/parameters/tenantIdParam'
        - $ref: '#/components/parameters/userIdParam'
      responses:
        '200':
          description: User roles retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResponseRoleList'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
    post:
      summary: Assign roles to user
      description: |
        Assigns multiple roles to a user.
        Existing role assignments are preserved.
      operationId: assignUserRoles
      tags:
        - UserRoles
      parameters:
        - $ref: '#/components/parameters/tenantIdParam'
        - $ref: '#/components/parameters/userIdParam'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - role_ids
              properties:
                role_ids:
                  type: array
                  description: Array of role IDs to assign to the user
                  items:
                    type: integer
                    format: int64
      responses:
        '200':
          description: Roles assigned successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResponseSuccess'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
    delete:
      summary: Remove roles from user
      description: Removes specified roles from a user
      operationId: removeUserRoles
      tags:
        - UserRoles
      parameters:
        - $ref: '#/components/parameters/tenantIdParam'
        - $ref: '#/components/parameters/userIdParam'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - role_ids
              properties:
                role_ids:
                  type: array
                  description: Array of role IDs to remove from the user
                  items:
                    type: integer
                    format: int64
      responses:
        '200':
          description: Roles removed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResponseSuccess'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'

  /api/v1/rbac/users/{userId}/permissions:
    get:
      summary: List user permissions
      description: |
        Retrieves all permissions a user has through their assigned roles.
        This is the aggregate of all permissions from all roles.
      operationId: listUserPermissions
      tags:
        - UserRoles
      parameters:
        - $ref: '#/components/parameters/tenantIdParam'
        - $ref: '#/components/parameters/userIdParam'
      responses:
        '200':
          description: User permissions retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResponsePermissionList'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'

components:
  parameters:
    tenantIdParam:
      name: X-Tenant-ID
      in: header
      required: true
      description: ID of the tenant
      schema:
        type: integer
        format: int64
      example: 1
    permissionIdParam:
      name: permissionId
      in: path
      required: true
      description: ID of the permission
      schema:
        type: integer
        format: int64
      example: 42
    roleIdParam:
      name: roleId
      in: path
      required: true
      description: ID of the role
      schema:
        type: integer
        format: int64
      example: 5
    userIdParam:
      name: userId
      in: path
      required: true
      description: ID of the user
      schema:
        type: integer
        format: int64
      example: 123
    cursorParam:
      name: cursor
      in: query
      description: Cursor for pagination
      schema:
        type: string
      example: "dXNlcklkOjEwMA=="
    limitParam:
      name: limit
      in: query
      description: Number of items per page
      schema:
        type: integer
        default: 10
        minimum: 1
        maximum: 100
      example: 20

  responses:
    BadRequest:
      description: Bad Request - The request was invalid or cannot be served
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            status:
              code: 400
              message: "Invalid request parameters"
              success: false
              error_code: "INVALID_REQUEST"
              path: "/api/v1/rbac/permissions"
              timestamp: "2025-04-17T14:30:00Z"
              details:
                - field: "permission_code"
                  message: "Permission code is required"
    Unauthorized:
      description: Unauthorized - Authentication is required or has failed
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            status:
              code: 401
              message: "Authentication required"
              success: false
              error_code: "UNAUTHORIZED"
              path: "/api/v1/rbac/permissions"
              timestamp: "2025-04-17T14:30:00Z"
    Forbidden:
      description: Forbidden - User does not have permission to access the resource
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            status:
              code: 403
              message: "Insufficient permissions"
              success: false
              error_code: "FORBIDDEN"
              path: "/api/v1/rbac/roles/1/permissions"
              timestamp: "2025-04-17T14:30:00Z"
    NotFound:
      description: Not Found - The requested resource was not found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            status:
              code: 404
              message: "Resource not found"
              success: false
              error_code: "NOT_FOUND"
              path: "/api/v1/rbac/permissions/999"
              timestamp: "2025-04-17T14:30:00Z"
    Conflict:
      description: Conflict - Request could not be processed due to conflict
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            status:
              code: 409
              message: "Resource already exists"
              success: false
              error_code: "RESOURCE_CONFLICT"
              path: "/api/v1/rbac/permissions"
              timestamp: "2025-04-17T14:30:00Z"
              details:
                - field: "permission_code"
                  message: "Permission code 'user.create' already exists"

  schemas:
    ResponseSuccess:
      type: object
      properties:
        status:
          $ref: '#/components/schemas/ResponseStatus'

    PermissionGroup:
      type: object
      properties:
        group_id:
          type: integer
          format: int64
          example: 5
        tenant_id:
          type: integer
          format: int64
          nullable: true
          example: 1
        permission_group_name:
          type: string
          example: "User Management"
          description: "Name of the permission group"
        permission_group_description:
          type: string
          nullable: true
          example: "Permissions related to user management"
          description: "Optional description of the permission group"
        created_by:
          type: integer
          format: int64
          nullable: true
          example: 1
        created_at:
          type: string
          format: date-time
          example: "2025-01-15T08:30:00Z"
        updated_by:
          type: integer
          format: int64
          nullable: true
          example: 1
        updated_at:
          type: string
          format: date-time
          example: "2025-02-10T14:22:00Z"

    PermissionGroupCreate:
      type: object
      required:
        - permission_group_name
      properties:
        tenant_id:
          type: integer
          format: int64
          nullable: true
          example: 1
          description: "Optional tenant ID for the permission group"
        permission_group_name:
          type: string
          example: "Content Management"
          description: "Name of the permission group"
        permission_group_description:
          type: string
          nullable: true
          example: "Permissions related to content management"
          description: "Optional description of the permission group"

    PermissionGroupUpdate:
      type: object
      required:
        - permission_group_name
      properties:
        permission_group_name:
          type: string
          example: "Content Management"
          description: "Name of the permission group"
        permission_group_description:
          type: string
          nullable: true
          example: "Permissions related to content management"
          description: "Optional description of the permission group"

    ResponsePermissionGroup:
      type: object
      properties:
        status:
          $ref: '#/components/schemas/ResponseStatus'
        data:
          $ref: '#/components/schemas/PermissionGroup'

    ResponsePermissionGroupList:
      type: object
      properties:
        status:
          $ref: '#/components/schemas/ResponseStatus'
        data:
          type: array
          items:
            $ref: '#/components/schemas/PermissionGroup'
        meta:
          $ref: '#/components/schemas/PaginationMeta'

    AssignUserRoleRequest:
      type: object
      required:
        - user_id
        - role_id
      properties:
        user_id:
          type: integer
          format: int64
          description: ID of the user to assign the role to
          example: 123
        role_id:
          type: integer
          format: int64
          description: ID of the role to assign
          example: 5

    Permission:
      type: object
      properties:
        permission_id:
          type: integer
          format: int64
          example: 42
        permission_code:
          type: string
          example: "user.create"
          description: "Unique code for the permission"
        permission_name:
          type: string
          example: "Create User"
          description: "Human-readable name of the permission"
        description:
          type: string
          nullable: true
          example: "Allows creating new users in the system"
          description: "Optional description of what the permission allows"
        resource_type:
          type: string
          nullable: true
          example: "user"
          description: "Type of resource this permission applies to"
        created_at:
          type: string
          format: date-time
          example: "2025-01-15T08:30:00Z"
        updated_at:
          type: string
          format: date-time
          example: "2025-02-10T14:22:00Z"

    PermissionCreate:
      type: object
      required:
        - permission_code
        - permission_name
      properties:
        permission_code:
          type: string
          example: "product.delete"
          description: "Unique code for the permission"
        permission_name:
          type: string
          example: "Delete Product"
          description: "Human-readable name of the permission"
        description:
          type: string
          nullable: true
          example: "Allows deleting products from the catalog"
          description: "Optional description of what the permission allows"
        resource_type:
          type: string
          nullable: true
          example: "product"
          description: "Type of resource this permission applies to"

    PermissionUpdate:
      type: object
      properties:
        permission_name:
          type: string
          example: "Delete Product"
          description: "Human-readable name of the permission"
        description:
          type: string
          nullable: true
          example: "Allows deleting products from the catalog"
          description: "Optional description of what the permission allows"
        resource_type:
          type: string
          nullable: true
          example: "product"
          description: "Type of resource this permission applies to"

    Role:
      type: object
      properties:
        role_id:
          type: integer
          format: int64
          example: 5
        tenant_id:
          type: integer
          format: int64
          example: 1
        role_code:
          type: string
          example: "store_manager"
          description: "Unique code for the role"
        role_name:
          type: string
          example: "Store Manager"
          description: "Human-readable name of the role"
        description:
          type: string
          nullable: true
          example: "Manages store operations and inventory"
          description: "Optional description of the role's purpose"
        is_system_role:
          type: boolean
          example: false    
          description: "Whether this is a system-defined role with special privileges"
        created_at:
          type: string
          format: date-time
          example: "2025-01-15T08:30:00Z"
        updated_at:
          type: string
          format: date-time
          example: "2025-02-10T14:22:00Z"
        permission_count:
          type: integer
          example: 12
          description: "Number of permissions assigned to this role"

    RoleCreate:
      type: object
      required:
        - role_code
        - role_name
      properties:
        role_code:
          type: string
          example: "regional_manager"
          description: "Unique code for the role"
        role_name:
          type: string
          example: "Regional Manager"
          description: "Human-readable name of the role"
        description:
          type: string
          nullable: true
          example: "Manages multiple store locations in a region"
          description: "Optional description of the role's purpose"
        is_system_role:
          type: boolean
          default: false
          example: false
          description: "Whether this is a system-defined role with special privileges"
        permission_ids:
          type: array
          items:
            type: integer
            format: int64
          example: [1, 2, 3, 7, 42]
          description: "Optional list of permission IDs to assign to the role"

    RoleUpdate:
      type: object
      properties:
        role_name:
          type: string
          example: "Regional Sales Manager"
          description: "Human-readable name of the role"
        description:
          type: string
          nullable: true
          example: "Manages sales across multiple store locations in a region"
          description: "Optional description of the role's purpose"
        is_system_role:
          type: boolean
          example: false
          description: "Whether this is a system-defined role with special privileges"

    ResponsePermission:
      type: object
      properties:
        status:
          $ref: '#/components/schemas/ResponseStatus'
        data:
          $ref: '#/components/schemas/Permission'

    ResponsePermissionList:
      type: object
      properties:
        status:
          $ref: '#/components/schemas/ResponseStatus'
        data:
          type: array
          items:
            $ref: '#/components/schemas/Permission'
        meta:
          $ref: '#/components/schemas/PaginationMeta'

    ResponseRole:
      type: object
      properties:
        status:
          $ref: '#/components/schemas/ResponseStatus'
        data:
          $ref: '#/components/schemas/Role'

    ResponseRoleList:
      type: object
      properties:
        status:
          $ref: '#/components/schemas/ResponseStatus'
        data:
          type: array
          items:
            $ref: '#/components/schemas/Role'
        meta:
          $ref: '#/components/schemas/PaginationMeta'

    ResponseStatus:
      type: object
      properties:
        code:
          type: integer
          example: 200
          description: "HTTP status code"
        message:
          type: string
          example: "Operation completed successfully"
          description: "Human-readable status message"
        success:
          type: boolean
          example: true
          description: "Whether the operation was successful"
        error_code:
          type: string
          nullable: true
          example: null
          description: "Application-specific error code when success is false"
        path:
          type: string
          example: "/api/v1/rbac/permissions"
          description: "Request path"
        timestamp:
          type: string
          format: date-time
          example: "2025-04-17T14:30:00Z"
          description: "When the request was processed"
        details:
          type: array
          items:
            type: object
          nullable: true
          description: "Additional details about errors or warnings"

    PaginationMeta:
      type: object
      properties:
        next_cursor:
          type: string
          nullable: true
          example: "dXNlcklkOjIw"
          description: "Cursor for fetching the next page of results, null if last page"
        has_more:
          type: boolean
          example: true
          description: "Whether more results are available beyond this page"
        total_count:
          type: integer
          example: 42
          description: "Total number of items matching the query"

    Error:
      type: object
      properties:
        status:
          type: object
          properties:
            code:
              type: integer
              example: 400
              description: "HTTP status code"
            message:
              type: string
              example: "Bad Request"
              description: "Human-readable error message"
            success:
              type: boolean
              example: false
              description: "Always false for error responses"
            error_code:
              type: string
              example: "INVALID_REQUEST"
              description: "Application-specific error code"
            path:
              type: string
              example: "/api/v1/rbac/permissions"
              description: "Request path"
            timestamp:
              type: string
              format: date-time
              example: "2025-04-17T14:30:00Z"
              description: "When the error occurred"
            details:
              type: array
              items:
                type: object
                properties:
                  field:
                    type: string
                    example: "permission_code"
                  message:
                    type: string
                    example: "Permission code is required"
              nullable: true
              description: "Additional details about validation errors"