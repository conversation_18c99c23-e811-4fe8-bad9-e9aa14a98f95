package errors

import (
	"net/http"
)

// AppError định nghĩa cấu trúc lỗi của ứng dụng
type AppError struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Details interface{} `json:"details,omitempty"`
	err     error
}

// Error trả về message lỗi
func (e *AppError) Error() string {
	if e.err != nil {
		return e.Message + ": " + e.err.Error()
	}
	return e.Message
}

// Unwrap trả về lỗi gốc
func (e *AppError) Unwrap() error {
	return e.err
}

// StatusCode trả về HTTP status code
func (e *AppError) StatusCode() int {
	return e.Code
}

// WithDetails thêm chi tiết cho lỗi
func (e *AppError) WithDetails(details interface{}) *AppError {
	e.Details = details
	return e
}

// New tạo lỗi mới
func New(message string) *AppError {
	return &AppError{
		Code:    http.StatusInternalServerError,
		Message: message,
	}
}

// Wrap bọc lỗi gốc với message
func Wrap(err error, message string) *AppError {
	if err == nil {
		return nil
	}

	// Kiểm tra nếu lỗi đã là AppError
	if appErr, ok := err.(*AppError); ok {
		return &AppError{
			Code:    appErr.Code,
			Message: message,
			Details: appErr.Details,
			err:     appErr,
		}
	}

	return &AppError{
		Code:    http.StatusInternalServerError,
		Message: message,
		err:     err,
	}
}

// WithCode đặt mã lỗi HTTP
func (e *AppError) WithCode(code int) *AppError {
	e.Code = code
	return e
}

// BadRequest tạo lỗi 400 Bad Request
func BadRequest(message string) *AppError {
	return &AppError{
		Code:    http.StatusBadRequest,
		Message: message,
	}
}

// NotFound tạo lỗi 404 Not Found
func NotFound(message string) *AppError {
	return &AppError{
		Code:    http.StatusNotFound,
		Message: message,
	}
}

// Unauthorized tạo lỗi 401 Unauthorized
func Unauthorized(message string) *AppError {
	return &AppError{
		Code:    http.StatusUnauthorized,
		Message: message,
	}
}

// Forbidden tạo lỗi 403 Forbidden
func Forbidden(message string) *AppError {
	return &AppError{
		Code:    http.StatusForbidden,
		Message: message,
	}
}

// InternalServerError tạo lỗi 500 Internal Server Error
func InternalServerError(message string) *AppError {
	return &AppError{
		Code:    http.StatusInternalServerError,
		Message: message,
	}
}

// IsNotFound kiểm tra lỗi là NotFound
func IsNotFound(err error) bool {
	if appErr, ok := err.(*AppError); ok {
		return appErr.Code == http.StatusNotFound
	}
	return false
}

// IsBadRequest kiểm tra lỗi là BadRequest
func IsBadRequest(err error) bool {
	if appErr, ok := err.(*AppError); ok {
		return appErr.Code == http.StatusBadRequest
	}
	return false
}
