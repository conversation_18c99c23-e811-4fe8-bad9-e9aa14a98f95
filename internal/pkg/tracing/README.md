# OpenTelemetry Tracing Package

Package này cung cấp tích hợp OpenTelemetry cho ứng dụng wnapi, b<PERSON> gồm tracing cho HTTP requests, database operations và custom spans.

## Cài đặt Dependencies

```bash
go get go.opentelemetry.io/otel@v1.35.0
go get go.opentelemetry.io/otel/exporters/otlp/otlptrace@v1.35.0
go get go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracegrpc@v1.35.0
go get go.opentelemetry.io/otel/sdk@v1.35.0
go get go.opentelemetry.io/otel/trace@v1.35.0
go get google.golang.org/grpc
```

## Cấu hình

### Environment Variables

```env
# Tracing Configuration
TRACING_ENABLED=true
TRACING_SERVICE_NAME=wnapi
TRACING_SERVICE_VERSION=0.1.0
TRACING_ENVIRONMENT=development
TRACING_EXPORTER_TYPE=console  # console, otlp, jaeger

# OTLP Configuration (when TRACING_EXPORTER_TYPE=otlp)
TRACING_OTLP_ENDPOINT=http://localhost:4317
TRACING_OTLP_INSECURE=true
TRACING_OTLP_TIMEOUT=10s

# Jaeger Configuration (when TRACING_EXPORTER_TYPE=jaeger)
TRACING_JAEGER_ENDPOINT=http://localhost:14268/api/traces
TRACING_JAEGER_USERNAME=
TRACING_JAEGER_PASSWORD=

# Sampling Configuration
TRACING_SAMPLING_TYPE=ratio
TRACING_SAMPLING_RATIO=0.1
```

### Code Configuration

```go
config := &tracing.Config{
    ServiceName:    "wnapi",
    ServiceVersion: "0.1.0",
    Environment:    "development",
    Enabled:        true,
    ExporterType:   "console", // "console", "otlp", "jaeger"
    OTLP: tracing.OTLPConfig{
        Endpoint: "http://localhost:4317",
        Insecure: true,
        Timeout:  10 * time.Second,
    },
    Jaeger: tracing.JaegerConfig{
        Endpoint: "http://localhost:14268/api/traces",
        Username: "",
        Password: "",
    },
    Sampling: tracing.SamplingConfig{
        Type:  "ratio",
        Ratio: 0.1,
    },
}
```

## Khởi tạo Tracing

```go
package main

import (
    "context"
    "log"
    "time"
    
    "your-project/internal/pkg/tracing"
)

func main() {
    // Initialize tracing provider
    config := tracing.DefaultConfig()
    provider, err := tracing.NewProvider(config)
    if err != nil {
        log.Fatalf("Failed to create tracing provider: %v", err)
    }
    
    // Ensure proper shutdown
    defer func() {
        ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
        defer cancel()
        
        if err := provider.Shutdown(ctx); err != nil {
            log.Printf("Failed to shutdown tracing provider: %v", err)
        }
    }()
    
    // Your application code here...
}
```

## Sử dụng

### HTTP Middleware

```go
import (
    "net/http"
    "your-project/internal/pkg/tracing"
)

func setupServer() {
    mux := http.NewServeMux()
    
    // Add tracing middleware
    tracedMux := tracing.HTTPMiddleware("wnapi")(mux)
    
    mux.HandleFunc("/api/hello", helloHandler)
    
    server := &http.Server{
        Addr:    ":8080",
        Handler: tracedMux,
    }
    
    log.Fatal(server.ListenAndServe())
}
```

### Manual Spans

```go
func businessLogic(ctx context.Context) error {
    // Start a new span
    ctx, span := tracing.StartSpan(ctx, "my-service", "business_operation")
    defer span.End()
    
    // Add attributes
    tracing.AddSpanAttributes(ctx,
        tracing.UserID("123"),
        tracing.Module("business"),
    )
    
    // Add events
    tracing.AddSpanEvent(ctx, "processing_started")
    
    // Your business logic here...
    
    // Record errors
    if err != nil {
        tracing.RecordError(ctx, err)
        return err
    }
    
    return nil
}
```

### WithSpan Helper

```go
func processData(ctx context.Context) error {
    return tracing.WithSpan(ctx, "data-service", "process_data", func(ctx context.Context) error {
        // Add attributes
        tracing.AddSpanAttributes(ctx,
            tracing.Function("processData"),
            attribute.Int("data.size", 1000),
        )
        
        // Your processing logic here...
        
        return nil
    })
}
```

### Database Tracing

```go
func queryUsers(ctx context.Context) error {
    // Start database span
    ctx, span := tracing.DatabaseMiddleware(ctx, "SELECT", "SELECT * FROM users")
    defer span.End()
    
    // Add database-specific attributes
    tracing.AddSpanAttributes(ctx,
        tracing.DBName("wnapi"),
        attribute.String("db.table", "users"),
    )
    
    // Execute query...
    
    return nil
}
```

### Module Tracing

```go
func authenticateUser(ctx context.Context) error {
    // Start module span
    ctx, span := tracing.ModuleMiddleware(ctx, "auth", "authenticate")
    defer span.End()
    
    // Authentication logic...
    
    return nil
}
```

### HTTP Client Tracing

```go
func makeAPICall(ctx context.Context) error {
    // Create HTTP client with tracing
    client := tracing.HTTPClientMiddleware(&http.Client{
        Timeout: 30 * time.Second,
    })
    
    // Create request
    req, err := http.NewRequestWithContext(ctx, "GET", "https://api.example.com/data", nil)
    if err != nil {
        return err
    }
    
    // Make request (tracing automatic)
    resp, err := client.Do(req)
    if err != nil {
        return err
    }
    defer resp.Body.Close()
    
    return nil
}
```

## Utilities

### Getting Trace Information

```go
// Get trace ID and span ID
traceID := tracing.TraceID(ctx)
spanID := tracing.SpanID(ctx)
tracingInfo := tracing.TracingInfo(ctx) // "trace_id=xxx span_id=yyy"
```

### Common Attributes

```go
// HTTP attributes
tracing.HTTPMethod("GET")
tracing.HTTPURL("https://example.com")
tracing.HTTPStatusCode(200)
tracing.HTTPUserAgent("Go-Client/1.0")

// Database attributes
tracing.DBSystem("mysql")
tracing.DBName("wnapi")
tracing.DBStatement("SELECT * FROM users")
tracing.DBOperation("SELECT")

// Service attributes
tracing.ServiceName("wnapi")
tracing.ServiceVersion("0.1.0")

// Custom attributes
tracing.UserID("user-123")
tracing.RequestID("req-456")
tracing.Module("auth")
tracing.Function("authenticate")
```

## Best Practices

### 1. Naming Conventions

- **Service names**: Use kebab-case (e.g., `user-service`, `auth-service`)
- **Span names**: Use descriptive names with operation (e.g., `authenticate_user`, `fetch_user_data`)
- **Attribute keys**: Use snake_case (e.g., `user.id`, `http.status_code`)

### 2. Span Hierarchy

```go
// Root span (HTTP request)
GET /api/users/123
├── validate_token (auth module)
├── get_user_data (user module)
│   └── SELECT users (database)
└── format_response (response module)
```

### 3. Error Handling

```go
ctx, span := tracing.StartSpan(ctx, "service", "operation")
defer span.End()

if err != nil {
    // Record error and set status
    tracing.RecordError(ctx, err)
    return err
}

// Set success status
tracing.SetSpanStatus(ctx, codes.Ok, "Operation completed successfully")
```

### 4. Sampling

- **Development**: 100% sampling (`ratio: 1.0`)
- **Staging**: 50% sampling (`ratio: 0.5`)
- **Production**: 1-10% sampling (`ratio: 0.01-0.1`)

### 5. Attributes vs Events

- **Attributes**: Static properties (user_id, operation_type, status_code)
- **Events**: Time-based occurrences (validation_started, cache_hit, error_occurred)

## Observability Stack

### Jaeger (Development)

```bash
# Run Jaeger all-in-one
docker run -d --name jaeger \
  -p 16686:16686 \
  -p 14250:14250 \
  jaegertracing/all-in-one:latest
```

### OpenTelemetry Collector

```yaml
# otel-collector.yaml
receivers:
  otlp:
    protocols:
      grpc:
        endpoint: 0.0.0.0:4317
      http:
        endpoint: 0.0.0.0:4318

exporters:
  jaeger:
    endpoint: http://jaeger:14250
    tls:
      insecure: true

service:
  pipelines:
    traces:
      receivers: [otlp]
      exporters: [jaeger]
```

## Monitoring và Debug

### 1. Trace ID trong Logs

```go
// Add trace ID to log entries
logger := logger.WithField("trace_id", tracing.TraceID(ctx))
logger.Info("Processing request")
```

### 2. Health Check

```go
func healthCheck(provider *tracing.Provider) error {
    ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
    defer cancel()
    
    return provider.ForceFlush(ctx)
}
```

### 3. Metrics

```go
// Export tracing metrics
span.SetAttributes(
    attribute.Float64("operation.duration_ms", float64(duration.Milliseconds())),
    attribute.Int("operation.items_processed", count),
)
```

## Troubleshooting

### Common Issues

1. **No traces visible**: Check OTLP endpoint connection
2. **Missing spans**: Verify context propagation
3. **High overhead**: Reduce sampling ratio
4. **Connection errors**: Check collector configuration

### Debug Mode

```go
config := &tracing.Config{
    // ... other config
    Sampling: tracing.SamplingConfig{
        Type:  "always", // Sample all traces for debugging
        Ratio: 1.0,
    },
}
```

## Performance Considerations

- **Sampling**: Use appropriate sampling rates for production
- **Attributes**: Limit number of attributes per span (< 50)
- **Batch export**: Configure appropriate batch sizes
- **Resource limits**: Monitor memory usage of spans

## Tích hợp với Modules

Để tích hợp tracing vào modules hiện tại:

1. **Auth Module**:
```go
func (s *AuthService) Authenticate(ctx context.Context, token string) error {
    return tracing.WithSpan(ctx, "auth", "authenticate", func(ctx context.Context) error {
        // Authentication logic
        return nil
    })
}
```

2. **Hello Module**:
```go
func (h *HelloHandler) Handle(ctx context.Context) error {
    tracing.AddSpanAttributes(ctx, tracing.Module("hello"))
    // Handler logic
    return nil
}
```
