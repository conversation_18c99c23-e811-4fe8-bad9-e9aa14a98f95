package response

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

type Status struct {
	Code      int         `json:"code"`
	Message   string      `json:"message"`
	Success   bool        `json:"success"`
	ErrorCode string      `json:"error_code"`
	Path      string      `json:"path"`
	Timestamp string      `json:"timestamp"`
	Details   interface{} `json:"details"`
}

type Meta struct {
	NextCursor string `json:"next_cursor,omitempty"`
	Has<PERSON>ore    bool   `json:"has_more,omitempty"`
}

type Response struct {
	Status Status      `json:"status"`
	Data   interface{} `json:"data"`
	Meta   *Meta       `json:"meta,omitempty"`
}

type Detail struct {
	Field   string `json:"field,omitempty"`
	Message string `json:"message"`
}

func Success(c *gin.Context, data interface{}, meta *Meta) {
	resp := Response{
		Status: Status{
			Code:      http.StatusOK,
			Message:   "Operation completed successfully",
			Success:   true,
			ErrorCode: "",
			Path:      c.Request.URL.Path,
			Timestamp: time.Now().Format(time.RFC3339),
			Details:   nil,
		},
		Data: data,
		Meta: meta,
	}
	c.JSON(http.StatusOK, resp)
}

func Error(c *gin.Context, code int, message, errorCode string) {
	ErrorWithDetails(c, code, message, errorCode, nil)
}

func BadRequest(c *gin.Context, message, errorCode string, details interface{}) {
	ErrorWithDetails(c, http.StatusBadRequest, message, errorCode, details)
}

func Unauthorized(c *gin.Context, message string) {
	Error(c, http.StatusUnauthorized, message, "UNAUTHORIZED")
}

func Forbidden(c *gin.Context, message string) {
	Error(c, http.StatusForbidden, message, "FORBIDDEN")
}

func NotFound(c *gin.Context, message string) {
	Error(c, http.StatusNotFound, message, "NOT_FOUND")
}

func InternalServerError(c *gin.Context, message string) {
	Error(c, http.StatusInternalServerError, message, "INTERNAL_SERVER_ERROR")
}

func ValidationError(c *gin.Context, details []Detail) {
	ErrorWithDetails(c, http.StatusBadRequest, "Validation failed", "VALIDATION_ERROR", details)
}

func ErrorWithDetails(c *gin.Context, code int, message, errorCode string, details interface{}) {
	resp := Response{
		Status: Status{
			Code:      code,
			Message:   message,
			Success:   false,
			ErrorCode: errorCode,
			Path:      c.Request.URL.Path,
			Timestamp: time.Now().Format(time.RFC3339),
			Details:   details,
		},
		Data: nil,
		Meta: nil,
	}
	c.JSON(code, resp)
}
