package response

const (
	ErrorCodeInvalidTenantData  = "INVALID_TENANT_DATA"
	ErrorCodeEmailAlreadyExists = "EMAIL_ALREADY_EXISTS"

	// Authentication errors
	ErrorCodeInvalidCredentials = "INVALID_CREDENTIALS"
	ErrorCodeTokenExpired       = "TOKEN_EXPIRED"
	ErrorCodeInvalidToken       = "INVALID_TOKEN"

	// Resource errors
	ErrorCodeResourceNotFound      = "RESOURCE_NOT_FOUND"
	ErrorCodeResourceAlreadyExists = "RESOURCE_ALREADY_EXISTS"
	ErrorCodeResourceAccessDenied  = "RESOURCE_ACCESS_DENIED"

	// Input validation errors
	ErrorCodeInvalidInput         = "INVALID_INPUT"
	ErrorCodeMissingRequiredField = "MISSING_REQUIRED_FIELD"

	// Server errors
	ErrorCodeInternalError = "INTERNAL_ERROR"
	ErrorCodeDatabaseError = "DATABASE_ERROR"

	// Rate limiting
	ErrorCodeTooManyRequests = "TOO_MANY_REQUESTS"
)
