app:
  name: "sample-project"
  version: "0.1.0"
  env: "development"

server:
  host: "0.0.0.0"
  port: 8080
  timeout: 30s
  read_timeout: 15s
  write_timeout: 15s
  max_header_bytes: 1048576

# Import c<PERSON><PERSON> hình từ file khác
imports:
  - ./modules.yaml

# Danh sách modules được kích hoạt
modules:
  - hello
  - auth

# <PERSON><PERSON>u hình cho từng module
module_settings:
  hello:
    message: "Xin chào từ Sample Project!"
  auth:
    jwt_secret: "sample_project_jwt_secret_key"
    access_token_expiry: "30m"  # 30 phút
    refresh_token_expiry: "168h"  # 7 ngày

# Cấu hình plugins
plugins:
  logger:
    enabled: true
    config:
      level: "debug"
      file: "logs/sample.log"
      rotation_size: 10485760  # 10MB
      rotation_count: 5

database:
  type: mysql
  host: localhost
  port: 3307
  username: root
  password: root
  database: wnapi
  max_open_conns: 20
  max_idle_conns: 10
  conn_max_lifetime: 3600s
  migration_path: ./migrations

log_level: "debug" 