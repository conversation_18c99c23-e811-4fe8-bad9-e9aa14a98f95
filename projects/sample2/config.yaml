app:
  name: "sample2-project"
  version: "0.1.0"
  env: "development"

server:
  host: "0.0.0.0"
  port: 8081  # Port khác với sample
  timeout: 30s
  read_timeout: 15s
  write_timeout: 15s
  max_header_bytes: 1048576

# Import c<PERSON><PERSON> hình từ file khác
imports:
  - ./modules.yaml

# Danh sách modules được kích hoạt
modules:
  - hello

# C<PERSON>u hình cho từng module
module_settings:
  hello:
    message: "Xin chào từ Sample Project 2!"

# Cấu hình plugins
plugins:
  logger:
    enabled: true
    config:
      level: "info"
      file: "logs/sample2.log"
      rotation_size: 5242880  # 5MB
      rotation_count: 3

database:
  type: mysql
  host: localhost
  port: 3307
  username: root
  password: root
  database: wnapi
  max_open_conns: 20
  max_idle_conns: 10
  conn_max_lifetime: 3600s
  migration_path: ./migrations

log_level: "debug" 